TwiML™ Voice: <ConversationRelay>
The <ConversationRelay> TwiML noun under the <Connect> verb routes a call to <PERSON><PERSON><PERSON>'s ConversationRelay service, providing advanced AI-powered voice interactions. ConversationRelay handles the complexities of live, synchronous voice calls, such as Speech-to-Text (STT) and Text-to-Speech (TTS) conversions, session management, and low-latency communication with your application. This approach allows your system to focus on processing conversational AI logic and sending back responses effectively.
In a typical setup, <ConversationRelay> connects to your AI application through a WebSocket, allowing real-time and event-based interaction. Your application receives transcribed caller speech in structured messages and sends responses as text, which ConversationRelay converts to speech and plays back to the caller. This setup is commonly used for customer service, virtual assistants, and other scenarios that require real-time, AI-based voice interactions.
Basic usage
Before you can use <ConversationRelay>, make sure you've completed the onboarding steps and configured your Twilio account accordingly.
WebSocket security
To ensure the secure operation of <ConversationRelay>, your WebSocket server must validate incoming requests using the Twilio signature. For detailed guidance on setting up signature validation, see Configure your WebSocket server.
Generating TwiML for <ConversationRelay>
Connect a Programmable Voice call to <PERSON><PERSON><PERSON>'s ConversationRelay service.
Node.js
Python
C#
Java
PHP
Ruby
Report code block
Copy code block
1
from twilio.twiml.voice_response import Connect, ConversationRelay, VoiceResponse
2

3
response = VoiceResponse()
4
connect = Connect(action='https://myhttpserver.com/connect_action')
5
connect.conversation_relay(
6
    url='wss://mywebsocketserver.com/websocket',
7
    welcome_greeting='Hi! Ask me anything!')
8
response.append(connect)
9

10
print(response)
Output
Copy output
1
<?xml version="1.0" encoding="UTF-8"?>
2
<Response>
3
  <Connect action="https://myhttpserver.com/connect_action">
4
    <ConversationRelay url="wss://mywebsocketserver.com/websocket" welcomeGreeting="Hi! Ask me anything!" />
5
  </Connect>
6
</Response>
● 
action (optional): The URL that Twilio will request when the <Connect> verb ends.
● url (required): The URL of your WebSocket server (must use the wss:// protocol).
● welcomeGreeting (optional): The message automatically played to the caller after we answer the call and establish the WebSocket connection.
When the TwiML execution is complete, Twilio will make a callback to the action URL with call information and the return parameters from ConversationRelay.
Agent observability with Conversational Intelligence
ConversationRelay integrates with Conversational Intelligence for native virtual agent observability and performance monitoring. To enable this feature, you need to set the intelligenceService attribute in the <ConversationRelay> noun as documented below.
<ConversationRelay> attributes
The <ConversationRelay> noun supports the following attributes:
Attribute name
Description
Default value
Required
url
The URL to your WebSocket server (must use wss://).

Required
welcomeGreeting
The message automatically played to the caller after we answer the call and establish the WebSocket connection.

Optional
welcomeGreetingInterruptible
Specifies if the caller can interrupt the welcomeGreeting with speech. Values can be "none", "dtmf", "speech", or "any". For backward compatibility, Boolean values are also accepted: true = "any" and false = "none".
"any"
Optional
language
The language code (for example, "en-US") that applies to both Speech-to-Text (STT) and Text-to-Speech (TTS). Setting this attribute is equivalent to setting both ttsLanguage and transcriptionLanguage.
"en-US"
Optional
ttsLanguage
The default language code to use for TTS when the text token message doesn't specify a language. If you set both attributes, this one overrides the language attribute. You can modify this via the ttsLanguage field in the language message you send through the Service Provider Interface (SPI).

Optional
ttsProvider
The provider for TTS. Available choices are "Google", "Amazon", and "ElevenLabs".
"ElevenLabs"
Optional
voice
The voice used for TTS. Options vary based on the ttsProvider. For details, refer to the Twilio TTS Voices. We list additional voices available for ConversationRelay below.
"UgBBYS2sOqTuMpoF3BR0" (ElevenLabs), "en-US-Journey-O" (Google), "Joanna-Neural" (Amazon)
Optional
transcriptionLanguage
The language code to use for STT when the session starts. If you set both attributes, this one overrides the language attribute for the transcription language. You can modify this via the transcriptionLanguage field in the language message you send through the SPI.

Optional
transcriptionProvider
The provider for STT (Speech Recognition). Available choices are "Google" and "Deepgram".
"Google"
Optional
speechModel
The speech model used for STT. Choices vary based on the transcriptionProvider. Refer to the provider's documentation for an accurate list.
"telephony" (Google), "nova-2-general" (Deepgram)
Optional
interruptible
Specifies if caller speech can interrupt TTS playback. Values can be "none", "dtmf", "speech", or "any". For backward compatibility, Boolean values are also accepted: true = "any" and false = "none".
"any"
Optional
dtmfDetection
Specifies whether the system sends Dual-tone multi-frequency (DTMF) keypresses over the WebSocket. Set to true to turn on DTMF events.

Optional
reportInputDuringAgentSpeech
Specifies whether your application receives prompts and DTMF events while the agent is speaking. Values can be "none", "dtmf", "speech", or "any". Note: The default value for this attribute has changed. The default was "any" before May, 2025 and it's now "none".
"none"
Optional
preemptible
Specifies if the TTS of the current talk cycle can allow text tokens from the subsequent talk cycle to interrupt.
false
Optional
hints
A comma-separated list of words or phrases that helps Speech-to-Text recognition for uncommon words, product names, or domain-specific terminology. Works similarly to the hints attribute in <Gather>.

Optional
debug
A space-separated list of options that you can use to subscribe to debugging messages. Options are debugging, speaker-events, and tokens-played. The debugging option provides general debugging information. speaker-events will notify your application about agentSpeaking and clientSpeaking events. tokens-played will provide messages about what's just been played over TTS.

Optional
elevenlabsTextNormalization
Specifies whether or not to apply text normalization while using the ElevenLabs TTS provider. Options are "on", "auto", or "off". "auto" has the same effect as "off" for ConversationRelay voice calls.
"off"
Optional
intelligenceService
A Conversational Intelligence Service SID or unique name for persisting conversation transcripts and running Language Operators for virtual agent observability. Please see this guide for more details.

Optional
Additional TTS voices available for ConversationRelay
Available ElevenLabs voices
We offer TTS provider support for ElevenLabs, which provides additional natural-sounding voice synthesis. Use the interface below to search and filter through a wide selection of voices by language, accent, age, and more. Each voice entry includes a voiceID that you can copy and paste into your <ConversationRelay> configuration.
How to Use ElevenLabs Voices
1. Search or Filter: Use the tool below to locate a voice that matches your requirements (for example, language, accent, category, age, gender, tag).
2. Copy the voiceID: From the search results, copy the unique identifier (for example, NYC9WEgkq1u4jiqBseQ9).
3. Configure <ConversationRelay>: In your TwiML, set ttsProvider="ElevenLabs" and use the copied voiceID in the voice attribute.
Example:
Copy code block
1
<Connect>
2
  <ConversationRelay url="wss://example.com/websocket" ttsProvider="ElevenLabs" voice="NYC9WEgkq1u4jiqBseQ9" ... />
3
</Connect>

Best practices
● Streaming Text Tokens: For smoother TTS playback, stream text tokens incrementally and set "last": true when the message is complete.
● Error Handling: Monitor for error messages sent over the SPI to handle any issues promptly.
● Language Switching: Use the language message to switch languages dynamically during a session.
● Session Management: Use the end message to gracefully end sessions when your application logic determines it's appropriate.
● Don't "wait" to send text tokens back from the Large Language Model (LLM); send them as you receive them.
● To achieve the lowest latency, use the partial completions (streaming) through the LLM APIs. That way, the system sends each word in separate text tokens. The last one of that has last=true. This enables ConversationRelay to identify the first sayable string.
● Don't trim LLM tokens; the tokens need to have spaces between them.
Handling punctuation and last: true in ConversationRelay TTS
1. Setting the last flag properly
▪ When sending text tokens that include punctuation, ensure the final token in the message includes "last": true.
▪ Without a final token marked with "last": true, ConversationRelay assumes additional tokens are forthcoming and may stop reading at the first punctuation mark (for example, period, comma, or question mark).
2. Using partial completions for streaming
▪ In streaming mode, send each text token incrementally with "last": false.
▪ When the LLM indicates that the response is complete (for example, when response.finish_reason() equals "stop"), mark that final token with "last": true.
▪ Example:
▪ Copy code block
▪ 1
▪ { "type": "text", "token": "Hello", "last": false }
▪ 2
▪ { "type": "text", "token": " world", "last": false }
▪ 3
▪ { "type": "text", "token": "!", "last": true }
3. Handling complete responses (non-streaming)
▪ In non-streaming mode, when the entire response is generated as a single complete sentence, mark the token with "last": true.
▪ Example:
▪ Copy code block
▪ { "type": "text", "token": "Hello world!", "last": true }
4. Punctuation handling in longer messages
▪ For messages with complex punctuation, consider breaking the response into smaller chunks.
▪ Each chunk should have the appropriate punctuation, with only the final token of the overall message marked with "last": true.
By following these guidelines, you can ensure that ConversationRelay processes and speaks your full message smoothly without unexpected pauses or truncation.
Prompt Engineering for voice responses in ConversationRelay
When setting up system prompts for Large Language Models (LLMs) in ConversationRelay, consider these best practices to ensure optimal performance with Text-to-Speech (TTS) in ConversationRelay:
● Explicit Number Formatting: Encourage the LLM to spell out numbers (for example, "two" instead of "2") to avoid misinterpretation by TTS.
● Avoid Special Characters: Avoid bullet points, asterisks, or special symbols, as these can cause pauses or mispronunciations in voice output.
● Focus on Conversational Tone: Design prompts to produce conversational, naturally flowing responses, which translate more effectively to TTS.
These prompt adjustments help improve the LLM-generated tokens' compatibility with voice output in ConversationRelay, enhancing clarity and consistency for users.
WebSocket message compliance for ConversationRelay
All WebSocket messages from ConversationRelay to your API follow the strict formats defined in these docs. Your application must also adhere to these specifications when sending messages back to ConversationRelay.
● Use One-Way Communication: Managed API services like AWS API Gateway often support two-way communication, but this approach doesn't work with ConversationRelay. Two-way communication may cause your application to send back non-conforming messages, causing us to terminate the session.
● Explicit Message Handling: Use one-way communication only, ensuring precise control over what and when you send messages to ConversationRelay.
Following these practices helps maintain session stability and ensures compatibility with ConversationRelay's message handling.
Text normalization best practices
When working with Text-to-Speech (TTS) in ConversationRelay, proper text normalization is important for delivering clear and natural spoken responses. This is especially important when using ElevenLabs voices, which may have difficulty with certain formats. Consider the following guidelines:
● Numbers and Units: Write numbers as words for improved pronunciation. For example, write "twenty dollars and fifty cents" instead of "$20.50".
● Dates: Spell out dates completely (for example, "March twenty-eighth, two thousand twenty-five" instead of "03/28/2025").
● Email Addresses: Replace or spell out special characters. For instance, write "user at example dot com" rather than "<EMAIL>" since the "@" sign may be mispronounced.
● Names: Use consistent formatting for names throughout the call to avoid variations (for example, always use "Anna" rather than alternating between "A-nna" and "Ah-nna").
● Abbreviations: Spell out abbreviations that should be pronounced fully (for example, "Doctor" instead of "Dr.").
● Special Characters: Replace special characters with their spoken equivalents (for example, "percent" instead of "%").
● Punctuation: Use appropriate punctuation to create natural pauses and intonation.
● Acronyms and Initialisms: Insert spaces between letters if they must be spelled out (for example, "H T T P" for letter-by-letter pronunciation).
If you use the ElevenLabs TTS provider then you can also use the elevenlabsTextNormalization TwiML attribute to help with text normalization. Setting this attribute to on will tell ElevenLabs to take an extra step to normalize text for you. Set the attribute to off for more control over the text normalization process and lower latency.
