"""
Django settings for Content Spark project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from pathlib import Path
from django.contrib.messages import constants as messages
from . json_logger import CustomJsonFormatter

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-li023_kseg+sl73j%q$91qvc_v#(&pk7#r$xqx^yff9t1l6q$2"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# ALLOWED_HOSTS = ['*']
ALLOWED_HOSTS = [
    "localhost",
    "127.0.0.1",
    "**************",
    "************",
    "siloenterprise.ai",
    "www.siloenterprise.ai",
    "silo.intelliclabs.com"
]

CSRF_TRUSTED_ORIGINS = [
    "http://localhost",
    "http://127.0.0.1",
    "https://**************",
    "http://**************",
    "https://************",
    "http://************",
    "http://silo.intelliclabs.com",
    "https://silo.intelliclabs.com",
    "http://siloenterprise.ai",
    "http://www.siloenterprise.ai",
    "https://siloenterprise.ai",
    "https://www.siloenterprise.ai"
]

# Application definition
INSTALLED_APPS = [
    "jazzmin",
    "import_export",
    "rest_framework",
    "core",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    'django.contrib.humanize',
    "corsheaders",
    "channels",
    "apps.authentication",
    "apps.home",
    "apps.system",
    "apps.chat",
    "apps.leads",
    "apps.phone",
    "apps.email",
    "apps.emailer",
    "apps.email_client",
    "apps.email_warmup",
    "apps.email_bulk",
    "apps.restaurant",
    "apps.voicebot",
    "apps.zoombot",
]

MIDDLEWARE = [
    "crum.CurrentRequestUserMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "csp.middleware.CSPMiddleware",
    "corsheaders.middleware.CorsMiddleware",
]

ROOT_URLCONF = "core.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / 'templates']
        ,
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "core.context_processors.load_global_variables"
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"
# ASGI_APPLICATION = "core.routing.application"
ASGI_APPLICATION = "core.asgi.application"


# Channel layers for Django Channels
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer",
    },
}


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = 'America/Los_Angeles'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/
STATIC_ROOT = '/www/static'
STATIC_URL = "static/"

STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static/'),
)

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = 'authentication.User'

JAZZMIN_SETTINGS = {
    # title of the window (Will default to current_admin_site.site_title if absent or None)
    "site_title": "Silo Enterprise AI",

    # Title on the login screen (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_header": "Silo Enterprise AI",

    # Title on the brand (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_brand": "Silo Enterprise AI",

    # Logo to use for your site, must be present in static files, used for brand on top left
    "site_logo": "/assets/favicon.png",

    # CSS classes that are applied to the logo above
    "site_logo_classes": "img-circle",

    # Relative path to a favicon for your site, will default to site_logo if absent (ideally 32x32 px)
    "site_icon": "assets/favicon.ico",

    # Welcome text on the login screen
    "welcome_sign": "Welcome to Silo Enterprise AI",

    # Copyright on the footer
    "copyright": "Operation Silo",

    # The model admin to search from the search bar, search bar omitted if excluded
    # "search_model": "apps.quote",

    # Field name on user model that contains avatar ImageField/URLField/Charfield or a callable that receives the user
    "user_avatar": None,

    ############
    # Top Menu #
    ############
    # Links to put along the top menu
    "topmenu_links": [

        # Url that gets reversed (Permissions can be added)
        {"name": "Home", "url": "admin:index", "permissions": ["auth.view_user"]},

        # external url that opens in a new window (Permissions can be added)
        {"name": "Support", "url": "https://siloenterprise.ai/", "new_window": True},

        # model admin to link to (Permissions checked against model)
        {"model": "auth.User"},

    ],

    #############
    # Side Menu #
    #############

    # Whether to display the side menu
    "show_sidebar": True,

    # Whether to aut expand the menu
    "navigation_expanded": True,

    # Custom icons for side menu apps/models See https://fontawesome.com/icons?d=gallery&m=free&v=5.0.0,5.0.1,5.0.10,5.0.11,5.0.12,5.0.13,5.0.2,5.0.3,5.0.4,5.0.5,5.0.6,5.0.7,5.0.8,5.0.9,5.1.0,5.1.1,5.2.0,5.3.0,5.3.1,5.4.0,5.4.1,5.4.2,5.13.0,5.12.0,5.11.2,5.11.1,5.10.0,5.9.0,5.8.2,5.8.1,5.7.2,5.7.1,5.7.0,5.6.3,5.5.0,5.4.2
    # for the full list of 5.13.0 free icon classes
    "icons": {
        "auth": "fas fa-users-cog",
        "auth.user": "fas fa-user",
        "auth.Group": "fas fa-users",
        "art.CreatedArt": "fas fa-paint-brush",
        "phone.PhoneLead": "fas fa-phone",
        "authentication.User": "fas fa-user",
        "authentication.UserType": "fas fa-users",
        "authentication.SubscriptionType": "fas fa-money-bill-wave-alt",
        "authentication.PurchaseType": "fas fa-money-bill-wave-alt",
        "system.EmailOutbox": "fas fa-envelope",
        "system.EmailSent": "fas fa-envelope-open",
        "system.Setting": "fas fa-cogs",
        "system.Payment": "fas fa-money-bill-wave-alt",
        "system.Payout": "fas fa-hand-holding-usd",
        "system.SubscriptionPayment": "fas fa-money-bill-wave-alt",
        "system.PurchasePayment": "fas fa-money-bill-wave-alt",
        "system.CreditUse": "fas fa-plus-square",
        "system.FAQ": "fas fa-question",
        "system.SiteLead": "fas fa-address-card",
        "system.SupportTour": "fas fa-hands-helping",
        "system.Proxy": "fas fa-globe",
        "chat.Chat": "fas fa-comment",
        "chat.ChatAvatar": "fas fa-user",
        "chat.ChatMessage": "fas fa-comments",
        "chat.ChatBot": "fas fa-robot",
        "chat.ChatBotLead": "fas fa-address-card",
        "chat.ChatBotQuestion": "fas fa-question-circle",
        "chat.ChatTemplate": "fab fa-codepen",
        "chat.ChatBotContact": "fas fa-address-book",
        "chat.ChatBotContactMessage": "fas fa-comments",
        "chat.ChatBotSequence": "fas fa-list-ol",
        "chat.ChatBotFile": "fas fa-file-alt",
        "chat.ChatBotSpend": "fas fa-dollar-sign",
        "chat.ChatBotValue": "fas fa-equals",
        "create.ContentTemplate": "fab fa-codepen",
        "create.ContentFolder": "fas fa-folder-open",
        "create.ContentDocument": "fas fa-file-alt",
        "create.ContentSpin": "fas fa-sync-alt",
        "site.PostImage": "fas fa-image",
        "site.Token": "fas fa-th",
        "site.WordPressPost": "fas fa-clone",
        "site.WordPressSite": "fab fa-wordpress-simple",
        "site.WordPressTask": "fas fa-cogs",
        "article.Article": "fas fa-newspaper",
        "leads.Lead": "fas fa-users",
        "leads.LeadCategory": "fas fa-th-list",
        "leads.LeadUpload": "fas fa-cloud-upload-alt",
        "leads.ScrapeJob": "fas fa-globe",
        "leads.ScrapeJobCategory": "fas fa-th-list",

    },
    # Icons that are used when one is not manually specified
    "default_icon_parents": "fas fa-chevron-circle-right",
    "default_icon_children": "fas fa-circle",

    #############
    # UI Tweaks #
    #############
    # Relative paths to custom CSS/JS scripts (must be present in static files)
    "custom_css": "/assets/css/admin.css",
    "custom_js": None,
    # Whether to show the UI customizer on the sidebar
    "show_ui_builder": False,

    #################
    # Related Modal #
    #################
    # Use modals instead of popups
    "related_modal_active": True,
}

JAZZMIN_UI_TWEAKS = {
    "theme": "lumen",
    "dark_mode_theme": None,
}

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication'
    ]
}

LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,
    'formatters': {
        'first_formatter': {
            '()': CustomJsonFormatter,
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'info.log',
            'formatter': 'first_formatter',
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'first_formatter',
        },
    },
    'loggers': {
        'main': {
            'handlers': ['file', 'console'],
            'propagate': True,
            'level': "INFO",
        }
    }
}

MESSAGE_LEVEL = messages.DEBUG
MESSAGE_TAGS = {
    messages.DEBUG: 'alert-dark',
    messages.INFO: 'alert-primary',
    messages.SUCCESS: 'alert-info',
    messages.WARNING: 'alert-warning',
    messages.ERROR: 'alert-danger',
 }


# Allows us to use modals with iframes.
CSP_FRAME_ANCESTORS = ("'self'",)
CSP_IMG_SRC = (
    "'self' 'unsafe-inline' data:",
    "*.printify.com",
    "*.amazonaws.com",
    "*.twimg.com",
    "*"
)

CSP_DEFAULT_SRC = (
    "'self' 'unsafe-inline' 'unsafe-eval' data:",
    "*.gstatic.com",
    "*.twitter.com",
    "*.sharethis.com",
    "*.fontawesome.com",
    "*.cloudflare.com",
    "*.facebook.net",
    "*.facebook.com",
    "cdn.jsdelivr.net",
    "*.bootstrapcdn.com",
    "*.stripe.com",
    "*.jquery.com",
    "*.github.io",
    "*.googleapis.com",
    "intellic-labs.sfo3.cdn.digitaloceanspaces.com",
    "*.jsdelivr.net",
    "unpkg.com",
    "*.vapi.ai",
    "*.daily.co",
    "*.wss.daily.co",
    "*.sentry.io",
    "*.ingest.sentry.io"
)
CSP_STYLE_SRC = (
    "'self' 'unsafe-inline' data:",
    "*.gstatic.com",
    "*.fontawesome.com",
    "api.iconify.design",
    "api.simplesvg.com",
    "api.unisvg.com",
    "cdn.jsdelivr.net",
    "cdn.quilljs.com"
)
CSP_STYLE_SRC_ATTR = (
    "'self' 'unsafe-inline' data:",
    "*.gstatic.com",
    "*.fontawesome.com",
    "cdn.jsdelivr.net",
    "cdn.quilljs.com"
)
CSP_STYLE_SRC_ELEM = (
    "'self' 'unsafe-inline' data:",
    "*.gstatic.com",
    "*.googleapis.com",
    "*.fontawesome.com",
    "*.cloudflare.com",
    "*.bootstrapcdn.com",
    "*.stripe.com",
    "*.jquery.com",
    "*.github.io",
    "*.vapi.ai",
    "cdn.datatables.net",
    "cdn.jsdelivr.net",
    "cdn.quilljs.com"
)
CSP_SCRIPT_SRC_ELEM = (
    "'self' 'unsafe-inline' 'unsafe-eval' data:",
    "*.gstatic.com",
    "*.googleapis.com",
    "*.fontawesome.com",
    "*.cloudflare.com",
    "*.bootstrapcdn.com",
    "*.stripe.com",
    "*.jquery.com",
    "*.github.io",
    "*.jsdelivr.net",
    "unpkg.com",
    "*.amcharts.com",
    "*.ckeditor.com",
    "*.vapi.ai",
    "*.daily.co",
    "*.sentry.io",
    "*.ingest.sentry.io",
    "c.daily.co",
    "*.wss.daily.co",
    "cdn.datatables.net",
    "cdn.jsdelivr.net"
    "api.iconify.design",
    "api.simplesvg.com",
    "api.unisvg.com",
    "cdn.quilljs.com"
)
CSP_CONNECT_SRC = (
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'",
    "*.gstatic.com",
    "*.twitter.com",
    "*.sharethis.com",
    "*.fontawesome.com",
    "*.cloudflare.com",
    "*.facebook.net",
    "*.facebook.com",
    "*.bootstrapcdn.com",
    "*.stripe.com",
    "*.jquery.com",
    "*.github.io",
    "*.googleapis.com",
    "intellic-labs.sfo3.cdn.digitaloceanspaces.com",
    "*.jsdelivr.net",
    "unpkg.com",
    "*.vapi.ai",
    "*.daily.co",
    "c.daily.co",
    "*.wss.daily.co",
    "*.sentry.io",
    "*.ingest.sentry.io",
    "wss://*.wss.daily.co/",
    "wss://*",
    "ws://*",
    "ws://localhost:8001",
    "api.iconify.design",
    "api.simplesvg.com",
    "api.unisvg.com"
)


LINK_DOMAIN = 'https://silo.intelliclabs.com'
LOGOUT_REDIRECT_URL = '/'



TWILIO_ACCOUNT_SID = '**********************************'
TWILIO_AUTH_TOKEN = 'c458f3307dddd904104884b48789c0ca'
TWILIO_PHONE_NUMBER = '+***********'
TWILIO_PHONE_NUMBER_VOICE_URL = f'{LINK_DOMAIN}/phone/api/voice/'
TWILIO_PHONE_NUMBER_SMS_URL = f'{LINK_DOMAIN}/phone/api/sms/'
TWILIO_MESSAGING_SERVICE_SID = 'MG3e35778415ba44f880487f0c927276e8'

# Twilio Conversation Relay settings
# These need to be created in the Twilio Console
# https://www.twilio.com/docs/voice/twiml/connect/conversationrelay/onboarding
TWILIO_CONVERSATION_SERVICE_SID = 'IS6f78db972d88415abb7b819212fa7e91'  # Replace with actual SID
TWILIO_CONVERSATION_SID = 'CH5d277fd223d0469d987bd370dbbb5934'  # Replace with actual SID


EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

EMAIL_HOST = 'mail.gptenterprise.ai'
EMAIL_PORT = 587  # 465
EMAIL_USE_TLS = False  # True

EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = '%Qa%uj,3O7?y'
EMAIL_FROM = '"Silo AI" <<EMAIL>>'
DEFAULT_FROM_EMAIL = '"Silo AI" <<EMAIL>>'

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': '/var/tmp/django_cache',
    }
}


# SECURE_SSL_REDIRECT = True
# SESSION_COOKIE_SECURE = True
# CSRF_COOKIE_SECURE = True
# SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'


CORS_ORIGIN_ALLOW_ALL = True



ENCRYPTION_KEY = 'unuWQIm6GMItPbDpQf4yuqg0mLduWS8E-Vn9w4kMivc='

# Zoom API settings
ZOOM_API_KEY = ''
ZOOM_API_SECRET = ''
ZOOM_ACCOUNT_ID = ''

# OpenAI API settings
OPENAI_API_KEY = ''


# MailGun API settings
MAILGUN_API_KEY = '**************************************************'
MAILGUN_WEBHOOK_SIGNING_KEY = 'f9f844973c6779bda1f1ccd27edc8868'
MAILGUN_VERIFICATION_PUBLIC_KEY = 'pubkey-52ab9b8437a7e9934e71b8512a66a877'


