"""
URL configuration for MarketingSaaS project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.urls import re_path
from django.views.generic import RedirectView
from django.views.generic.base import TemplateView

urlpatterns = [

    re_path("admin/", admin.site.urls),

    re_path('chat/', include('apps.chat.urls')),
    re_path('system/', include('apps.system.urls')),
    re_path('leads/', include('apps.leads.urls')),
    re_path('phone/', include('apps.phone.urls')),
    re_path('voicebot/', include('apps.voicebot.urls')),
    re_path('zoombot/', include('apps.zoombot.urls')),
    re_path('emailer/', include('apps.emailer.urls')),
    re_path('bulk_email/', include('apps.email_bulk.urls')),
    re_path('email/', include('apps.email.urls')),
    re_path('email_client/', include('apps.email_client.urls')),
    re_path('email_warmup/', include('apps.email_warmup.urls')),
    re_path('restaurant/', include('apps.restaurant.urls')),
    re_path('accounts/', include('django.contrib.auth.urls')),
    re_path(
        'robots.txt',
        TemplateView.as_view(template_name='robots.txt', content_type='text/plain'),
    ),
    re_path('', include('apps.authentication.urls')),
    re_path('', include('apps.home.urls')),

    re_path(r'^favicon\.ico$', RedirectView.as_view(url='/static/assets/favicon.ico')),

]
