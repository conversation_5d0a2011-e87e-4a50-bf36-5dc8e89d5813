# Voice Bot with <PERSON><PERSON><PERSON> Conversation Relay and <PERSON><PERSON><PERSON> Claude

This Django app implements a voice bot using <PERSON><PERSON><PERSON>'s ConversationRelay feature with WebSockets and Anthropic's <PERSON> for generating responses.

## Features

- Handles incoming voice calls using <PERSON><PERSON><PERSON>'s ConversationRelay with WebSockets
- Processes voice conversations in real-time using <PERSON><PERSON><PERSON>'s <PERSON>
- Provides low-latency responses for a natural conversation experience
- Stores conversation history in the database
- Provides admin interface for managing voice conversations

## Setup

1. Install the required dependencies:
   ```
   pip install twilio anthropic channels
   ```

2. Configure Twilio settings in `settings.py`:
   - `TWILIO_ACCOUNT_SID`: Your Twilio account SID
   - `TWILIO_AUTH_TOKEN`: Your Twilio auth token
   - `TWILIO_PHONE_NUMBER`: Your Twilio phone number

3. Configure Django Channels in `settings.py`:
   ```python
   INSTALLED_APPS = [
       # ... other apps
       "channels",
       # ... your apps
   ]

   ASGI_APPLICATION = "core.routing.application"

   CHANNEL_LAYERS = {
       "default": {
           "BACKEND": "channels.layers.InMemoryChannelLayer",
       },
   }
   ```

4. Run migrations to create the database tables:
   ```
   python manage.py makemigrations
   python manage.py migrate
   ```

5. Configure your Twilio phone number to use the voice bot:
   - Set the voice webhook URL to `https://your-domain.com/voicebot/welcome/`

6. Ensure your server supports WebSockets:
   - For production, use a ASGI server like Daphne or Uvicorn
   - Configure your web server (Nginx, etc.) to proxy WebSocket connections

## Usage

When someone calls your Twilio phone number, the voice bot will:
1. Answer the call with a welcome message
2. Connect to the ConversationRelay WebSocket
3. Process the caller's speech in real-time using Anthropic's Claude LLM
4. Respond with AI-generated messages with low latency

## Models

### VoiceConversation

Stores information about voice conversations:
- `user`: The user associated with the conversation (optional)
- `chat_bot`: The chat bot associated with the conversation (optional)
- `call_sid`: The Twilio call SID
- `phone_to`: The phone number called
- `phone_from`: The caller's phone number
- `conversation_sid`: The Twilio conversation SID
- `status`: The status of the conversation
- `duration`: The duration of the conversation in seconds
- `raw_request`: The raw request data for debugging
- `date_created`: The date and time the conversation was created
- `date_updated`: The date and time the conversation was last updated

### VoiceMessage

Stores individual messages in a voice conversation:
- `conversation`: The conversation the message belongs to
- `role`: The role of the message sender (human or AI)
- `message`: The message text
- `message_display`: The formatted message text for display
- `audio_url`: The URL of the audio file (if available)
- `duration`: The duration of the message in seconds
- `date_created`: The date and time the message was created

## Components

### Views

#### voice_bot_welcome

Handles incoming calls and initiates a conversation relay with WebSocket connection.

#### status_callback

Handles status callbacks from Twilio's conversation relay.

#### message_callback

Legacy endpoint for backward compatibility. All message processing is now handled by the WebSocket consumer.

### WebSocket Consumer

The `VoiceBotConsumer` class handles real-time communication with Twilio's ConversationRelay:
- Processes incoming speech transcripts
- Generates responses using Anthropic's Claude LLM
- Sends responses back to Twilio for text-to-speech conversion

### URLs

- `/voicebot/welcome/`: Handles incoming calls
- `/voicebot/status_callback/`: Handles status callbacks
- `/voicebot/message_callback/`: Legacy endpoint (not used with WebSockets)
- `/ws/voicebot/websocket/`: WebSocket endpoint for real-time communication

## Notes

- The voice bot uses Anthropic's Claude LLM for generating responses, which provides natural and contextually relevant responses.
- The WebSocket-based ConversationRelay feature allows for real-time voice conversations with the AI with low latency.
- All conversations and messages are stored in the database for later review and analysis.
- For production deployment, ensure your server is properly configured to handle WebSocket connections.
