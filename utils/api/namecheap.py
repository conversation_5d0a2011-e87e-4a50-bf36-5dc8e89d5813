import requests
import xml.etree.ElementTree as ET


class NamecheapUtility:
    """
    A wrapper for the Namecheap API to manage domains and DNS records.

    Features:
    - Check domain availability
    - Register a domain
    - Set custom nameservers
    - Switch to Namecheap's default DNS
    - Get DNS host records
    - Set, add, update, delete A/CNAME/MX/TXT/NS/etc. records

    Example usage:
    ```python
    nc = NamecheapAPI(
        api_user="your_api_user",
        api_key="your_api_key",
        username="your_username",
        client_ip="your_ip",
        sandbox=True
    )

    # Check domain availability
    print(nc.check_domain("examplebot123.com"))

    # Use Namecheap DNS
    nc.use_namecheap_dns("examplebot123.com")

    # Add A record
    nc.add_dns_record("examplebot123.com", {
        "Name": "@",
        "Type": "A",
        "Address": "***************",
        "TTL": "1800"
    })

    # Register domain
    contact = {
        "FirstName": "<PERSON>",
        "LastName": "Doe",
        "Address1": "123 Main St",
        "City": "New York",
        "StateProvince": "NY",
        "PostalCode": "10001",
        "Country": "US",
        "Phone": "*************",
        "Email": "<EMAIL>"
    }

    # namecheap.register_domain("example12345678.com", 1, contact_info=contact)

    # Add a new A record
    api.add_dns_record("example.com", {
        "Name": "api",
        "Type": "A",
        "Address": "*******",
        "TTL": 1800
    })

    # Update the root A record
    api.update_dns_record("example.com", {
        "Name": "@",
        "Type": "A",
        "Address": "*******",
        "TTL": 1800
    })

    # Delete a CNAME record
    api.delete_dns_record("example.com", "blog", "CNAME")
    ```
    """
    def __init__(self, api_user, api_key, username, client_ip, sandbox=True):
        self.api_user = api_user
        self.api_key = api_key
        self.username = username
        self.client_ip = client_ip
        self.sandbox = sandbox
        self.base_url = "https://api.sandbox.namecheap.com/xml.response" if sandbox else "https://api.namecheap.com/xml.response"
        self.ns = {"nc": "http://api.namecheap.com/xml.response"}

    def _make_request(self, command, params):
        base_params = {
            "ApiUser": self.api_user,
            "ApiKey": self.api_key,
            "UserName": self.username,
            "ClientIp": self.client_ip,
            "Command": command,
        }
        all_params = {**base_params, **params}
        response = requests.get(self.base_url, params=all_params)
        response.raise_for_status()
        return self._parse_response(response.text)

    def _parse_response_old(self, xml):
        root = ET.fromstring(xml)
        errors = root.find(".//Errors")
        if errors is not None and list(errors):
            raise Exception("API Error: " + errors[0].text)
        return root

    def _parse_response(self, xml):
        root = ET.fromstring(xml)

        # DEBUG: print or log full XML
        print("==== RAW XML ====")
        print(xml)

        errors = root.find(".//Errors")
        if errors is not None and list(errors):
            raise Exception("API Error: " + errors[0].text)
        return root

    def check_domain(self, domain):
        """Check if a domain is available."""
        result = self._make_request("namecheap.domains.check", {"DomainList": domain})

        ns = {"nc": "http://api.namecheap.com/xml.response"}  # define the namespace
        r = result.find(".//nc:DomainCheckResult", ns)

        if r is None:
            xml_str = ET.tostring(result, encoding="unicode")
            raise Exception(f"DomainCheckResult not found in response:\n{xml_str}")

        return {
            "Available": r.attrib["Available"] == "true",
            "Domain": r.attrib["Domain"],
            "IsPremium": r.attrib.get("IsPremiumName", "false") == "true"
        }

    def register_domain(self, domain_name, years=1, nameservers=None, contact_info=None):
        """
        Register a domain. Raises an error if unavailable.
        """
        params = {
            "DomainName": domain_name,
            "Years": years,
        }

        if nameservers:
            params["Nameservers"] = ",".join(nameservers)

        # Add dummy contact info if none provided
        if not contact_info:
            contact_info = {
                "RegistrantFirstName": "John",
                "RegistrantLastName": "Doe",
                "RegistrantAddress1": "123 Fake St",
                "RegistrantCity": "Faketown",
                "RegistrantStateProvince": "CA",
                "RegistrantPostalCode": "12345",
                "RegistrantCountry": "US",
                "RegistrantPhone": "*************",
                "RegistrantEmailAddress": "<EMAIL>",
            }

        for k, v in contact_info.items():
            params[k] = v
            params[k.replace("Registrant", "Tech")] = v
            params[k.replace("Registrant", "Admin")] = v
            params[k.replace("Registrant", "AuxBilling")] = v

        result = self._make_request("namecheap.domains.create", params)

        # extract with namespace
        ns = {"nc": "http://api.namecheap.com/xml.response"}
        r = result.find(".//nc:DomainCreateResult", ns)

        if r is None:
            xml_str = ET.tostring(result, encoding="unicode")
            raise Exception(f"DomainCreateResult not found:\n{xml_str}")

        return {
            "Registered": r.attrib["Registered"] == "true",
            "Domain": r.attrib["Domain"],
            "OrderID": r.attrib["OrderID"],
            "TransactionID": r.attrib["TransactionID"],
        }

    def update_dns_records(self, domain, host_records):
        sld, tld = domain.split(".", 1)
        params = {
            "SLD": sld,
            "TLD": tld
        }

        for i, rec in enumerate(host_records, 1):
            params[f"HostName{i}"] = rec["Name"]
            params[f"RecordType{i}"] = rec["Type"]
            params[f"Address{i}"] = rec["Address"]
            params[f"TTL{i}"] = rec.get("TTL", "1800")

        result = self._make_request("namecheap.domains.dns.setHosts", params)
        r = result.find(".//nc:CommandResponse", self.ns)
        if r is None:
            raise Exception("Failed to update DNS. No CommandResponse found.")
        return True

    def use_namecheap_dns(self, domain):
        sld, tld = domain.split(".", 1)
        result = self._make_request("namecheap.domains.dns.setDefault", {
            "SLD": sld,
            "TLD": tld
        })
        r = result.find(".//nc:CommandResponse", self.ns)
        if r is None:
            raise Exception("Failed to set Namecheap DNS. No CommandResponse found.")
        return True  # Success if no exception

    def get_dns_records(self, domain):
        """
        Fetch current DNS records.
        :param domain: e.g. "example.com"
        :return: List of dicts with keys: Name, Type, Address, TTL
        """
        sld, tld = domain.split(".", 1)
        result = self._make_request("namecheap.domains.dns.getHosts", {
            "SLD": sld,
            "TLD": tld
        })
        records = []
        for host in result.findall(".//nc:Host", self.ns):
            records.append({
                "Name": host.attrib.get("Name"),
                "Type": host.attrib.get("Type"),
                "Address": host.attrib.get("Address"),
                "TTL": host.attrib.get("TTL")
            })
        return records

    def set_dns_records(self, domain, records):
        """
        Overwrite all DNS records for a domain.

        :param domain: "example.com"
        :param records: List of dicts with keys: Name, Type, Address, TTL
        """
        sld, tld = domain.split(".", 1)
        params = {"SLD": sld, "TLD": tld}
        for i, record in enumerate(records, start=1):
            params[f"HostName{i}"] = record["Name"]
            params[f"RecordType{i}"] = record["Type"]
            params[f"Address{i}"] = record["Address"]
            params[f"TTL{i}"] = record.get("TTL", "1800")
        result = self._make_request("namecheap.domains.dns.setHosts", params)
        r = result.find(".//nc:DomainDNSSetHostsResult", self.ns)
        if r is None:
            raise Exception("Failed to set DNS records (missing result in response)")
        return {
            "IsSuccess": r.attrib.get("IsSuccess", "").lower() == "true"
        }

    def add_dns_record(self, domain, record):
        """
        Add a DNS record (append, don't overwrite others).

        :param domain: e.g. "example.com"
        :param record: dict with keys Name, Type, Address, TTL
        """
        current = self.get_dns_records(domain)
        current.append(record)
        return self.set_dns_records(domain, current)

    def update_dns_record(self, domain, record):
        """
        Update a DNS record by matching Name + Type.

        :param domain: e.g. "example.com"
        :param record: dict with keys Name, Type, Address, TTL
        """
        current = self.get_dns_records(domain)
        updated = [r for r in current if not (
            r["Name"] == record["Name"] and r["Type"] == record["Type"]
        )]
        updated.append(record)
        return self.set_dns_records(domain, updated)

    def delete_dns_record(self, domain, name, type_):
        """
        Delete a DNS record by name and type.

        :param domain: e.g. "example.com"
        :param name: e.g. '@' or 'www'
        :param type_: e.g. 'A' or 'CNAME'
        """
        current = self.get_dns_records(domain)
        updated = [r for r in current if not (
            r["Name"] == name and r["Type"] == type_
        )]
        return self.set_dns_records(domain, updated)
