import asyncio
import json
import logging
import sys
import websockets
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# WebSocket endpoint
WS_ENDPOINT = "wss://silo.intelliclabs.com/ws/voicebot/websocket/"

# Sample message templates
SAMPLE_MESSAGES = {
    "start": {
        "type": "start",
        "callSid": "test_call_" + datetime.now().strftime("%Y%m%d%H%M%S")
    },
    "speech": {
        "type": "speech",
        "callSid": "",  # Will be filled in during runtime
        "transcript": "Hello, how are you today?"
    },
    "dtmf": {
        "type": "dtmf",
        "callSid": "",  # Will be filled in during runtime
        "digits": "1"
    },
    "mark": {
        "type": "mark",
        "callSid": "",  # Will be filled in during runtime
        "name": "test_mark"
    }
}

async def send_message(websocket, message_type, custom_text=None):
    """Send a message to the WebSocket server."""
    message = SAMPLE_MESSAGES.get(message_type, {}).copy()

    # If it's a speech message and custom text is provided, use it
    if message_type == "speech" and custom_text:
        message["transcript"] = custom_text

    if not message:
        logger.error(f"Unknown message type: {message_type}")
        return

    # Log the message being sent
    logger.info(f"Sending message: {json.dumps(message, indent=2)}")

    # Send the message
    await websocket.send(json.dumps(message))

async def receive_messages(websocket):
    """Receive and log messages from the WebSocket server."""
    try:
        while True:
            response = await websocket.recv()
            try:
                parsed_response = json.loads(response)
                logger.info(f"Received: {json.dumps(parsed_response, indent=2)}")
            except json.JSONDecodeError:
                logger.info(f"Received (raw): {response}")
    except websockets.exceptions.ConnectionClosedError:
        logger.warning("Connection closed")

async def interactive_session():
    """Run an interactive WebSocket session."""
    call_sid = None

    try:
        async with websockets.connect(WS_ENDPOINT) as websocket:
            logger.info(f"Connected to {WS_ENDPOINT}")

            # Start a task to receive messages
            receive_task = asyncio.create_task(receive_messages(websocket))

            # Interactive loop
            while True:
                print("\nAvailable commands:")
                print("1. start - Send a start message")
                print("2. speech <text> - Send a speech message with optional text")
                print("3. dtmf <digits> - Send DTMF digits")
                print("4. mark - Send a mark message")
                print("5. exit - Exit the program")

                command = input("\nEnter command: ").strip()

                if command.startswith("exit"):
                    break

                parts = command.split(" ", 1)
                cmd = parts[0].lower()
                args = parts[1] if len(parts) > 1 else None

                if cmd == "start":
                    # Generate a new call_sid
                    call_sid = "test_call_" + datetime.now().strftime("%Y%m%d%H%M%S")
                    SAMPLE_MESSAGES["start"]["callSid"] = call_sid

                    # Update call_sid in all message templates
                    for msg_type in SAMPLE_MESSAGES:
                        if "callSid" in SAMPLE_MESSAGES[msg_type]:
                            SAMPLE_MESSAGES[msg_type]["callSid"] = call_sid

                    await send_message(websocket, "start")

                elif cmd == "speech":
                    if not call_sid:
                        logger.warning("You must start a session first with 'start'")
                        continue

                    text = args if args else "Hello, how are you today?"
                    await send_message(websocket, "speech", text)

                elif cmd == "dtmf":
                    if not call_sid:
                        logger.warning("You must start a session first with 'start'")
                        continue

                    digits = args if args else "1"
                    SAMPLE_MESSAGES["dtmf"]["digits"] = digits
                    await send_message(websocket, "dtmf")

                elif cmd == "mark":
                    if not call_sid:
                        logger.warning("You must start a session first with 'start'")
                        continue

                    await send_message(websocket, "mark")

                else:
                    logger.warning(f"Unknown command: {cmd}")

            # Cancel the receive task
            receive_task.cancel()
            try:
                await receive_task
            except asyncio.CancelledError:
                pass

    except websockets.exceptions.ConnectionClosedError as e:
        logger.error(f"Connection error: {e}")
    except Exception as e:
        logger.error(f"Error: {e}")

def main():
    """Main entry point."""
    try:
        asyncio.run(interactive_session())
    except KeyboardInterrupt:
        logger.info("Program terminated by user")

if __name__ == "__main__":
    main()
