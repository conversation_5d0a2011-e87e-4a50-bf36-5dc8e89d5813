:root{
    --sidebar-width: 320px;
}

.rbt-left-panel {
    width: var(--sidebar-width);
    background-color: var(--color-dark);
    position: fixed;
    top: 80px;
    left: 0;
    min-height: 450px;
    padding: 10px 20px 0 20px;
    padding-bottom: 0;
    border-right: 1px solid var(--color-border);
    height: calc(100% - 80px);
    @media #{$smlg-device} {
        z-index: 9999;
    }
    .rbt-default-sidebar{
        position: relative;
        height: 100%;
        .rbt-default-sidebar-wrapper {
            height: 70vh;
            overflow: scroll;
            -ms-overflow-style: none;
            scrollbar-width: none;
            &::-webkit-scrollbar {
                display: none;
            }
            .rbt-course-details-list-wrapper,
            .rbt-default-sidebar-list {
                @extend %liststyle;
                li {
                    display: flex;
                    justify-content: space-between;
                    margin: 5px 0;
                    i {
                        color: var(--color-body);
                        margin-right: 10px;
                        font-size: 20px;
                    }
                    img{
                        width: 35px;
                        height: auto;
                        margin-right: 10px;
                    }
                    span {
                        font-weight: 500;
                        font-size: 16px;
                        line-height: 26px;
                        &.rbt-feature-value {
                            font-size: 12px;
                        }
                    }
                    a {
                        display: flex;
                        align-items: center;
                        color: var(--color-body);
                        transition: 0.4s;
                        padding: 10px 12px;
                        width: 100%;
                        border-radius: var(--radius-small);
                        &:hover,
                        &.active {
                            color: var(--color-primary);
                            background: var(--color-blackest);
                            i{
                                color: var(--color-primary);
                            }
                        }
                        
                    }
                    &.has-submenu{
                        display: block;
                        .collapse-btn{
                            position: relative;
                            &::after {
                                position: absolute;
                                content: "\e996";
                                font-family: 'feather' !important;
                                right: 10px;
                                top: 50%;
                                transform: translateY(-50%);
                                transition: all 0.4s ease;
                            }
                            &.collapsed {
                                &::after {
                                    content: "\e9b1";
                                }
                            }
                        }
                        .submenu{
                            margin-left: 30px;
                        }
                    }
                }
            }
        }
        .subscription-box{
            position: absolute;
            bottom: 50px;
            left: 0;
            width: 100%;
            height: auto;
            z-index: 5;
            background-color: var(--color-blackest);
            border-radius: var(--radius);
            .inner{
                display: flex;
                flex-direction: column;
                padding: 10px;
                .autor-info{
                    display: flex;
                    align-items: flex-start;
                    margin-bottom: 10px;
                    border-radius: var(--radius-small);
                    padding: 10px;
                    transition: all 0.3s;
                    position: relative;
                    &:hover{
                        background-color: var(--color-dark);
                    }
                    .author-img{
                        height: 40px;
                        width: 40px;
                        border-radius: 50%;
                        position: relative;
                        overflow: hidden;
                        margin-right: 10px;
                        &.active{
                            &::after{
                                content: '';
                                position: absolute;
                                right: 5px;
                                bottom: 5px;
                                height: 10px;
                                width: 10px;
                                border-radius: 50%;
                                background: var(--color-success);
                            }
                        }
                    }
                    .author-desc{
                        h6{
                            margin-bottom: 2px;
                        }
                        p{
                            margin-bottom: 0;
                        }
                    }
                    .author-badge{
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        padding: 0px 5px;
                        border-radius: 4px;
                        background: var(--color-primary);
                        color: var(--color-white);
                        font-size: var(--font-size-b4);
                        list-style: var(--line-height-b4);
                    }
                }
                .btn-default{
                    display: block;
                    cursor: pointer;
                }
            }
        }
        .copyright-text{
            position: absolute;
            bottom: 20px;
            z-index: 3;
            margin-bottom: 0;
            width: 100%;
        }
    }
}

.chat-content{
    .title{
        .rainbow-badge-card{
            margin-left: 5px;
        }
    }
    .download-btn{
        position: absolute;
        top: 30px;
        right: 30px;
        i{
            margin-right: 5px;
        }
    }
}



.rbt-sm-separator{
    margin: 20px 0;
    width: 100%;
    height: 1px;
    background-color: var(--color-border);
}

.rbt-main-content {
    background: var(--color-dark);
    margin-left: var(--sidebar-width);
    min-height: 450px;
    height: auto;
    margin-bottom: 210px;
    margin-right: var(--sidebar-width);
    margin-top: 80px;
    @media #{$smlg-device} {
        margin-left: 0;
        margin-right: 0;
    }
}

.rbt-dashboard-content {
    .content-page{
        padding: 0 190px;
        @media #{$laptop-device} {
            padding: 0 50px;
        }
        @media #{$sm-layout} {
            padding: 0 20px;
        }
        @media #{$md-layout} {
            padding: 0 40px;
        }
        @media #{$lg-layout} {
            padding: 0 50px;
        }
        .chat-box-list{
            padding-left: 0;
            list-style: none;
            margin: 0;
            .chat-box{
                margin-bottom: 30px;
                padding: 30px;
                border-radius: var(--radius);
                background-color: var(--color-blackest);
                position: relative;
                @media #{$sm-layout} {
                    padding: 10px;
                }
                &:last-child{
                    margin-bottom: 0;
                }
                .inner{
                    .chat-section{
                        display: flex;
                        align-items: flex-start;
                        .author{
                            height: 40px;
                            width: 40px;
                            border-radius: 50%;
                            overflow: hidden;
                            margin-right: 20px;
                            background-color: transparent;
                            color: var(--color-primary);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            @media #{$sm-layout} {
                                height: 24px;
                                width: 24px;
                                margin-right: 5px;
                            }
                            svg{
                                width: 24px;
                                height: auto;
                            }
                        }
                        .chat-content{
                            max-width: calc(100% - 60px);
                            width: 100%;
                            @media #{$sm-layout} {
                                max-width: calc(100% - 30px);
                            }
                            .title{
                                margin-bottom: 10px;
                                &.color-text-off{
                                    color: var(--color-text-off);
                                }
                            }
                            p{
                                margin-bottom: 0;
                            }
                            .img-box{
                                position: relative;
                                .download-btn{
                                    right: 20px;
                                    top: 20px;
                                }
                            }
                            .img-box-grp{
                                display: flex;
                                .img-box+.img-box{
                                    margin-left: 20px;
                                }
                            }
                            .reaction-section{
                                .btn-grp{
                                    display: flex;
                                    justify-content: space-between;
                                    @media #{$sm-layout} {
                                        flex-wrap: wrap;
                                    }
                                    .left-side-btn{
                                        @media #{$sm-layout} {
                                            display: flex;
                                        }
                                    }
                                    .right-side-btn{
                                        @media #{$sm-layout} {
                                            margin-top: 10px;
                                        }
                                    }
                                    .react-btn{
                                        display: inline-flex;
                                        justify-content: center;
                                        align-items: center;
                                        padding: 0 10px;
                                        font-size: var(--font-size-b1);
                                        line-height: var(--line-height-b1);
                                        @media #{$sm-layout} {
                                            display: flex;
                                            margin: 0 5px;
                                        }
                                        span{
                                            font-size: var(--font-size-b3);
                                            line-height: var(--line-height-b3);
                                            margin-left: 10px;
                                        }
                                        &.show{
                                            background-color: var(--color-primary);
                                            border-color: var(--color-primary);
                                        }
                                    }
                                }
                            }
                            .image-caption{
                                padding: 15px 30px;
                                border-radius: var(--radius);
                                background: var(--color-dark);
                                .caption-title{
                                    margin-bottom: 0;
                                }
                            }
                            .video-popup{
                                &.frame-image{
                                    background-color: var(--color-dark);
                                }
                            }
                            .typing-icon{
                                #iframe-icon{
                                    background: transparent;
                                    display: block;
                                    shape-rendering: auto;
                                    width: 50px;
                                    height: 50px;
                                    overflow: hidden;
                                }
                            }
                        }
                        & +.chat-section{
                            margin-top: 10px;
                        }
                        &.generate-section{
                            display: flex;
                            align-items: center;
                            &:nth-child(2){
                                .author{
                                    color: var(--color-secondary);
                                }
                            }
                        }
                        &.generate-details-section{
                            .title{
                                margin-top: 10px;
                                @media #{$sm-layout} {
                                    margin-top: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .banner-area{
        padding: 20px;
    }
}

.rbt-right-side-panel {
    width: var(--sidebar-width);
    background: var(--color-dark);
    min-height: 450px;
    height: 100vh;
    position: fixed;
    overflow: scroll;
    -ms-overflow-style: none;
    scrollbar-width: none;
    right: 0;
    top: 80px;
    border-left: 1px solid var(--color-border);
    @media #{$smlg-device} {
        z-index: 999;
    }
    &::-webkit-scrollbar {
        display: none;
    }
    .right-side-top{
        padding: 20px;
        border-bottom: 1px solid var(--color-border);
        position: sticky;
        top: 0;
        z-index: 3;
        background: var(--color-dark);
        a{
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            padding: 10px 20px;
            position: relative;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 14px;
            border-radius: 5px;
            position: relative;
            cursor: pointer;
            span{
                font-size: var(--font-size-b3);
                line-height: var(--line-height-b3);
            }
            .icon{
                margin-right: 10px;
            }
        }
    }
    .right-side-bottom{
        padding: 20px;
        .title{
            padding: 0 12px;
            margin-bottom: 10px;
        }
        .search-section{
            position: relative;
            input{
                background: transparent;
                border: 2px solid var(--color-border);
                padding: 5px 15px 5px 30px;
                border-radius: var(--radius);
                color: var(--color-body);
                font-size: var(--font-size-b3);
                &::placeholder{
                    color: var(--color-body);
                    font-size: var(--font-size-b3);
                }
                &:focus,
                &:focus-visible{
                    border-color: var(--color-primary) !important;
                    outline: none;
                }
            }
            i{
                position: absolute;
                top: 50%;
                left: 10px;
                transform: translateY(-50%);
            }
        }
        .chat-history-section{
            padding-bottom: 20px;
            border-bottom: 1px solid var(--color-border);
            .chat-history-list{
                padding-left: 0;
                list-style: none;
                margin: 0;
                .history-box{
                    text-decoration: none;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 5px 5px 5px 10px;
                    border-radius: 5px;
                    font-size: var(--font-size-b3);
                    line-height: var(--line-height-b3);
                    font-weight: 400;
                    letter-spacing: 0;
                    position: relative;
                    cursor: pointer;
                    user-select: none;
                    transition: all 0.3s ease;
                    border: none;
                    margin: 0;
                    .more-info-icon{
                        background: transparent;
                        border: none;
                        color: var(--color-heading);
                        opacity: 0;
                        visibility: hidden;
                        font-size: var(--font-size-b1);
                        line-height: var(--line-height-b1);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 25px;
                        width: 25px;
                        border-radius: 50%;
                        background-color: transparent;
                        transition: all 0.3s;
                        &.show{
                            background-color: var(--color-dark);
                        }
                    }
                    &:hover{
                        background: var(--color-blackest);
                        .more-info-icon{
                            opacity: 0.8;
                            visibility: visible;
                            &:hover{
                                opacity: 1;
                            }
                        }
                    }
                    &.active{
                        background: var(--color-blackest);
                        .more-info-icon{
                            opacity: 0.8;
                            visibility: visible;
                            &:hover{
                                opacity: 1;
                            }
                        }
                    }
                }
            }
            &+.chat-history-section{
                padding-top: 20px; 
            }
            &:last-child{
                padding-bottom: 0;
                border: none;
            }
        }
    }
}

.rbt-dashboard-page{
    .dropdown-toggle::after,
    .dropup .dropdown-toggle::after{
        display: none;
    }
    .dropdown-menu {
        border-radius: var(--radius);
        padding: 10px 10px;
        background-color: var(--color-blackest);
        box-shadow: 2px 6px 20px 10px rgb(0 0 0 / 0.5);
        z-index: 9999;
        li{
            margin: 0;
            .dropdown-item{
                font-weight: 500;
                padding: 5px 20px;
                font-size: var(--font-size-b3);
                line-height: var(--line-height-b3);
                display: flex;
                align-items: center;
                color: var(--color-body);
                border-radius: 3px;
                svg{
                    width: 15px;
                    height: auto;
                    margin-right: 10px;
                }
                i{
                    font-size: var(--line-height-b3);
                    line-height: var(--line-height-b3);
                    margin-right: 10px;
                }
                &:hover,
                &:active,
                &:focus{
                    background-color: var(--color-dark);
                    color: var(--color-primary);
                }
                &.delete-item{
                    &:hover,
                    &:active,
                    &:focus{
                        color: var(--color-danger);
                    }
                }
            }
        }
    }
    
}

.custom-tooltip{
    .tooltip-inner{
        background-color: var(--color-primary);
        color: var(--color-white);
        font-size: var(--font-size-b4);
    }
    .tooltip-arrow::before{
        border-top-color: var(--color-primary);
    }
}



// Header
.rbt-dashboard-header {
    position: fixed;
    text-align: center;
    width: 100%;
    margin-left: 0;
    top: 0;
    background-color: var(--color-dark);
    padding: 0 20px;
    .mainmenu-nav{
        margin-left: 0;
    }
    .right-menu{
        ul{
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
            li{
                margin: 0;
                a{
                    color: var(--color-heading);
                    font-size: 16px;
                    font-weight: 500;
                    padding: 0 17px;
                    display: block;
                    height: 80px;
                    line-height: 80px;
                    transition: 0.3s;
                    &:hover{
                        color: #FF3BD4;
                    }
                }
            }
        }
    }
    .genarator-card{
        .title{
            font-size: var(--font-size-b3);
        }
    }
}
.header-default{
    .genarator-card{
        .title{
            font-size: var(--font-size-b3);
        }
    }
}



.rbt-content {
    width: 100%;
    position: relative;
    min-height: 450px;
}

.rbt-static-bar {
    position: fixed;
    width: calc(100% - (2*var(--sidebar-width)));
    margin-top: auto;
    z-index: 5;
    bottom: 0;
    padding: 30px 0;
    background-color: var(--color-dark);
    padding-bottom: 100px;
    box-shadow: 0px -40px 55px -20px rgba(7,7,16,1);
    transition: all 0.4s ease;
    &.area-left-expanded{
        width: calc(100% - var(--sidebar-width));
        padding-bottom: 50px;
        @media #{$smlg-device} {
            width: 100%;
        }
        .chatenai-separator{
            display: none;
        }
    }
    &.area-right-expanded{
        width: calc(100% - var(--sidebar-width));
        padding-bottom: 50px;
        @media #{$smlg-device} {
            width: 100%;
        }
        .chatenai-separator{
            display: none;
        }
        &.area-left-expanded{
            width: 100%;
            padding-bottom: 50px;
            .chatenai-separator{
                display: none;
            }
        }
    }
    .staticbar-btn-grp{
        margin: 0;
        padding: 0;
        list-style: none;
        position: absolute;
        top: auto;
        bottom: 100%;
        left: 0;
        width: 100%;
        justify-content: center;
        display: flex;
        background: var(--color-dark);
        flex-wrap: wrap;
        li{
            &+li{
                margin-left: 10px;
            }
            @media #{$sm-layout} {
                padding: 0 10px;
            }
        }
    }
    &.collapse-width{
        width: calc(100% - var(--sidebar-width));
        .chatenai-separator.has-position-bottom{
            bottom: -25px;
        }
        @media #{$smlg-device} {
            width: 100%;
            padding-bottom: 50px;
        }
        @media #{$sm-layout} {
            padding: 10px;
            padding-bottom: 50px;
        }
    }
    @media #{$smlg-device} {
        width: 100%;
        padding-bottom: 50px;
    }
    @media #{$sm-layout} {
        padding: 10px;
        padding-bottom: 50px;
    }
    .new-chat-form{
        margin: 0 190px;
        position: relative;
        border-radius: var(--radius);
        @media #{$sm-layout} {
            margin: 0;
        }
        @media #{$md-layout} {
            margin: 0 50px;
        }
        @media #{$lg-layout} {
            margin: 0 50px;
        }
        @media #{$laptop-device} {
            margin: 0 45px;
        }
        textarea{
            border: 2px solid var(--color-border);
            background-color: transparent;
            border-radius: var(--radius);
            outline: 0;
            display: block;
            width: 100%;
            padding: 18px 120px 16px 60px;
            font-size: 16px;
            line-height: 22px;
            max-height: 200px;
            @media #{$sm-layout} {
                padding: 10px;
                max-height: 100px;
            }
        }
        .form-icon{
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: var(--color-body);
            height: 30px;
            width: 30px;
            border-radius: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            &:hover{
                background: var(--color-dark);
            }
            @media #{$sm-layout} {
                display: none;
            }
            i{
                font-size: var(--font-size-b1);
                line-height: var(--line-height-b1);
            }
            &.icon-gpt{
                left: 19px;
            }
            &.icon-plus{
                right: 100px;
                border: none;
                background: transparent;
                cursor: pointer;
                pointer-events: all;
                .input-file { 
                    position: absolute;
                    right: 0;
                    margin: 0;
                    padding: 0;
                    height: 100%;
                    width: 100%;
                    cursor: pointer;
                    opacity: 0;
                    filter: alpha(opacity=0);

                }
            }
            &.icon-mic{
                right: 55px;
                background: transparent;
                color: var(--color-body);
                border: none;
            }
            &.icon-send{
                right: 19px;
                background: transparent;
                color: var(--color-body);
                border: none;
            }
            &:hover{
                background: var(--color-dark);
            }
        }
    }
    .small-text{
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 15px;
        @media #{$sm-layout} {
            margin-bottom: 10px;
        }
    }
    .chatenai-separator{
        z-index: -2;
        @media #{$smlg-device} {
            display: none;
        }
    }
}



// Rbt popup dashboard Style
.popup-dashboardleft-section{
    transition: all 0.4s ease;
    &.collapsed{
        transform: translateX(-100%);
    }
}
.popup-dashboardright-section{
    transition: all 0.4s ease;
    &.collapsed{
        transform: translateX(100%);
    }
}
.rbt-main-content{
    transition: all 0.4s ease;
    &.area-left-expanded{
        margin-left: 0;
    }
    &.area-right-expanded{
        margin-right: 0;
    }
}

.popup-dashboardleft-btn,
.popup-dashboardright-btn{
    transition: all 0.4s ease;
    &.opened{
        i{
            &.feather-menu.right{
                &::before{
                    content: "\e910";
                }
            }
            &.feather-menu.left{
                &::before{
                    content: "\e912";
                }
            }
        }
    }
}







/*---------------------------
    Rbt Show More Button  
------------------------------*/
.rbt-show-more-btn {
    font-size: 14px;
    font-weight: 700;
    color: var(--color-primary);
    display: block;
    margin-top: 5px;
    position: relative;
    padding: 5px;
    cursor: pointer;
    &::before {
        background: linear-gradient(rgba(7,7,16,0) 0%, rgba(7, 7, 16, 1) 100%);
        position: absolute;
        content: "";
        left: 0;
        bottom: 100%;
        width: 100%;
        height: 80px;
    }
    &::after {
        content: "\e92e";
        position: absolute;
        font-family: 'feather';
        right: 0;
        top: 50%;
        transform: translateY(-50%);
    }
    &.active {
        &::before {
            background: none;
            display: none;
        }
        &::after {
            content: "\e931";
        }
    }
}


.has-show-more {
    .has-show-more-inner-content {
        max-height: 200px;
        overflow: hidden;
        position: relative;
        width: 100%;
        transition: all 0.4s;
    }
    &.active {
        .has-show-more-inner-content {
            max-height: inherit !important;
        }
    }
}




// Dashboard Pricing
.chatenai-tab{
    display: flex;
    justify-content: center;
    .tab-btn-grp{
        padding: 10px;
        border: 2px solid var(--color-border);
        border-radius: 100px;
        display: flex;
        justify-content: space-between !important;
        button{
            padding: 10px 35px;
            font-size: var(--font-size-b1);
            line-height: var(--line-height-b1);
            font-weight: 500;
            color: var(--color-heading);
            align-items: center;
            justify-content: center;
            display: flex;
            border-radius: 100px;
            transition: all 0.3s ease;
            border: none;
            outline: none;
            &.active{
                background: var(--color-primary);
                color: var(--color-white);
            }
            &:focus-visible,
            &:focus-within,
            &:focus{
                color: var(--color-white);
            }
            .rainbow-badge-card{
                margin-left: 10px;
                font-size: var(--font-size-b4);
                line-height: var(--line-height-b4);
                background: none;
            }
        }
    }
}

.expand-btn-grp{
    display: flex;
    justify-content: center;
    align-items: center;
    button{
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
        background: var(--color-blackest);
        width: 40px;
        height: 40px;
        border-radius: 100%;
        color: var(--color-primary);
        border: none;
        font-size: var(--font-size-b1);
        &.popup-dashboardright-btn{
            background: var(--color-primary);
            color: var(--color-white);
        }
        &:hover{
            background: var(--color-primary);
            color: var(--color-white);
        }
    }
}

.mainmenu-nav .mainmenu li.with-megamenu .rainbow-megamenu.with-mega-item-2{
    width: 600px;
    padding: 15px;
    background-color: var(--color-blackest);
    overflow: hidden;
    box-shadow: 0 0 20px 5px rgba(7, 7, 16, 0.1);
    border-radius: 0 0 10px 10px;
    .wrapper{
        border-radius: none;
        background: none;
        box-shadow: none;
    }
}



// Small Carosel Bannner
.chatenai-small-slider{
    .inner{
        background: var(--color-secondary);
        display: flex;
        border-radius: var(--radius);
        justify-content: space-between;
        overflow: hidden;
        align-items: flex-end;
        @media #{$sm-layout} {
            flex-direction: column;
            align-items: flex-start;
        }
        &.bg-one{
            background: var(--color-secondary);
        }
        &.bg-two{
            background: var(--color-primary);
        }
        &.bg-three{
            background: var(--color-tertiary);
        }
        &.bg-four{
            background: var(--color-primary-alt);
        }
        .content{
            padding: 30px;
            .title{
                margin-bottom: 10px;
                color: var(--color-white);
            }
            p{
                color: var(--color-white);
            }
        }
    }
    .img-section{
        display: flex;
        max-width: 33%;
        height: 100%;
        img{
            height: 100%;
        }
        @media #{$sm-layout} {
            max-width: 100%;
        }
    }
}

.rbt-dashboard-content{
    .slick-dots{
        display: flex;
        position: absolute;
        bottom: 30px;
        left: 40px;
    }
    .rainbow-slick-dot .slick-dots li button::after{
        background: var(--color-white);
        box-shadow: inset 0 0 0 5px var(--color-white);
        opacity: 0.6;
    }
    .rainbow-slick-dot .slick-dots li.slick-active button::after{
        box-shadow: inset 0 0 0 1px var(--color-white);
        opacity: 0.8;
    }
}

.welcome-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .content-section{
        .icon{
            font-size: var(--h3);
        }
    }
    .btn-default{
        cursor: pointer;
    }
}



/*-------------------------
    Admin User Settings
--------------------------*/


.rbt-admin-profile {
    display: flex;
    align-items: center;
    .admin-thumbnail {
        margin-right: 12px;
        img {
            width: 52px;
            min-width: 52px;
            height: 52px;
            border-radius: 100%;
        }
    }
    .admin-info {
        .name {
            color: var(--color-heading);
            font-weight: 500;
            display: block;
            line-height: 1;
            margin-bottom: 2px;
        }
    }
}

.rbt-user-menu-list-wrapper {
    .user-list-wrapper {
        margin: 0 -12px;
        list-style: none;
        padding: 0;
        li {
            margin: 0;
            a {
                display: flex;
                padding: 5px 12px;
                align-items: center;
                border-radius: 3px;
                color: var(--color-body);
                img,
                i {
                    max-height: 17px;
                    margin-right: 7px;
                }
                i {
                    font-size: 17px;
                }
    
                span {
                    font-size: 14px;
                }
            }
            &:hover {
                a {
                    color: var(--color-primary);
                    background: var(--color-dark);
                }
            }
        }
    }
}
.rbt-user{
    height: 80px;
    line-height: 80px;
    padding: 0 17px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
}

.rbt-user-wrapper {
    position: relative;
    z-index: 2;
    .admin-img{
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
        background: var(--color-blackest);
        width: 40px;
        height: 40px;
        border-radius: 100%;
        color: var(--color-primary);
        border: none;
        font-size: var(--font-size-b1);
        overflow: hidden;
    }
    @media #{$large-mobile} {
        position: static;
    }
    .rbt-user-menu-list-wrapper {
        @extend %rbt-dropdown-type-1;
        background: var(--color-blackest);
        min-width: 250px;
        border-radius: 0 0 10px 10px;
        @media #{$lg-layout} {
            left: auto;
            right: 0;
        }
        @media #{$md-layout} {
            left: auto;
            right: 0;
        }
        @media #{$sm-layout} {
            left: auto;
            right: 0;
        }
        @media #{$large-mobile} {
            left: 20px;
            right: 20px;
            width: auto;
        }
        
        .inner {
            padding: 20px;
            hr {
                margin-left: -20px;
                margin-right: -20px;
            }
            .rbt-admin-profile {
                padding-bottom: 20px;
            }
        }
    }
    &:hover {
        .rbt-user-menu-list-wrapper {
            @extend %rbt-hover-dropdown-type-1;
            
        }
    }
    &.right-align-dropdown {
        .rbt-user-menu-list-wrapper {
            right: 0;
            left: auto;
            @media #{$large-mobile} {
                right: 20px;
                left: 20px;
                width: auto;
            }
        }
    }
}
