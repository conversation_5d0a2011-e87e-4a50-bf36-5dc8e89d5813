2025-06-09 15:36:08 - === NEW WEBSOCKET CONNECTION ===
2025-06-09 15:36:08 - Scope details:
2025-06-09 15:36:08 - Client: ['127.0.0.1', 52148]
2025-06-09 15:36:08 - Headers: {b'host': b'localhost:8001', b'connection': b'Upgrade', b'pragma': b'no-cache', b'cache-control': b'no-cache', b'upgrade': b'websocket', b'origin': b'http://localhost:8000', b'sec-websocket-version': b'13', b'user-agent': b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', b'accept-encoding': b'gzip, deflate, br, zstd', b'accept-language': b'en-US,en;q=0.9', b'cookie': b'csrftoken=fwcqylKrW349A5ho7pe930W1MwJjzqtF', b'sec-gpc': b'1', b'sec-websocket-key': b'DHRvYEn2KZrn+hCFy7MqMQ==', b'sec-websocket-extensions': b'permessage-deflate; client_max_window_bits'}
2025-06-09 15:36:08 - Path: /ws/voicebot/websocket/
2025-06-09 15:36:08 - Query String: b''
2025-06-09 15:36:08 - User: AnonymousUser
2025-06-09 15:36:19 - === RECEIVED MESSAGE ===
2025-06-09 15:36:19 - Raw message: {"type":"start","callSid":"test_call_20250609223401457"}
2025-06-09 15:36:19 - === SESSION START ===
2025-06-09 15:36:23 - === RECEIVED MESSAGE ===
2025-06-09 15:36:23 - Raw message: {"type":"speech","callSid":"test_call_20250609223401457","transcript":"Tell me a joke"}
2025-06-09 15:36:23 - === HANDLE SPEECH ===
2025-06-09 15:38:25 - === NEW WEBSOCKET CONNECTION ===
2025-06-09 15:38:25 - Scope details:
2025-06-09 15:38:25 - Client: ['127.0.0.1', 52169]
2025-06-09 15:38:25 - Headers: {b'host': b'localhost:8001', b'connection': b'Upgrade', b'pragma': b'no-cache', b'cache-control': b'no-cache', b'upgrade': b'websocket', b'origin': b'http://localhost:8000', b'sec-websocket-version': b'13', b'user-agent': b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', b'accept-encoding': b'gzip, deflate, br, zstd', b'accept-language': b'en-US,en;q=0.9', b'cookie': b'csrftoken=fwcqylKrW349A5ho7pe930W1MwJjzqtF', b'sec-gpc': b'1', b'sec-websocket-key': b'CeKSUVJR1AMt7FCDGkxf3Q==', b'sec-websocket-extensions': b'permessage-deflate; client_max_window_bits'}
2025-06-09 15:38:25 - Path: /ws/voicebot/websocket/
2025-06-09 15:38:25 - Query String: b''
2025-06-09 15:38:25 - User: AnonymousUser
2025-06-09 15:38:27 - === RECEIVED MESSAGE ===
2025-06-09 15:38:27 - Raw message: {"type":"start","callSid":"test_call_20250609223827470"}
2025-06-09 15:38:27 - === SESSION START ===
2025-06-09 15:38:31 - === RECEIVED MESSAGE ===
2025-06-09 15:38:31 - Raw message: {"type":"speech","callSid":"test_call_20250609223827470","transcript":"Hello, how are you today?"}
2025-06-09 15:38:31 - === HANDLE SPEECH ===
2025-06-09 15:40:25 - === NEW WEBSOCKET CONNECTION ===
2025-06-09 15:40:25 - Scope details:
2025-06-09 15:40:25 - Client: ['127.0.0.1', 52186]
2025-06-09 15:40:25 - Headers: {b'host': b'localhost:8001', b'connection': b'Upgrade', b'pragma': b'no-cache', b'cache-control': b'no-cache', b'upgrade': b'websocket', b'origin': b'http://localhost:8000', b'sec-websocket-version': b'13', b'user-agent': b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', b'accept-encoding': b'gzip, deflate, br, zstd', b'accept-language': b'en-US,en;q=0.9', b'cookie': b'csrftoken=fwcqylKrW349A5ho7pe930W1MwJjzqtF', b'sec-gpc': b'1', b'sec-websocket-key': b'NAF1+WHvXdENK9dQ15pX4g==', b'sec-websocket-extensions': b'permessage-deflate; client_max_window_bits'}
2025-06-09 15:40:25 - Path: /ws/voicebot/websocket/
2025-06-09 15:40:25 - Query String: b''
2025-06-09 15:40:25 - User: AnonymousUser
2025-06-09 15:40:29 - === RECEIVED MESSAGE ===
2025-06-09 15:40:29 - Raw message: {"type":"start","callSid":"test_call_20250609223827470"}
2025-06-09 15:40:29 - === SESSION START ===
2025-06-09 15:40:32 - === RECEIVED MESSAGE ===
2025-06-09 15:40:32 - Raw message: {"type":"speech","callSid":"test_call_20250609223827470","transcript":"Hello, how are you today?"}
2025-06-09 15:42:30 - === NEW WEBSOCKET CONNECTION ===
2025-06-09 15:42:30 - Scope details:
2025-06-09 15:42:30 - Client: ['127.0.0.1', 52197]
2025-06-09 15:42:30 - Headers: {b'host': b'localhost:8001', b'connection': b'Upgrade', b'pragma': b'no-cache', b'cache-control': b'no-cache', b'upgrade': b'websocket', b'origin': b'http://localhost:8000', b'sec-websocket-version': b'13', b'user-agent': b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', b'accept-encoding': b'gzip, deflate, br, zstd', b'accept-language': b'en-US,en;q=0.9', b'cookie': b'csrftoken=fwcqylKrW349A5ho7pe930W1MwJjzqtF', b'sec-gpc': b'1', b'sec-websocket-key': b'nz4/ZqQKbCDKdnkCEX31Jw==', b'sec-websocket-extensions': b'permessage-deflate; client_max_window_bits'}
2025-06-09 15:42:30 - Path: /ws/voicebot/websocket/
2025-06-09 15:42:30 - Query String: b''
2025-06-09 15:42:30 - User: AnonymousUser
2025-06-09 15:42:33 - === RECEIVED MESSAGE ===
2025-06-09 15:42:33 - Raw message: {"type":"speech","callSid":"test_call_20250609223827470","transcript":"Hello, how are you today?"}
2025-06-09 15:44:15 - === RECEIVED MESSAGE ===
2025-06-09 15:44:15 - Raw message: {
  "type": "speech",
  "callSid": "test_call_20250609223827470",
  "voicePrompt": "Hello, how are you today?"
}
2025-06-09 15:44:16 - === OPENAI!!!!dfdffdfdf!!! ===
