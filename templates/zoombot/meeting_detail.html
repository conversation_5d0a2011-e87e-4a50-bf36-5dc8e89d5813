{% extends "layouts/base.html" %}

{% block title %} Meeting Details {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Meeting Details</h4>
                    {% if meeting.status == 'scheduled' %}
                        <a href="{% url 'zoombot:meeting_join' meeting_id=meeting.id %}" class="ms-3">
                            <span class="badge rounded-pill bg-success-subtle text-success">
                                Join Meeting <i class="fas fa-sign-in-alt ms-2"></i>
                            </span>
                        </a>
                    {% endif %}
                    {% if meeting.status == 'completed' %}
                        <a href="{% url 'zoombot:meeting_summary' meeting_id=meeting.id %}" class="ms-3">
                            <span class="badge rounded-pill bg-info-subtle text-info">
                                View Summary <i class="fas fa-file-alt ms-2"></i>
                            </span>
                        </a>
                    {% endif %}
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:index' %}">
                                    Zoom Bot
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:meetings_list' %}">
                                    Meetings
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Details
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Meeting Information</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 30%">Meeting ID</th>
                                    <td>{{ meeting.meeting_id }}</td>
                                </tr>
                                <tr>
                                    <th>Topic</th>
                                    <td>{% if meeting.meeting_topic %}{{ meeting.meeting_topic }}{% else %}<em>No Topic</em>{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        {% if meeting.status == 'scheduled' %}
                                            <span class="badge bg-warning">Scheduled</span>
                                        {% elif meeting.status == 'in_progress' %}
                                            <span class="badge bg-success">In Progress</span>
                                        {% elif meeting.status == 'completed' %}
                                            <span class="badge bg-info">Completed</span>
                                        {% else %}
                                            <span class="badge bg-danger">Failed</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Start Time</th>
                                    <td>{% if meeting.start_time %}{{ meeting.start_time }}{% else %}<em>Not started</em>{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>End Time</th>
                                    <td>{% if meeting.end_time %}{{ meeting.end_time }}{% else %}<em>Not ended</em>{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>Duration</th>
                                    <td>{{ meeting.duration }} minutes</td>
                                </tr>
                                <tr>
                                    <th>Configuration</th>
                                    <td>
                                        <a href="{% url 'zoombot:config_detail' config_id=meeting.config.id %}">
                                            {{ meeting.config.name }}
                                        </a>
                                        <br>
                                        <small class="text-muted">Wake word: {{ meeting.config.wake_word }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Summary Recipients</th>
                                    <td>{% if meeting.summary_recipients %}{{ meeting.summary_recipients }}{% else %}<em>None</em>{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>Created</th>
                                    <td>{{ meeting.date_created }}</td>
                                </tr>
                                <tr>
                                    <th>Last Updated</th>
                                    <td>{{ meeting.date_updated }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Meeting Summaries</h4>
                </div>
                <div class="card-body">
                    {% if summaries %}
                        <div class="accordion" id="summaryAccordion">
                            {% for summary in summaries %}
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading{{ summary.id }}">
                                        <button class="accordion-button {% if not forloop.first %}collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ summary.id }}" aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}" aria-controls="collapse{{ summary.id }}">
                                            Summary #{{ forloop.counter }} - {{ summary.date_created }}
                                        </button>
                                    </h2>
                                    <div id="collapse{{ summary.id }}" class="accordion-collapse collapse {% if forloop.first %}show{% endif %}" aria-labelledby="heading{{ summary.id }}" data-bs-parent="#summaryAccordion">
                                        <div class="accordion-body">
                                            <p>{{ summary.summary_text }}</p>
                                            <div class="text-end">
                                                <small class="text-muted">
                                                    {% if summary.summary_sent %}
                                                        <i class="fas fa-check-circle text-success"></i> Sent to recipients
                                                    {% else %}
                                                        <i class="fas fa-times-circle text-danger"></i> Not sent
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <p>No summaries available for this meeting yet.</p>
                            {% if meeting.status == 'completed' %}
                                <a href="{% url 'zoombot:meeting_summary' meeting_id=meeting.id %}" class="btn btn-sm btn-info mt-2">
                                    Generate Summary
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">Meeting Transcript</h4>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" id="toggleWakeWordBtn">
                            <i class="fas fa-filter"></i> Show Wake Word Triggers Only
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if transcripts %}
                        <div class="table-responsive">
                            <table class="table table-hover" id="transcriptTable">
                                <thead>
                                    <tr>
                                        <th style="width: 15%">Time</th>
                                        <th style="width: 15%">Speaker</th>
                                        <th>Message</th>
                                        <th style="width: 10%">Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transcript in transcripts %}
                                        <tr class="{% if transcript.is_wake_word_trigger %}wake-word-row bg-light-success{% endif %}">
                                            <td>{{ transcript.timestamp }}</td>
                                            <td>{% if transcript.speaker_name %}{{ transcript.speaker_name }}{% else %}Unknown{% endif %}</td>
                                            <td>{{ transcript.content }}</td>
                                            <td>
                                                {% if transcript.speaker_type == 'human' %}
                                                    <span class="badge bg-primary">Human</span>
                                                {% elif transcript.speaker_type == 'ai' %}
                                                    <span class="badge bg-success">AI</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">System</span>
                                                {% endif %}
                                                {% if transcript.is_wake_word_trigger %}
                                                    <span class="badge bg-warning">Wake Word</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <p>No transcript available for this meeting yet.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const toggleWakeWordBtn = document.getElementById('toggleWakeWordBtn');
        const transcriptTable = document.getElementById('transcriptTable');
        let showWakeWordOnly = false;
        
        if (toggleWakeWordBtn && transcriptTable) {
            toggleWakeWordBtn.addEventListener('click', function() {
                showWakeWordOnly = !showWakeWordOnly;
                
                const rows = transcriptTable.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    if (showWakeWordOnly) {
                        if (!row.classList.contains('wake-word-row')) {
                            row.style.display = 'none';
                        }
                        toggleWakeWordBtn.innerHTML = '<i class="fas fa-filter"></i> Show All Messages';
                    } else {
                        row.style.display = '';
                        toggleWakeWordBtn.innerHTML = '<i class="fas fa-filter"></i> Show Wake Word Triggers Only';
                    }
                });
            });
        }
    });
</script>
{% endblock javascripts %}