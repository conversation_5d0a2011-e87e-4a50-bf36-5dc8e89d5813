{% extends "layouts/base.html" %}

{% block title %} Configuration Details {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Configuration Details</h4>
                    <a href="{% url 'zoombot:config_edit' config_id=config.id %}" class="ms-3">
                        <span class="badge rounded-pill bg-warning-subtle text-warning">
                            Edit Configuration <i class="fas fa-edit ms-2"></i>
                        </span>
                    </a>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:index' %}">
                                    Zoom Bot
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:config_list' %}">
                                    Configurations
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Details
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Configuration Information</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 30%">Name</th>
                                    <td>{{ config.name }}</td>
                                </tr>
                                <tr>
                                    <th>Wake Word</th>
                                    <td><code>{{ config.wake_word }}</code></td>
                                </tr>
                                <tr>
                                    <th>Description</th>
                                    <td>{% if config.description %}{{ config.description }}{% else %}<em>No description</em>{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        {% if config.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created</th>
                                    <td>{{ config.date_created }}</td>
                                </tr>
                                <tr>
                                    <th>Last Updated</th>
                                    <td>{{ config.date_updated }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3 d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'zoombot:config_list' %}" class="btn btn-secondary">Back to List</a>
                        <a href="{% url 'zoombot:config_edit' config_id=config.id %}" class="btn btn-warning">Edit Configuration</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">Using This Configuration</h4>
                    <a href="{% url 'zoombot:meeting_create' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> New Meeting
                    </a>
                </div>
                <div class="card-body">
                    <p>To use this configuration in a Zoom meeting:</p>
                    <ol>
                        <li>Go to <a href="{% url 'zoombot:meeting_create' %}">Create Meeting</a></li>
                        <li>Enter the Zoom meeting details</li>
                        <li>Select this configuration from the dropdown</li>
                        <li>Click "Connect to Meeting"</li>
                    </ol>
                    <p>When the bot joins the meeting, it will listen for the wake word <code>{{ config.wake_word }}</code> and respond to requests.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Meetings Using This Configuration</h4>
                </div>
                <div class="card-body">
                    {% if meetings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Meeting ID</th>
                                        <th>Topic</th>
                                        <th>Status</th>
                                        <th>Start Time</th>
                                        <th>End Time</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for meeting in meetings %}
                                        <tr>
                                            <td>{{ meeting.meeting_id }}</td>
                                            <td>{% if meeting.meeting_topic %}{{ meeting.meeting_topic }}{% else %}<em>No Topic</em>{% endif %}</td>
                                            <td>
                                                {% if meeting.status == 'scheduled' %}
                                                    <span class="badge bg-warning">Scheduled</span>
                                                {% elif meeting.status == 'in_progress' %}
                                                    <span class="badge bg-success">In Progress</span>
                                                {% elif meeting.status == 'completed' %}
                                                    <span class="badge bg-info">Completed</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Failed</span>
                                                {% endif %}
                                            </td>
                                            <td>{% if meeting.start_time %}{{ meeting.start_time }}{% else %}-{% endif %}</td>
                                            <td>{% if meeting.end_time %}{{ meeting.end_time }}{% else %}-{% endif %}</td>
                                            <td>
                                                <a href="{% url 'zoombot:meeting_detail' meeting_id=meeting.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if meeting.status == 'scheduled' %}
                                                    <a href="{% url 'zoombot:meeting_join' meeting_id=meeting.id %}" class="btn btn-sm btn-success">
                                                        <i class="fas fa-sign-in-alt"></i> Join
                                                    </a>
                                                {% endif %}
                                                {% if meeting.status == 'completed' %}
                                                    <a href="{% url 'zoombot:meeting_summary' meeting_id=meeting.id %}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-file-alt"></i> Summary
                                                    </a>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <p>No meetings are using this configuration yet. <a href="{% url 'zoombot:meeting_create' %}">Create a meeting</a> to get started.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
{% endblock javascripts %}