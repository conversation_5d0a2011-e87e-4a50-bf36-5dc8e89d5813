{% extends "layouts/base.html" %}

{% block title %} Edit Configuration {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Edit Configuration</h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:index' %}">
                                    Zoom Bot
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:config_list' %}">
                                    Configurations
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:config_detail' config_id=config.id %}">
                                    Details
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Edit
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Configuration Details</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ config.name }}" required>
                            <div class="form-text">Enter a descriptive name for this configuration</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="wake_word" class="form-label">Wake Word <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="wake_word" name="wake_word" value="{{ config.wake_word }}" required>
                            <div class="form-text">Enter the phrase that will trigger the bot to listen for a request</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ config.description }}</textarea>
                            <div class="form-text">Enter a description for this configuration (optional)</div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if config.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">Active</label>
                            <div class="form-text">If checked, this configuration can be used for meetings</div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'zoombot:config_detail' config_id=config.id %}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">About Wake Words</h4>
                </div>
                <div class="card-body">
                    <p>The wake word is the phrase that triggers the bot to listen for a request. When someone in the meeting says the wake word, the bot will process everything that follows as a request to OpenAI.</p>
                    <p>Tips for choosing a good wake word:</p>
                    <ul>
                        <li>Choose a phrase that is unlikely to be said accidentally during normal conversation</li>
                        <li>Choose a phrase that is easy to remember and pronounce</li>
                        <li>Choose a phrase that is distinct from other wake words used by other devices</li>
                    </ul>
                    <p>The default wake word is "Hey Edith", but you can change it to anything you prefer.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger-subtle">
                    <h4 class="card-title text-danger">Danger Zone</h4>
                </div>
                <div class="card-body">
                    <p class="text-danger">Changing the configuration will affect all meetings that use this configuration. If you change the wake word, users will need to use the new wake word in all future meetings.</p>
                    
                    <p>If you want to create a new configuration instead of modifying this one, you can <a href="{% url 'zoombot:config_create' %}">create a new configuration</a>.</p>
                    
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">Active Meetings</h5>
                        <p>If there are any active meetings using this configuration, changes will take effect immediately.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
{% endblock javascripts %}