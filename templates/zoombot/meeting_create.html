{% extends "layouts/base.html" %}

{% block title %} Create Zoom Meeting {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Connect to Zoom Meeting</h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:index' %}">
                                    Zoom Bot
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:meetings_list' %}">
                                    Meetings
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Connect
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Meeting Details</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="meeting_id" class="form-label">Meeting ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="meeting_id" name="meeting_id" required>
                            <div class="form-text">Enter the Zoom meeting ID (e.g., 123 456 7890)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="meeting_password" class="form-label">Meeting Password</label>
                            <input type="text" class="form-control" id="meeting_password" name="meeting_password">
                            <div class="form-text">Enter the meeting password if required</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="meeting_topic" class="form-label">Meeting Topic</label>
                            <input type="text" class="form-control" id="meeting_topic" name="meeting_topic">
                            <div class="form-text">Enter a descriptive topic for this meeting</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="config_id" class="form-label">Bot Configuration <span class="text-danger">*</span></label>
                            <select class="form-select" id="config_id" name="config_id" required>
                                <option value="">Select a configuration</option>
                                {% for config in configs %}
                                    <option value="{{ config.id }}">{{ config.name }} (Wake word: {{ config.wake_word }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Select the bot configuration to use for this meeting</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="summary_recipients" class="form-label">Summary Recipients</label>
                            <input type="text" class="form-control" id="summary_recipients" name="summary_recipients">
                            <div class="form-text">Enter email addresses separated by commas to receive the meeting summary</div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'zoombot:meetings_list' %}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Connect to Meeting</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">How It Works</h4>
                </div>
                <div class="card-body">
                    <p>When you connect to a Zoom meeting:</p>
                    <ol>
                        <li>The bot will join the meeting using the provided meeting ID and password.</li>
                        <li>It will listen for the wake word specified in the selected configuration.</li>
                        <li>When someone says the wake word, the bot will process the request using OpenAI.</li>
                        <li>The bot will record the entire meeting transcript for later reference.</li>
                        <li>After the meeting ends, the bot will generate a summary using OpenAI.</li>
                        <li>The summary will be sent to the specified email addresses.</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
{% endblock javascripts %}