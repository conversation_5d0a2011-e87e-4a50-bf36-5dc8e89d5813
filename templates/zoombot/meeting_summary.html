{% extends "layouts/base.html" %}

{% block title %} Meeting Summary {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Meeting Summary</h4>
                    <a href="{% url 'zoombot:meeting_detail' meeting_id=meeting.id %}" class="ms-3">
                        <span class="badge rounded-pill bg-primary-subtle text-primary">
                            View Meeting Details <i class="fas fa-eye ms-2"></i>
                        </span>
                    </a>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:index' %}">
                                    Zoom Bot
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:meetings_list' %}">
                                    Meetings
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:meeting_detail' meeting_id=meeting.id %}">
                                    Details
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Summary
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Meeting Information</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th>Meeting ID</th>
                                    <td>{{ meeting.meeting_id }}</td>
                                </tr>
                                <tr>
                                    <th>Topic</th>
                                    <td>{% if meeting.meeting_topic %}{{ meeting.meeting_topic }}{% else %}<em>No Topic</em>{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        {% if meeting.status == 'scheduled' %}
                                            <span class="badge bg-warning">Scheduled</span>
                                        {% elif meeting.status == 'in_progress' %}
                                            <span class="badge bg-success">In Progress</span>
                                        {% elif meeting.status == 'completed' %}
                                            <span class="badge bg-info">Completed</span>
                                        {% else %}
                                            <span class="badge bg-danger">Failed</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Start Time</th>
                                    <td>{% if meeting.start_time %}{{ meeting.start_time }}{% else %}<em>Not started</em>{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>End Time</th>
                                    <td>{% if meeting.end_time %}{{ meeting.end_time }}{% else %}<em>Not ended</em>{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>Duration</th>
                                    <td>{{ meeting.duration }} minutes</td>
                                </tr>
                                <tr>
                                    <th>Recipients</th>
                                    <td>{% if meeting.summary_recipients %}{{ meeting.summary_recipients }}{% else %}<em>None</em>{% endif %}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">Meeting Summary</h4>
                    {% if summary %}
                        <div>
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="generate_summary" value="true">
                                <button type="submit" class="btn btn-sm btn-primary">
                                    <i class="fas fa-sync-alt"></i> Regenerate Summary
                                </button>
                            </form>
                            {% if not summary.summary_sent and meeting.summary_recipients %}
                                <form method="post" class="d-inline">
                                    {% csrf_token %}
                                    <input type="hidden" name="send_summary" value="true">
                                    <button type="submit" class="btn btn-sm btn-success ms-2">
                                        <i class="fas fa-envelope"></i> Send to Recipients
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if summary %}
                        <div class="summary-content">
                            <p class="text-muted small">Generated on {{ summary.date_created }}</p>
                            <div class="p-3 bg-light rounded">
                                {{ summary.summary_text|linebreaks }}
                            </div>
                            {% if summary.summary_sent %}
                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-check-circle"></i> This summary has been sent to the recipients.
                                </div>
                            {% elif meeting.summary_recipients %}
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-exclamation-circle"></i> This summary has not been sent to the recipients yet.
                                </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <p>No summary has been generated for this meeting yet.</p>
                            {% if meeting.status == 'completed' %}
                                <form method="post" class="mt-3">
                                    {% csrf_token %}
                                    <input type="hidden" name="generate_summary" value="true">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-magic"></i> Generate Summary
                                    </button>
                                </form>
                            {% else %}
                                <p class="mt-2">The meeting must be completed before a summary can be generated.</p>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {% if summary %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">How It Works</h4>
                </div>
                <div class="card-body">
                    <p>The meeting summary is generated using OpenAI's language model, which analyzes the meeting transcript to identify key points, decisions, and action items.</p>
                    <p>The summary includes:</p>
                    <ul>
                        <li>Main topics discussed</li>
                        <li>Key decisions made</li>
                        <li>Action items and assignments</li>
                        <li>Important dates and deadlines</li>
                    </ul>
                    <p>You can regenerate the summary at any time, or send it to the specified recipients via email.</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
{% endblock javascripts %}