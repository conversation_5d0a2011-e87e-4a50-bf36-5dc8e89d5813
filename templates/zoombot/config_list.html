{% extends "layouts/base.html" %}

{% block title %} Zoom Bot Configurations {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Zoom Bot Configurations</h4>
                    <a href="{% url 'zoombot:config_create' %}" class="ms-3">
                        <span class="badge rounded-pill bg-secondary-subtle text-secondary">
                            Create New Configuration <i class="fas fa-plus ms-2"></i>
                        </span>
                    </a>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:index' %}">
                                    Zoom Bot
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Configurations
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">All Configurations</h4>
                </div>
                <div class="card-body">
                    {% if configs %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Wake Word</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for config in configs %}
                                        <tr>
                                            <td>{{ config.name }}</td>
                                            <td><code>{{ config.wake_word }}</code></td>
                                            <td>{% if config.description %}{{ config.description }}{% else %}<em>No description</em>{% endif %}</td>
                                            <td>
                                                {% if config.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ config.date_created }}</td>
                                            <td>{{ config.date_updated }}</td>
                                            <td>
                                                <a href="{% url 'zoombot:config_detail' config_id=config.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'zoombot:config_edit' config_id=config.id %}" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <p>You don't have any bot configurations yet. <a href="{% url 'zoombot:config_create' %}">Create a configuration</a> to get started.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">About Configurations</h4>
                </div>
                <div class="card-body">
                    <p>Zoom Bot configurations allow you to customize how the bot behaves in meetings:</p>
                    <ul>
                        <li><strong>Wake Word:</strong> The phrase that triggers the bot to listen for a request (e.g., "Hey Edith")</li>
                        <li><strong>Status:</strong> Whether the configuration is active and can be used for meetings</li>
                    </ul>
                    <p>You can create multiple configurations with different wake words for different types of meetings.</p>
                </div>
            </div>
        </div>
    </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
{% endblock javascripts %}