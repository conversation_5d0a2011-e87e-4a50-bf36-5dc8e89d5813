{% extends "layouts/base.html" %}

{% block title %} Zoom Meetings {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Zoom Meetings</h4>
                    <a href="{% url 'zoombot:meeting_create' %}" class="ms-3">
                        <span class="badge rounded-pill bg-secondary-subtle text-secondary">
                            Connect to Meeting <i class="fas fa-plus ms-2"></i>
                        </span>
                    </a>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="{% url 'zoombot:index' %}">
                                    Zoom Bot
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Meetings
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">All Meetings</h4>
                </div>
                <div class="card-body">
                    {% if meetings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Meeting ID</th>
                                        <th>Topic</th>
                                        <th>Status</th>
                                        <th>Start Time</th>
                                        <th>End Time</th>
                                        <th>Duration</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for meeting in meetings %}
                                        <tr>
                                            <td>{{ meeting.meeting_id }}</td>
                                            <td>{% if meeting.meeting_topic %}{{ meeting.meeting_topic }}{% else %}No Topic{% endif %}</td>
                                            <td>
                                                {% if meeting.status == 'scheduled' %}
                                                    <span class="badge bg-warning">Scheduled</span>
                                                {% elif meeting.status == 'in_progress' %}
                                                    <span class="badge bg-success">In Progress</span>
                                                {% elif meeting.status == 'completed' %}
                                                    <span class="badge bg-info">Completed</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Failed</span>
                                                {% endif %}
                                            </td>
                                            <td>{% if meeting.start_time %}{{ meeting.start_time }}{% else %}-{% endif %}</td>
                                            <td>{% if meeting.end_time %}{{ meeting.end_time }}{% else %}-{% endif %}</td>
                                            <td>{{ meeting.duration }} min</td>
                                            <td>
                                                <a href="{% url 'zoombot:meeting_detail' meeting_id=meeting.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if meeting.status == 'scheduled' %}
                                                    <a href="{% url 'zoombot:meeting_join' meeting_id=meeting.id %}" class="btn btn-sm btn-success">
                                                        <i class="fas fa-sign-in-alt"></i> Join
                                                    </a>
                                                {% endif %}
                                                {% if meeting.status == 'completed' %}
                                                    <a href="{% url 'zoombot:meeting_summary' meeting_id=meeting.id %}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-file-alt"></i> Summary
                                                    </a>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <p>You don't have any meetings yet. <a href="{% url 'zoombot:meeting_create' %}">Connect to a meeting</a> to get started.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
{% endblock javascripts %}