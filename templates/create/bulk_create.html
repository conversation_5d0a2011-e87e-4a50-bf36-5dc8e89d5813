{% extends "layouts/base.html" %}

{% block title %} Bulk Article Creation {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheetspre %}
    <link href="/static/assets/vendor/jquery-smartwizard/src/css/smart_wizard.css" rel="stylesheet">
    <link href="/static/assets/vendor/bootstrap-tagsinput/css/bootstrap-tagsinput.css" rel="stylesheet">

    <link href="/static/assets/vendor/clockpicker/css/bootstrap-clockpicker.min.css" rel="stylesheet">
    <link href="/static/assets/vendor/jquery-asColorPicker/css/asColorPicker.min.css" rel="stylesheet">
    <link href="/static/assets/vendor/bootstrap-material-datetimepicker/css/bootstrap-material-datetimepicker.css" rel="stylesheet">
{% endblock stylesheetspre %}
{% block stylesheets %}
    <link href="/static/assets/vendor/tourguidejs/tour.min.css" rel="stylesheet">
    <style>
    .form-wizard .tab-content {
        height: 100% !important;
    }
    .bootstrap-select .dropdown-menu {
        max-height: 200px !important;
    }
    </style>
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}

    <!--
    <div class="row">
        <div class="col-xl-12">
            <div class="page-titles">
                <div class="d-flex align-items-center flex-wrap ">
                    <h3 class="heading">Add A Twitter Task</h3>
                </div>
            </div>
        </div>
    </div>
    -->


    <div class="row">
        <div class="col-xl-12 col-xxl-12">



            <div class="card">


                <div class="card-header">
                    <h4 class="card-title">
                        Bulk Article Creator <i class="las la-info-circle template-block ms-2 text-primary" id="run-tutorial"></i>
                    </h4>
                </div>


                <form method="post" id="create-articles-form">
                {% csrf_token %}
                <div class="card-body">
                    <div id="smartwizard" class="form-wizard order-create">
                        <ul class="nav nav-wizard">
                            <li>
                                <a class="nav-link" href="#wizard_type" id="wizard_type_link">
                                    <span>1</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link" href="#wizard_source" id="wizard_source_link">
                                    <span>2</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link" href="#wizard_details" id="wizard_details_link">
                                    <span>3</span>
                                </a>
                            </li>
                            <li>
                                <a class="nav-link" href="#wizard_schedule" id="wizard_schedule_link">
                                    <span>4</span>
                                </a>
                            </li>
                        </ul>
                        <div class="tab-content">


                            {# ################################### #}
                            {# ################################### #}
                            {# ####### Task Type          ######## #}
                            {# ################################### #}
                            {# ################################### #}
                            <div id="wizard_type" class="tab-pane" role="tabpanel">

                                <div class="row">
                                    <div class="col-12">
                                        <div class="skip-email text-center">
                                            <p>What topic/information should these articles cover?  Please be as detailed as possible.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row emial-setup mb-5">
                                    
                                    <div class="col-lg-3 col-sm-3 col-3">
                                    </div>
                                    <div class="col-lg-6 col-sm-6 col-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="description">Topic</label>
                                            <textarea class="form-control" rows="7" id="description" name="description" placeholder="Topic of your articles..."></textarea>
                                        </div>
                                    </div>
                                    
                                </div>

                            </div>


                            {# ################################### #}
                            {# ################################### #}
                            {# ####### Task Source        ######## #}
                            {# ################################### #}
                            {# ################################### #}
                            <div id="wizard_source" class="tab-pane" role="tabpanel">



                                <div class="row">
                                    <div class="col-12">
                                        <div class="skip-email text-center">
                                            <p>Please add keywords for the AI to create posts for. One keyword per line.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row emial-setup mb-5">
                                    
                                    <div class="col-lg-3 col-sm-3 col-3">
                                    </div>
                                    <div class="col-lg-6 col-sm-6 col-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="keywords">Keywords</label>
                                            <textarea class="form-control" rows="7" id="keywords" name="keywords" placeholder="One keyword per line..."></textarea>
                                        </div>
                                    </div>
                                    
                                </div>


                            </div>


                            {# #################################### #}
                            {# #################################### #}
                            {# ####### Task source details ######## #}
                            {# #################################### #}
                            {# #################################### #}
                            <div id="wizard_details" class="tab-pane" role="tabpanel">



                                <div class="row emial-setup mb-5 height400" id="article-counts-options">
                                    <!--<div class="col-12">
                                        <div class="skip-email text-center">
                                            <p>
                                                Should we include AI generated images with your posts?<br />
                                                Each image will consume 100 credits, per image, per post.<br />
                                                10 posts a day, 2 images per post, equals 2,000 credits
                                            </p>
                                        </div>
                                    </div>-->
                                    <div class="col-lg-2 col-sm-6 col-6">
                                        
                                    </div>
                                    <div class="col-lg-4 col-sm-6 col-6 text-center">
                                        <p>
                                            How many articles should the AI write for you?
                                            Each article will consume credits, at 1 credit per word.
                                            Creating 10 articles, at 500 words per, equals 5,000 credits.
                                        </p>
                                        <div class="mb-3" id="article-count-section">
                                            <select class="default-select form-control wide mb-3 form-control-lg" id="article_count" name="article_count">
                                                {% for article_count in article_counts %}
                                                <option value="{{ article_count.0 }}">Create {{ article_count.1 }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-sm-6 col-6 text-center">
                                        <p>
                                            How long should your articles be? Counts are approximate.
                                            This feature will consume 1 credit per word.
                                            Creating 10 articles, at 500 words, equals 5,000 credits.
                                        </p>
                                        <div class="mb-3" id="word-count-section">
                                            <select class="default-select form-control wide mb-3 form-control-lg" id="word_count" name="word_count">
                                                {% for word_count in word_counts %}
                                                <option value="{{ word_count.0 }}">{{ word_count.1 }} Per Article</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                
                                
                                
                                </div>
                            
                            

                            </div>


















                            {# ################################### #}
                            {# ################################### #}
                            {# ####### Task schedule      ######## #}
                            {# ################################### #}
                            {# ################################### #}
                            <div id="wizard_schedule" class="tab-pane" role="tabpanel">
                                <div class="col-12">
                                    <div class="skip-email text-center">
                                        <p>
                                            Choose the tone you would like the AI to take when creating your articles.
                                        </p>
                                    </div>
                                </div>

                                <div class="row mb-100 height400" id="article-details-options">
                                
                                    <div class="col-lg-2 col-sm-6 col-6">
                                        
                                    </div>
                                
                                    <div class="col-lg-4 col-sm-6 col-6">
                                        <div class="mb-0">
                                            <span id="article-tone-section">
                                                <label class="form-label mb-1">Writing Tone</label>
                                                <div class="input-group mb-3">
                                                    <select name="writing_tone" class="form-control">
                                                    {% for tone in tones %}
                                                        <option value="{{ tone.0 }}">{{ tone.1 }}</option>
                                                    {% endfor %}
                                                    </select>
                                                </div>
                                            </span>
                                        </div>
                                    </div>

                                
                                    <div class="col-lg-4 col-sm-6 col-6 mb-2">
                                        <div class="mb-0">
                                            
                                            <span id="name-section">
                                                <label class="form-label mb-1" for="name">Name your article batch</label>
                                                <div class="input-group mb-3">
                                                    <input type="text" class="form-control" value="" name="name" id="name">
                                                </div>
                                            </span>
                                            
                                        </div>
                                    </div>
                                
                                
                                    <div class="col-lg-12 mb-2">
                                        <div class="mb-3 text-center">
                                        
                                            <button type="button" class="btn btn-primary me-2 mt-3" id="submit-button"{% if not can_add_task %} xxdisabled{% endif %}>
                                                Create Articles Now
                                                <span class="ms-2">
                                                    <i class="fas fa-plus-circle" id="submit-button-icon"></i>
                                                </span>
                                            </button>
                                        
                                        </div>
                                    </div>
                                
                                </div>

                            </div>
                        
                        </div>
                    </div>

                </div>
                
                </form>
            </div>
        
        </div>
    </div>





{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/vendor/jquery-smartwizard/dist/js/jquery.smartWizard.js"></script>
    <script src="/static/assets/vendor/bootstrap-tagsinput/js/bootstrap-tagsinput.min.js"></script>

    <!-- momment js is must -->
    <script src="/static/assets/vendor/moment/moment.min.js"></script>
    <script src="/static/assets/vendor/bootstrap-daterangepicker/daterangepicker.js"></script>
    <!-- clockpicker -->
    <script src="/static/assets/vendor/clockpicker/js/bootstrap-clockpicker.min.js"></script>
    <script src="/static/assets/vendor/bootstrap-material-datetimepicker/js/bootstrap-material-datetimepicker.js"></script>
    <script src="/static/assets/vendor/pickadate/picker.js"></script>
    <script src="/static/assets/vendor/pickadate/picker.time.js"></script>
    <script src="/static/assets/vendor/pickadate/picker.date.js"></script>
    <script src="/static/assets/js/plugins-init/clock-picker-init.js"></script>
    <script src="/static/assets/vendor/tourguidejs/tour.js"></script>
    <script>
		$(document).ready(function() {

			// SmartWizard initialize
			$('#smartwizard').smartWizard();
            $('.sw-btn-next').eq(0).attr('id', 'wizard-next-button');

            //
            const tourEndClick = '#wizard_type_link';
            const tourHideBack = true;
            {% include 'includes/tour_script.html' %}

            /*
             *
             */
            function setWorking(working=true) {
                if(working) {
                    $('#submit-button').attr('disabled', true);
                    $('#submit-button-icon').removeClass('fa-plus-circle');
                    $('#submit-button-icon').addClass('fa-spinner fa-spin');
                } else {
                    $('#submit-button').attr('disabled', false);
                    $('#submit-button-icon').addClass('fa-plus-circle');
                    $('#submit-button-icon').removeClass('fa-spinner fa-spin');
                }
            }
            function showError() {
                setWorking(false);
                Swal.fire(
                    'Error',
                    'Please complete the form before generating articles.',
                    'error'
                )
            }
            function pollUntilComplete(id) {
                return new Promise((resolve, reject) => {
                $.ajax({
                    url: '/create/api/create_bulk_poll/' + id + '/?format=json',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        console.log('response');
                        console.log(response);
                        console.log(response.is_completed);
                        if (response.is_completed) {
                            console.log('Completed!');
                            resolve({
                                keep_waiting: false,
                                articles: response
                            });
                        } else {
                            console.log('Not yet completed... Retrying...');
                            resolve( {
                                keep_waiting: true,
                                articles: response
                            });
                        }
                    },
                    error: function(error) {
                        resolve( {
                            keep_waiting: true,
                            articles: null
                        });
                    }
                });
                });
            }
            function startPolling(id) {
                return new Promise(resolve => {
                    const poll = () => {
                        pollUntilComplete(id).then(response => {
                            if (response.keep_waiting) {
                                setTimeout(poll, 10000);
                            } else {
                                resolve(response);
                            }
                        });
                    };
            
                    poll();
                });
            }
            
            $('#submit-button').on('click', function() {
                setWorking(true);

                if( !$('#keywords').val() ) {
                    showError();
                    return false;
                }
                if( !$('#description').val() ) {
                    showError();
                    return false;
                }
                if( !$('#name').val() ) {
                    showError();
                    return false;
                }

                Swal.fire({
                    title: 'Create articles?',
                    html: `Are you sure you want to create articles?<br />This process will take some time.`,
                    type: 'info',
                    showCancelButton: true,
                    confirmButtonText: 'Create Articles',
                    showLoaderOnConfirm: true,
                    preConfirm: (name) => {
                        setWorking(true);

                        let bulk_db = null;
                        let form = $("#create-articles-form");
                        const formData = new FormData();
                        $.each(form.serializeArray(), function (_, kv) {
                            formData.append(kv.name, kv.value);
                        });
                        formData.append('csrfmiddlewaretoken', $('input[name=csrfmiddlewaretoken]').val());

                        return fetch('{% url 'create_api_create_bulk' %}', {
                            method: "POST",
                            body: formData
                        }).then(async response => {
                            let Json = function (data) {
                                return data;
                            }
                            let returnData = await Json(response.json());
                            if (!response.ok) {
                                throw new Error(returnData.message);
                            } else {
                                console.log('response.id');
                                console.log(response);
                                console.log('returnData');
                                console.log(returnData);
                                console.log(returnData.id);
                                await startPolling(returnData.id).then(pollResponse => {
                                    // The polling is over. Handle your response object here.
                                    console.log('pollResponse.articles.article_file')
                                    console.log(pollResponse)
                                    console.log(pollResponse.articles)
                                    console.log(pollResponse.articles.article_file)
                                    console.log(pollResponse.articles.created_date)
                                    bulk_db = pollResponse.articles;
                                    return pollResponse.articles;
                                });
                                return bulk_db;
                            }
                        }).catch(error => {
                            console.log(error);
                            Swal.showValidationMessage(error);
                        })
                    },
                    allowOutsideClick: () => !Swal.isLoading()
                }).then(async (result) => {
                    console.log('result');
                    console.log(result);
                    if (result.value.created_date) {
                        const {value: go} = await Swal.fire({
                            title: 'Success',
                            html: `Your articles were successfully created.<br />You will now be redirected to the results.</u></a>`,
                            type: 'success'
                        });
                        if (go) {
                            window.location.href = "/create/view_documents/" + result.value.content_folder_id + "/";
                        }
                    }
                    
                    setWorking(false);
                });

                console.log('Submit');

            });

		});
	</script>
{% endblock javascripts %}