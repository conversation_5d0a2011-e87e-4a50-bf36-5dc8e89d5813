{% extends "layouts/base.html" %}

{% block title %} Create Content {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link href="/static/assets/vendor/tourguidejs/tour.min.css" rel="stylesheet">
    <style>
    #editor-toolbar {
        margin-left: 10px;
        margin-top: 10px;
    }
    </style>
{% endblock stylesheets %}

{% block content-body %}mh-auto{% endblock content-body %}
{% block content-fluid %}p-0{% endblock content-fluid %}
{% block content %}


                <!-- row -->
                <div class="row" id="tour-document-start">
                    <div class="col-lg-12">
                        <div class="card rounded-0 mb-0 h-auto">
                            <div class="card-body p-0 ">
								<div class="row gx-0">

                                    {% if not document.chat %}
									<!--column-->
									<div class="col-lg-2 col-xl-2 col-xxl-2">
										<div class="email-left-box dlab-scroll pt-3" id="email-left">
											<div class="p-0">
												<span class="btn text-white btn-block" id="tour-templates" style="background-color: #00273a !important;">
                                                    <!-- <i class="fa-solid fa-plus me-2"></i> -->
                                                    Templates
                                                </span>
											</div>
											<div class="mail-list rounded mt-3" id="tour-template-list">
                                                {% for template in templates %}
												<a href="/create/document/{{ template.pk }}/" class="list-group-item{% if active_template.pk == template.pk %} active{% endif %}">
                                                    <i class="{{ template.icon }} align-middle px-0"></i>
                                                    {{ template.name }}<!-- <strong>{{ template.buzz_word }}</strong> -->
                                                    {% if template.is_standard == True and template.is_starter == False %}
                                                        <i class="la la-star"></i>
                                                    {% elif template.is_pro == True and template.is_starter == False %}
                                                        <i class="la la-star"></i>
                                                    {% elif template.is_platinum == True and template.is_starter == False %}
                                                        <i class="la la-star"></i>
                                                    {% endif %}
                                                </a>
                                                {% endfor %}
											</div>
										</div>
									</div>
									<!--/column-->
                                    {% endif %}


                                    {% if not document.chat %}
									<div class="col-lg-4 col-xl-4 col-xxl-4">
										<div class="email-right-box ms-0 mt-3 ">
											<div class="compose-wrapper " id="compose-content">
												<div class="compose-content">
                                                    <h4>Template: {{ active_template.name }}</h4>
                                                    <hr />
													<form id="create-content-form">
                                                        {% csrf_token %}
                                                        <input type="hidden" name="is_form" value="y">
                                                        {% for variable in active_template_variables %}
                                                            <h5 class="mt-3 mb-0">
                                                                {{ variable.name }}
                                                            </h5>
                                                            {% if variable.dropdown_choices_list %}
                                                                <div class="mb-3">
                                                                    <select class="default-select form-control form-select-lg" data-control="select2"
                                                                    data-allow-clear="false" data-hide-search="true"
                                                                           id="variable_{{ variable.token }}"
                                                                           name="variable_{{ variable.token }}">
                                                                        {% for choice in variable.dropdown_choices_list %}
                                                                            <option value="{{ choice }}">{{ choice|title }}</option>
                                                                        {% endfor %}
                                                                    </select>
                                                                </div>
                                                            {% else %}
                                                                {% if variable.is_text_area == True %}
                                                                <div class="mb-3">
                                                                    <textarea
                                                                       id="variable_{{ variable.token }}"
                                                                       name="variable_{{ variable.token }}"
                                                                       class="textarea_editor form-control bg-transparent"
                                                                       rows="5"
                                                                       placeholder="{{ variable.description }}"></textarea>
                                                                </div>
                                                                {% else %}
                                                                <div class="mb-3">
                                                                    <input type="text"
                                                                           class="form-control bg-transparent"
                                                                           id="variable_{{ variable.token }}"
                                                                           name="variable_{{ variable.token }}"
                                                                           placeholder="{{ variable.description }}">
                                                                </div>
                                                                {% endif %}
                                                            {% endif %}
                                                        {% endfor %}
													</form>
												</div>
												<div class="text-start mt-4 mb-3">
													<button class="btn btn-primary btn-sl-sm me-2"{% if document %} disabled{% endif %}
                                                            id="create-content-button" type="button">
                                                        <span class="me-2">
                                                            <i class="fas fa-magic" id="create-content-icon"></i>
                                                        </span>
                                                        Create Content
                                                    </button>
												</div>
											</div>
										</div>
									</div>
                                    {% endif %}







                                    {% if not document.chat %}
									<div class="col-lg-6 col-xl-6 col-xxl-6 ps-3" id="tour-document-section">
                                    {% else %}
                                    <div class="col-lg-2 col-xl-2 col-xxl-2"></div>
									<div class="col-lg-8 col-xl-8 col-xxl-8" id="tour-document-section">
                                    {% endif %}
                                        <div class="ck-editor-wrapper">
                                        
                                            <div id="editor-toolbar">
                                            
                                                <button class="ck-button-custom ck-button-custom-start mt-0 ms-0 mb-2" id="editor-button-rewrite">
                                                    <i class="fa-regular fas fa-redo-alt align-middle"></i>
                                                    Re-write
                                                </button>
                                                <button class="ck-button-custom mt-0 mb-2" id="editor-button-expand">
                                                    <i class="fa-regular fas fa-expand-arrows-alt align-middle"></i>
                                                    Expand
                                                </button>
                                                <button class="ck-button-custom mt-0 mb-2" id="editor-button-add">
                                                    <i class="fa-regular fas fa-reply-all align-middle"></i>
                                                    Insert Content
                                                </button>
    
                                            
                                                <button class="ck-button-custom mt-0 mb-2" id="editor-button-copyscape">
                                                    CopyScape
                                                    <i class="fa-regular fas fa-check align-middle"></i>
                                                </button>
    
    
                                                <button class="ck-button-custom mt-0" id="editor-button-spin">
                                                    Spin
                                                    <i class="fa-regular fas fa-sync-alt align-middle"></i>
                                                </button>


                                                <div class="btn-group" role="group">
                                                    <button class="ck-button-custom dropdown-toggle mt-0" id="artwork-button" data-bs-toggle="dropdown">
                                                        <i class="fa-regular fas fa-palette align-middle"></i>
                                                        Insert Image
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        <div class="pre-scrollable">
                                                        {%  if artwork %}
                                                            {% for art in artwork %}
                                                            <a class="dropdown-item add-image" href="javascript:void(0);" data-id="{{ art.pk }}" data-file="{{ art.art_file }}">{{ art.name }}</a>
                                                            {% endfor %}
                                                        {% else %}
                                                            <span class="ms-3">No Artwork Found</span>
                                                        {% endif %}
                                                        </div>
                                                    </div>
                                                </div>



                                            
                                                <button class="ck-button-custom mt-0" id="editor-button-help">
                                                    <i class="fa-regular fas fa-info-circle align-middle"></i>
                                                </button>
                                            
                                            </div>
                                            






                                            <div id="ckeditor">{% if document.text %}{{ document.text|safe }}{% endif %}</div>
                                        </div>
                                        <div class="text-start mt-3 mb-3 me-4">

                                            <input type="hidden" value="{% if document %}{{ document.pk }}{% endif %}" id="document-id">

                                            <div class="row" id="tour-save-section">
                                                <div class="col-sm-5">
                                                    <input type="text" class="form-control w-100" placeholder="Document name"
                                                           id="editor-document-name" value="{% if document %}{{ document.name }}{% endif %}">
                                                </div>
                                                <div class="col-sm-4 mt-2 mt-sm-0" id="save-folder-dropdown">
                                                    <select class="default-select form-control wide w-100 h-100" id="editor-folder-select">
                                                        <option value=""{% if document %}{% if document.content_folder.pk == 0 %} selected{% endif %}{% endif %}>Default Folder</option>
                                                        {% for folder in folders %}
                                                        <option value="{{ folder.pk }}"
                                                            {% if document %}{% if document.content_folder.pk == folder.pk %} selected{% endif %}{% endif %}>
                                                            {{ folder.name }}
                                                        </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                                <div class="col-sm-3 mt-2 mt-sm-0">
                                                    <button class="btn btn-primary w-100 h-100" id="editor-button-save" type="button">
                                                        <i class="fas fa-save"></i>
                                                        Save
                                                    </button>
                                                </div>
                                            </div>

                                        </div>
                                    </div>







								</div>


                            </div>
                        </div>
                    </div>
                </div>



{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/vendor/ckeditor/ckeditor.js"></script>
    <script src="/static/assets/vendor/tourguidejs/tour.js"></script>
    <script>
    $(document).ready(function() {
        const canUseProFeatures = {% if user.subscription_type.pk > 1 %}true{% else %}false{% endif %};

        {% include 'includes/tour_script.html' %}
    
        /*
         *
         */
        $('.add-image').on('click', function(e) {
            const fileName = $(this).data('file');
            const id = $(this).data('id');
            // replaceSelectedText('<img src="/get_image/' + fileName + '" border="0" style="float: right; width: 50%;">');
            const html = '<img src="/get_image/' + fileName + '" width="50%" style="float: right; width: 50%;">';
            const viewFragment = editor.data.processor.toView( html );
            const modelFragment = editor.data.toModel( viewFragment );
            editor.model.insertContent(modelFragment);
        });
















        /*
         * Returns the selected/highlighted text from the document editor.
         */
        function getSelectedText() {
            const range = editor.model.document.selection.getFirstRange();
            let selectedText = '';
            for (const item of range.getItems()) {
                if (typeof item === 'undefined' || typeof item.data === 'undefined') {
                    continue;
                }

                console.log('Range: ' + item.data);
                selectedText = item.data;
                console.log('Bong: ' + selectedText);
            }
            return selectedText;
        }

        /*
         * Replaces or inserts text into the document editor.
         * If text is selected/highlighted, it will replace it.
         * If none is selected, it will place the text at the mouse location.
         */
        function replaceSelectedText(text) {
            editor.model.change(writer => {
                const range = editor.model.document.selection.getFirstRange();
                editor.model.insertContent(writer.createText(text), range);
                //editor.model.insertContent(writer.createText(text), range);
                //editor.model.insertContent(writer.createHTML(text), range);
            });
        }

        /*
         * API call helper function.  Dials up the API to create content.
         */
        function callCreateContentAPI(formData, promptID, isForm=false) {
            formData['csrfmiddlewaretoken'] = '{{ csrf_token }}';
            $.ajax({
                url: '/create/api/create_content/' + promptID + '/',
                type: 'POST',
                data: formData,
                success: function(result)
                {
                    console.log(result);
                    if(isForm) {
                        //$('#ckeditor').html(result.text);
                        editor.data.set(result.text);
                    } else {
                        replaceSelectedText(result.text);
                    }
                    Swal.fire('Success!', 'Your content has been created.', 'success' );
                },
                error: function(result)
                {
                    if(result.responseJSON.out_of_credits) {
                        outOfCredits();
                    } else {
                        Swal.fire(
                            "Error", "There was an error creating your content. Please try again later.", "error"
                        );
                    }
                },
                complete: function(data) {
                    popWorkingOverlay(false);
                }
            });
        }

        /*
         * Saving the created document to the server.
         */
        $('#editor-button-save').on('click', function() {
            if(!$('#editor-document-name').val()) {
                Swal.fire("Error", "Please enter in the document name.", "error");
                return;
            }
            if(!editor.getData()) {
                Swal.fire("Error", "It's an empty document!", "error");
                return;
            }
            let actionName = 'created';
            if($('#document-id').val()) {
                actionName = 'updated';
            }
            formData = {
                'csrfmiddlewaretoken': '{{ csrf_token }}',
                'name': $('#editor-document-name').val(),
                'folder_id': $('#editor-folder-select').val(),
                'text': editor.getData(),
                'document_id': $('#document-id').val(),
                'template_id': {% if not document.chat %}{{ active_template.pk }}{% else %}0{% endif %}
            };
            console.log(formData);
            popWorkingOverlay(true);
            $.ajax({
                url: '/create/api/create_document/',
                type: 'POST',
                data: formData,
                success: function(result) {
                    $('#document-id').val(result.id);
                    Swal.fire('Success!', 'Your document has been ' + actionName + '.', 'success' );
                },
                error: function (response) {
                    Swal.fire(
                        "Error",
                        "There was an error " + actionName + " your document. Please try again later.",
                        "error"
                    );
                },
                complete: function(data) {
                    popWorkingOverlay(false);
                }
            });
        });

        /*
         * The editor 'Expand Content' button.
         * It will ask the user for a topic to write about,
         * then create a paragraph about that topic,
         * and insert it at the mouse position.
         */
        $('#editor-button-expand').on('click', function() {
            console.log('Button hit expand...');
            let selectedText = getSelectedText();
            if(selectedText.length > 0) {

                console.log('Found...' + selectedText);
                Swal.fire({
                    title: 'Are you sure?',
                    text: 'Would you like to have the system expand only the text you highlighted?',
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, expand it!'
                }).then((result) => {
                    console.log('Success!');
                    console.log(result);
                    if (result.value == true) {
                        popWorkingOverlay(true);
                        callCreateContentAPI({'variable_text': selectedText}, '57');
                    }
                });
            } else {
                Swal.fire("Error", "Please select/highlight the text you want to expand.", "error");
            }
        });

        /*
         * The editor 'Insert Content' button.
         * It will ask the user for a topic to write about,
         * then create a paragraph about that topic,
         * and insert it at the mouse position.
         */
        $('#editor-button-add').on('click', async function () {
            console.log('Button hit add...');
            const {value: text} = await Swal.fire({
                title: 'Add a paragraph',
                text: 'What should this content be about?',
                type: 'question',
                input: 'textarea',
                inputPlaceholder: 'What should this content be about?',
                inputAttributes: {
                    'aria-label': 'What should this content be about?',
                    min: 8,
                    max: 120,
                    step: 1
                },
                showCancelButton: true
            });
            console.log(typeof text);
            if (text !== undefined) {
                if (text.length !== 0) {
                    popWorkingOverlay(true);
                    callCreateContentAPI({'variable_text': text}, '61');
                } else {
                    Swal.fire("Error", "Please enter in the topic you'd like to have written about.", "error");
                }
            }
        });

        /*
         * The editor 'Re-write Content' button.
         * It will take the highlighted/selected text and have it re-written,
         * then replace it with the new text.
         */
        $('#editor-button-rewrite').on('click', function() {
            console.log('Button hit re-write...');
            let selectedText = getSelectedText();
            if(selectedText.length > 0) {
                console.log('Found...'+selectedText);
                Swal.fire({
                    title: 'Are you sure?',
                    text: "Would you like to have the system re-write only the text you highlighted?",
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, re-write it!'
                }).then((result) => {
                    console.log('Success!');
                    console.log(result);
                    if (result.value == true) {
                        popWorkingOverlay(true);
                        callCreateContentAPI({'variable_text': selectedText}, '59');
                    } else {
                        popWorkingOverlay(false);
                    }
                });
            } else {
                Swal.fire("Error", "Please select/highlight the text you want to re-write.", "error");
            }
        });


        /*
         * This uses the template & form to create the bulk of the content of the document.
         */
        $('#create-content-button').on('click', function() {
            let noError = true;

            // Fields are dynamic so let's make sure we have everythign we need.
            // All mandatory fields start with the 'variable_' prefix.
            $('[id^=variable_]').each(function(key, value) {
                if(!$(this).val()) {
                    Swal.fire("Error", "Please fill out all fields", "error");
                    noError = false;
                }
            });

            if(noError) {
                popWorkingOverlay(true);
                callCreateContentAPI($('#create-content-form').serialize(), '{{ active_template.pk }}', true);
                $('#create-content-button').prop("disabled", true);
            }
        });





        /*
         * Copyscape
         */
        $('#editor-button-copyscape').on('click', function() {
            if(!canUseProFeatures) {
                proFeatureOnly();
                return;
            }
            {% if user.copyscape_username and user.copyscape_key %}
                const copyscape = true;
            {% else %}
                const copyscape = false;
            {% endif %}
            if(!copyscape) {
                Swal.fire("Error", "Please add your Copyscape account to your profile to use this feature.", "error");
                return;
            }
            popWorkingOverlay(true);
            $.ajax({
                url: '/create/api/copyscape_document/',
                type: 'POST',
                data: {text:editor.getData(), csrfmiddlewaretoken:'{{ csrf_token }}'},
                success: function (result) {
                    console.log(result);
                    Swal.fire({
                        title: 'CopyScape Results',
                        type: 'info',
                        html: result.copyscape
                    })
                },
                error: function (response) {
                    Swal.fire(
                        "Error",
                        "There was an error getting the CopyScape data. Please try again later.",
                        "error"
                    );
                },
                complete: function(data) {
                    popWorkingOverlay(false);
                }
            });

        });





        /*
         *
         */
        $('#editor-button-spin').on('click', async function () {
            if(!canUseProFeatures) {
                proFeatureOnly();
                return;
            }
            if(!editor.getData()) {
                Swal.fire("Error", "It's an empty document!", "error");
                return;
            }

            const {value: versions} = await Swal.fire({
                title: 'How many versions would you like?',
                icon: 'question',
                input: 'range',
                inputLabel: 'Versions',
                inputAttributes: {
                    min: 1,
                    max: 10,
                    step: 1
                },
                inputValue: 5
            });
            if (versions) {
                console.log('Copies: ' + versions);
                popWorkingOverlay(true);
                $.ajax({
                    url: '/create/api/spin_document/',
                    type: 'POST',
                    data: {text:editor.getData(), versions:versions, csrfmiddlewaretoken:'{{ csrf_token }}'},
                    success: async function (result) {
                        $('#document-id').val(result.id);
                        await Swal.fire(
                            'Success!',
                            'Your documents have been created and your download will start shortly.',
                            'success'
                        );
                        location.href = result.url;
                    },
                    error: function (response) {
                        Swal.fire(
                            "Error",
                            "There was an error creating your documents. Please try again later.",
                            "error"
                        );
                    },
                    complete: function(data) {
                        popWorkingOverlay(false);
                    }
                });
            }

        });



    });
	</script>

{% endblock javascripts %}