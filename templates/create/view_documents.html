{% extends "layouts/base.html" %}

{% block title %} Word Genius Chat {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link href="/static/assets/css/documents.css" rel="stylesheet">
    <link href="/static/assets/vendor/tourguidejs/tour.min.css" rel="stylesheet">
    <style>
    .folder-structure {
        height: calc(100vh - 35rem) !important;
    }
    </style>
{% endblock stylesheets %}

{% block content-body %}mh-auto{% endblock content-body %}
{% block content-fluid %}p-0 mh-auto{% endblock content-fluid %}
{% block content %}

                <div class="card p-0 rounded-0 file_area mb-0 default-tab">
					<div class="row gx-0">
						<!--column-->
						<div class="col-xl-3 col-xxl-4 col-lg-4 col-md-5">
							<div class="email-left-box border-end  dlab-scroll" id="email-left">
								<div class="p-0">
									<span class="btn text-white btn-block text-left ps-3" style="background-color: #00273a !important;">
                                        <i class="fa-solid fa-folder-open me-3"></i>Your Folders
                                    </span>
								</div>
								<div class="mail-list rounded" role="tablist" id="tour-folder-list">
									<a href="#" data-folder="0" id="folder-link-0"
                                       class="list-group-item {% if not folder_pk %}active{% endif %} folder-link">
                                        <i class="fa-regular fa-folder align-middle"></i>
                                        Default Folder
                                    </a>
                                    {% for folder in folders %}
									<a href="#" id="folder-link-{{ folder.pk }}"
                                       data-folder="{{ folder.pk }}" class="list-group-item folder-link{% if folder_pk and folder.pk == folder_pk %} active{% endif %}">
                                        <i class="fa-regular fa-folder align-middle"></i>
                                        {{ folder.name }}
                                    </a>
                                    {% endfor %}

								</div>
                                <hr class="mt-4" />
								<div class="mail-list rounded overflow-hidden mt-4" id="create-folder-form">
									<div class="intro-title d-flex justify-content-between my-0 px-1 py-2">
										<h5>Create New Folder</h5>
									</div>
									<div class="basic-form">
                                        <form method="post" id="add-folder-form">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="add-folder">
                                            <div class="mb-3 row">
                                                <div class="col-sm-12">
                                                    <input type="name" name="name" class="form-control" placeholder="Folder Name">
                                                </div>
                                            </div>
                                            <div class="mb-3 row">
                                                <div class="col-sm-12">
                                                    <button type="submit" class="btn btn-primary w-100">
                                                        <i class="fas fa-plus-circle me-2 ms-0"></i>
                                                        Add Folder
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
								</div>
							</div>
						</div>
						<!--/column-->
						<!--column-->
						<div class="col-xl-9 col-xxl-8 col-lg-8 col-md-7">
							<!--row-->
							<div class="row gx-0">
								<!--column-->
								<div class="col-xl-12">
									<div class="file-header">
										<div class="card-body py-0">
											<div class="page-titles">
												<div class="mt-1">
													<h2 class="heading">Folder Content</h2>
												</div>
                                                <div class="d-flex my-2">
                                                    
													<div class="d-flex align-items-center">
                                                        
														<button class="btn sharp btn-primary me-2" id="run-tutorial-button" data-toggle="tooltip" data-placement="bottom" title="Help Tutorial">
                                                            <i class="fas fa-info-circle"></i>
                                                        </button>
                                                        
                                                        <div class="dropdown custom-dropdown">
                                                            <div class="btn sharp btn-primary p-3 me-4" id="download-all-button" data-bs-toggle="dropdown" data-toggle="tooltip" data-placement="bottom" title="Click to download all articles">
                                                                <i class="fa fa-download"></i>
                                                            </div>
                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                <a class="dropdown-item" id="download-all-button-doc" href="/create/download/documents/{% if folder_pk %}{{ folder_pk }}{% else %}0{% endif %}/doc/"><i class="fas fa-file-word"></i> Download folder documents as DOC Files</a>
                                                                <a class="dropdown-item" id="download-all-button-pdf" href="/create/download/documents/{% if folder_pk %}{{ folder_pk }}{% else %}0{% endif %}/pdf/"><i class="fas fa-file-pdf"></i> Download folder documents as PDF Files</a>
                                                                <a class="dropdown-item" id="download-all-button-txt" href="/create/download/documents/{% if folder_pk %}{{ folder_pk }}{% else %}0{% endif %}/txt/"><i class="fas fa-file-alt"></i> Download folder documents as TXT Files</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    
                                                    
													<div class="input-group search-area">
														<input type="text" class="form-control" id="document-search-input" placeholder="Search here...">
														<span class="input-group-text">
                                                            <a href="javascript:void(0)">
                                                                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path opacity="0.3" d="M16.6751 19.4916C16.2194 19.036 16.2194 18.2973 16.6751 17.8417C17.1307 17.3861 17.8694 17.3861 18.325 17.8417L22.9916 22.5084C23.4473 22.964 23.4473 23.7027 22.9916 24.1583C22.536 24.6139 21.7973 24.6139 21.3417 24.1583L16.6751 19.4916Z" fill="white"/>
                                                                    <path d="M12.8333 18.6667C16.055 18.6667 18.6667 16.055 18.6667 12.8334C18.6667 9.61169 16.055 7.00002 12.8333 7.00002C9.61166 7.00002 6.99999 9.61169 6.99999 12.8334C6.99999 16.055 9.61166 18.6667 12.8333 18.6667ZM12.8333 21C8.323 21 4.66666 17.3437 4.66666 12.8334C4.66666 8.32303 8.323 4.66669 12.8333 4.66669C17.3436 4.66669 21 8.32303 21 12.8334C21 17.3437 17.3436 21 12.8333 21Z" fill="white"/>
                                                                </svg>
															</a>
														</span>
													</div>
													<div class="d-flex align-items-center folder-layout-tab" id="list-buttons">
														<div class="feature-btn">
															<svg width="24" height="24" fill="none" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
															    <path d="m7.1971 7.5684h-1.8203-2.8809c-0.23438 0-0.47852 0.01953-0.71289-0.00586 0.06836 0.00977 0.13867 0.01953 0.20703 0.02734-0.0625-0.00976-0.11914-0.02343-0.17774-0.04687 0.0625 0.02539 0.125 0.05273 0.1875 0.07812-0.05468-0.02539-0.10547-0.05468-0.15429-0.09179 0.05273 0.04101 0.10547 0.08203 0.1582 0.12304-0.03906-0.0332-0.07422-0.06836-0.10742-0.10742 0.04101 0.05274 0.08203 0.10547 0.12304 0.1582-0.0371-0.04882-0.0664-0.09765-0.09179-0.15429 0.02539 0.0625 0.05273 0.125 0.07812 0.1875-0.02343-0.0586-0.03906-0.11524-0.04687-0.17774 0.00976 0.06836 0.01953 0.13868 0.02734 0.20703-0.01953-0.19335-0.00586-0.39257-0.00586-0.58593v-1.2871-2.9082c0-0.23438-0.01953-0.47852 0.00586-0.7129-0.00976 0.06836-0.01953 0.13868-0.02734 0.20704 0.00976-0.0625 0.02344-0.11914 0.04687-0.17774-0.02539 0.0625-0.05273 0.125-0.07812 0.1875 0.02539-0.05469 0.05469-0.10547 0.09179-0.1543-0.04101 0.05274-0.08203 0.10547-0.12304 0.15821 0.0332-0.03907 0.06836-0.07422 0.10742-0.10742-0.05273 0.04101-0.10547 0.08203-0.1582 0.12304 0.04882-0.03711 0.09765-0.0664 0.15429-0.09179-0.0625 0.02539-0.125 0.05273-0.1875 0.07812 0.0586-0.02344 0.11524-0.03906 0.17774-0.04687-0.06836 0.00976-0.13867 0.01953-0.20703 0.02734 0.19336-0.01953 0.39257-0.00586 0.58593-0.00586h1.2871 2.9082c0.23437 0 0.47851-0.01953 0.71289 0.00586-0.06836-0.00977-0.13867-0.01953-0.20703-0.02734 0.0625 0.00976 0.11914 0.02343 0.17773 0.04687-0.0625-0.02539-0.125-0.05273-0.1875-0.07812 0.05469 0.02539 0.10547 0.05468 0.1543 0.09179-0.05274-0.04101-0.10547-0.08203-0.15821-0.12304 0.03907 0.0332 0.07422 0.06835 0.10743 0.10742-0.04102-0.05274-0.08203-0.10547-0.12305-0.15821 0.03711 0.04883 0.06641 0.09766 0.0918 0.1543-0.02539-0.0625-0.05274-0.125-0.07813-0.1875 0.02344 0.0586 0.03906 0.11524 0.04688 0.17774-0.00977-0.06836-0.01953-0.13868-0.02735-0.20704 0.01953 0.19336 0.00586 0.39258 0.00586 0.58594v1.2871 2.9082c0 0.23438 0.01953 0.47852-0.00586 0.71289 0.00977-0.06835 0.01953-0.13867 0.02735-0.20703-0.00977 0.0625-0.02344 0.11914-0.04688 0.17774 0.02539-0.0625 0.05274-0.125 0.07813-0.1875-0.02539 0.05469-0.05469 0.10547-0.0918 0.15429 0.04102-0.05273 0.08203-0.10546 0.12305-0.1582-0.03321 0.03906-0.06836 0.07422-0.10743 0.10742 0.05274-0.04101 0.10547-0.08203 0.15821-0.12304-0.04883 0.03711-0.09766 0.0664-0.1543 0.09179 0.0625-0.02539 0.125-0.05273 0.1875-0.07812-0.05859 0.02344-0.11523 0.03906-0.17773 0.04687 0.06836-0.00976 0.13867-0.01953 0.20703-0.02734-0.02539 0.00391-0.05274 0.00586-0.08008 0.00586-0.20312 0.00391-0.4082 0.08398-0.55273 0.22852-0.13477 0.13476-0.23829 0.35742-0.22852 0.55273 0.01953 0.41601 0.34375 0.79101 0.78125 0.78125 0.38281-0.00781 0.75-0.15039 1.0215-0.42383 0.26563-0.26562 0.40626-0.62109 0.42188-0.99414 0.00195-0.06445 0-0.12695 0-0.19141v-1.1387-3.2402c0-0.25977 0.00391-0.51953 0-0.7793-0.00781-0.53125-0.29883-1.0684-0.79297-1.2969-0.21875-0.10157-0.41601-0.15625-0.66211-0.15821h-0.08594-2.6777-2.5176c-0.39453 0-0.78125 0.14063-1.0625 0.42383-0.2832 0.2832-0.42383 0.66797-0.42383 1.0664v0.91601 3.252 1.0273 0.08398c0.001953 0.24415 0.058594 0.44532 0.1582 0.66211 0.22852 0.49414 0.76562 0.78516 1.2969 0.79297 0.78125 0.00977 1.5625 0 2.3438 0h2.8144 0.16407c0.4082 0 0.80078-0.35937 0.78125-0.78125-0.01563-0.42578-0.33985-0.7832-0.7793-0.7832zm0 3.3007h-2.1426-2.9414c-0.08007 0-0.16015-0.0019-0.24023 0-0.63281 0.0098-1.1641 0.4004-1.377 0.9922-0.10742 0.2969-0.076172 0.6524-0.076172 0.9649v3.1386 1.334 0.3125c0 0.4063 0.15234 0.8184 0.46094 1.0938 0.29102 0.2597 0.64453 0.3887 1.0332 0.3887h0.34375 1.3555 3.1152 0.38281c0.27735 0 0.53711-0.0508 0.78711-0.1817 0.4668-0.2422 0.74219-0.7617 0.7461-1.2773v-0.2676-2.9668-1.4805c0-0.2265 0.01562-0.4609-0.00586-0.6875-0.03125-0.3594-0.16602-0.709-0.4375-0.959-0.27735-0.2558-0.62305-0.3964-1.0039-0.4043-0.4082-0.0078-0.80078 0.3653-0.78125 0.7813 0.01953 0.4297 0.34375 0.7715 0.78125 0.7812 0.02734 0 0.05469 2e-3 0.08203 0.0059-0.06836-0.0098-0.13867-0.0195-0.20703-0.0273 0.0625 0.0097 0.11914 0.0234 0.17773 0.0468-0.0625-0.0254-0.125-0.0527-0.1875-0.0781 0.05469 0.0254 0.10547 0.0547 0.1543 0.0918-0.05273-0.041-0.10547-0.082-0.1582-0.123 0.03906 0.0332 0.07422 0.0683 0.10742 0.1074-0.04102-0.0528-0.08203-0.1055-0.12305-0.1582 0.03711 0.0488 0.06641 0.0976 0.0918 0.1543-0.02539-0.0625-0.05273-0.125-0.07813-0.1875 0.02344 0.0586 0.03907 0.1152 0.04688 0.1777-0.00977-0.0684-0.01953-0.1387-0.02734-0.207 0.01953 0.1933 0.00586 0.3926 0.00586 0.5859v1.2871 2.9082c0 0.2344 0.01953 0.4785-0.00586 0.7129 0.00976-0.0683 0.01953-0.1387 0.02734-0.207-0.00977 0.0625-0.02344 0.1191-0.04688 0.1777 0.0254-0.0625 0.05274-0.125 0.07813-0.1875-0.02539 0.0547-0.05469 0.1055-0.0918 0.1543 0.04102-0.0527 0.08203-0.1055 0.12305-0.1582-0.0332 0.0391-0.06836 0.0742-0.10742 0.1074 0.05273-0.041 0.10547-0.082 0.1582-0.123-0.04883 0.0371-0.09765 0.0664-0.1543 0.0918 0.0625-0.0254 0.125-0.0528 0.1875-0.0781-0.05859 0.0234-0.11523 0.039-0.17773 0.0468 0.06836-0.0097 0.13867-0.0195 0.20703-0.0273-0.19336 0.0195-0.39258 0.0058-0.58594 0.0058h-1.2871-2.9082c-0.23437 0-0.47852 0.0196-0.71289-0.0058 0.06836 0.0098 0.13867 0.0195 0.20703 0.0273-0.0625-0.0097-0.11914-0.0234-0.17773-0.0468 0.0625 0.0253 0.125 0.0527 0.1875 0.0781-0.05469-0.0254-0.10547-0.0547-0.1543-0.0918 0.05273 0.041 0.10547 0.082 0.1582 0.123-0.03906-0.0332-0.07422-0.0683-0.10742-0.1074 0.04102 0.0527 0.08203 0.1055 0.12305 0.1582-0.03711-0.0488-0.06641-0.0976-0.0918-0.1543 0.02539 0.0625 0.05274 0.125 0.07813 0.1875-0.02344-0.0586-0.03907-0.1152-0.04688-0.1777 0.00977 0.0683 0.01953 0.1387 0.02734 0.207-0.01953-0.1933-0.00585-0.3926-0.00585-0.5859v-1.2871-2.9082c0-0.2344-0.01954-0.4785 0.00585-0.7129-0.00976 0.0683-0.01953 0.1386-0.02734 0.207 0.00977-0.0625 0.02344-0.1191 0.04688-0.1777-0.02539 0.0625-0.05274 0.125-0.07813 0.1875 0.02539-0.0547 0.05469-0.1055 0.0918-0.1543-0.04102 0.0527-0.08203 0.1054-0.12305 0.1582 0.0332-0.0391 0.06836-0.0742 0.10742-0.1074-0.05273 0.041-0.10547 0.082-0.1582 0.123 0.04883-0.0371 0.09766-0.0664 0.1543-0.0918-0.0625 0.0254-0.125 0.0527-0.1875 0.0781 0.05859-0.0234 0.11523-0.039 0.17773-0.0468-0.06836 0.0097-0.13867 0.0195-0.20703 0.0273 0.19336-0.0195 0.39258-0.0059 0.58594-0.0059h1.2598 2.8965 0.67187c0.40821 0 0.80079-0.3593 0.78125-0.7812-0.01953-0.4238-0.34375-0.7813-0.7832-0.7813zm3.5039-6.8691h0.8066 1.9356 2.332 2.0254c0.3281 0 0.6563 0.00586 0.9844 0h0.0137c0.4082 0 0.8007-0.35938 0.7812-0.78125-0.0195-0.42383-0.3437-0.78125-0.7812-0.78125h-0.8067-1.9355-2.3321-2.0253c-0.3282 0-0.6563-0.00586-0.9844 0h-0.0137c-0.4082 0-0.80077 0.35938-0.78124 0.78125 0.01758 0.42187 0.34374 0.78125 0.78124 0.78125zm5.0313 2.0391h-1.7012-2.711-0.621c-0.4082 0-0.80083 0.35938-0.7813 0.78125 0.01954 0.42383 0.3438 0.78125 0.7813 0.78125h1.7011 2.711 0.6211c0.4082 0 0.8007-0.35937 0.7812-0.78125-0.0176-0.42383-0.3418-0.78125-0.7812-0.78125zm-5.0313 7.9218h0.8066 1.9356 2.332 2.0254c0.3281 0 0.6563 0.0059 0.9844 0h0.0137c0.4082 0 0.8007-0.3593 0.7812-0.7812-0.0195-0.4238-0.3437-0.7813-0.7812-0.7813h-0.8067-1.9355-2.3321-2.0253c-0.3282 0-0.6563-0.0058-0.9844 0h-0.0137c-0.4082 0-0.80077 0.3594-0.78124 0.7813 0.01758 0.4238 0.34374 0.7812 0.78124 0.7812zm5.0313 2.0391h-1.7012-2.711-0.621c-0.4082 0-0.80083 0.3594-0.7813 0.7812 0.01954 0.4239 0.3438 0.7813 0.7813 0.7813h1.7011 2.711 0.6211c0.4082 0 0.8007-0.3594 0.7812-0.7813-0.0176-0.4218-0.3418-0.7812-0.7812-0.7812z" fill="#00273a"/>
															</svg>
														</div>
														<div class="feature-btn">
															<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																<path d="M9 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V9C3 9.53043 3.21071 10.0391 3.58579 10.4142C3.96086 10.7893 4.46957 11 5 11H9C9.53043 11 10.0391 10.7893 10.4142 10.4142C10.7893 10.0391 11 9.53043 11 9V5C11 4.46957 10.7893 3.96086 10.4142 3.58579C10.0391 3.21071 9.53043 3 9 3ZM5 9V5H9V9H5ZM19 3H15C14.4696 3 13.9609 3.21071 13.5858 3.58579C13.2107 3.96086 13 4.46957 13 5V9C13 9.53043 13.2107 10.0391 13.5858 10.4142C13.9609 10.7893 14.4696 11 15 11H19C19.5304 11 20.0391 10.7893 20.4142 10.4142C20.7893 10.0391 21 9.53043 21 9V5C21 4.46957 20.7893 3.96086 20.4142 3.58579C20.0391 3.21071 19.5304 3 19 3ZM15 9V5H19V9H15ZM9 13H5C4.46957 13 3.96086 13.2107 3.58579 13.5858C3.21071 13.9609 3 14.4696 3 15V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H9C9.53043 21 10.0391 20.7893 10.4142 20.4142C10.7893 20.0391 11 19.5304 11 19V15C11 14.4696 10.7893 13.9609 10.4142 13.5858C10.0391 13.2107 9.53043 13 9 13ZM5 19V15H9V19H5ZM19 13H15C14.4696 13 13.9609 13.2107 13.5858 13.5858C13.2107 13.9609 13 14.4696 13 15V19C13 19.5304 13.2107 20.0391 13.5858 20.4142C13.9609 20.7893 14.4696 21 15 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V15C21 14.4696 20.7893 13.9609 20.4142 13.5858C20.0391 13.2107 19.5304 13 19 13ZM15 19V15H19V19H15Z" fill="#00273a"></path>
															</svg>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<!--/column-->
								<!--column-->
							<div class="col-xl-12 tab-content">
                            
                                {% if documents %}
                                <ul class="folder-structure dlab-scroll" id="folder">
                                    {% for document in documents %}
                                        <li class="folder-item in-folder-{% if not document.content_folder %}0{% else %}{{ document.content_folder.pk }}{% endif %} {% if folder_pk %}{% if document.content_folder.pk != folder_pk %}hidden{% endif %}{% else %}hidden{% endif %}"
                                         data-toggle="tooltip" data-placement="bottom" title="{{ document.name }}">
                                            <div class="basic-dropdown">
	                                            <div class="dropdown">
                                                    <div class="file-list dropdown-toggle filelist-basic-dropdown"  data-bs-toggle="dropdown">
                                                        <div class="dz-media">
                                                            <svg width="35" height="40" viewBox="0 0 59 81" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                {% if document.chat %}
                                                                <path d="M53.1365 80H5.86371C3.13985 80 0.90918 77.7693 0.90918 75.0455V5.95453C0.90918 3.23067 3.13985 1 5.86371 1H38.3839L58.091 20.7071V75.0455C58.091 77.7693 55.8604 80 53.1365 80Z" fill="#D0E9FF" stroke="#1B87FF"></path>
                                                                <path d="M38.5911 0.5L58.5911 20.5H38.5911V0.5Z" fill="#1B87FF"></path>
                                                                <path d="M38.5911 0.5L58.5911 20.5H38.5911V0.5Z" stroke="#1B87FF"></path>
                                                                <path d="m29.5,62.87732c9.66438,0 17.5,-6.91211 17.5,-15.43866s-7.83563,-15.43866 -17.5,-15.43866s-17.5,6.91211 -17.5,15.43866c0,3.88172 1.62531,7.43261 4.30938,10.1454c-0.21219,2.24081 -0.91219,4.69776 -1.68656,6.54158c-0.17281,0.41023 0.16188,0.86898 0.59719,0.7984c4.935,-0.81604 7.86844,-2.06878 9.14375,-2.72162a19.81875,19.98204 0 0 0 5.13625,0.67489z" fill="#1B87FF"></path>
                                                                {% else %}
                                                                <path d="M53.1365 80H5.86371C3.13985 80 0.90918 77.7693 0.90918 75.0455V5.95453C0.90918 3.23067 3.13985 1 5.86371 1H38.3839L58.091 20.7071V75.0455C58.091 77.7693 55.8604 80 53.1365 80Z" fill="#D0E9FF" stroke="#1B87FF"/>
                                                                <path d="M38.5911 0.5L58.5911 20.5H38.5911V0.5Z" fill="#1B87FF"/>
                                                                <path d="M38.5911 0.5L58.5911 20.5H38.5911V0.5Z" stroke="#1B87FF"/>
                                                                <path d="M13.1365 31.4092H45.8638V35.9546H13.1365V31.4092Z" fill="#1B87FF"/>
                                                                <path d="M13.1365 31.4092H45.8638V35.9546H13.1365V31.4092Z" stroke="#1B87FF"/>
                                                                <path d="M13.1365 42.3184H45.8638V46.8638H13.1365V42.3184Z" fill="#1B87FF"/>
                                                                <path d="M13.1365 42.3184H45.8638V46.8638H13.1365V42.3184Z" stroke="#1B87FF"/>
                                                                <path d="M13.1365 53.2275H36.7729V57.773H13.1365V53.2275Z" fill="#1B87FF"/>
                                                                <path d="M13.1365 53.2275H36.7729V57.773H13.1365V53.2275Z" stroke="#1B87FF"/>
                                                                {% endif %}
                                                            </svg>
                                                        </div>
                                                        <div class="dz-info">
                                                            <!--<a href="/create/document/{% if document.chat %}0{% else %}{{ document.content_template.pk }}{% endif %}/{{ document.pk }}/">-->
                                                                <h4 class="title">
                                                                    <!--{{ document.name }}-->
                                                                    {{ document.name|slice:20 }}{% if document.name|length|get_digit:"-1" > 20 %}...{% endif %}
                                                                </h4>
                                                                <span>{{ document.updated_date|date:'F d, Y - g:i A' }}</span>
                                                            <!--</a>-->
                                                        </div>
                                                    </div>

                                                    <div class="dropdown-menu dropdown-menu-left">
                                                        <a class="dropdown-item" href="/create/document/{% if document.chat %}0{% else %}{{ document.content_template.pk }}{% endif %}/{{ document.pk }}/"><i class="fas fa-edit"></i> Edit Document</a>
                                                        <!--
                                                        <hr class="my-2" />
                                                        <a class="dropdown-item add-doc-brain" href="javascript:;" data-id="{{ document.pk }}"><i class="fas fa-brain"></i> Add to Digital Brain</a>
                                                        -->
                                                        <hr class="my-2" />
                                                        <a class="dropdown-item" href="/create/download/document/{{ document.pk }}/doc/"><i class="fas fa-file-word"></i> Download DOC File</a>
                                                        <a class="dropdown-item" href="/create/download/document/{{ document.pk }}/pdf/"><i class="fas fa-file-pdf"></i> Download PDF File</a>
                                                        <a class="dropdown-item" href="/create/download/document/{{ document.pk }}/txt/"><i class="fas fa-file-alt"></i> Download TXT File</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    {% endfor %}
                                </ul>
                                {% else %}
                                    <!--
                                    <h1 class="mx-4 mt-3">No documents yet.  Get creating!</h1>
                                    -->








                                                    <div class="card m-5">
                                                        <div class="">
                                                            <div class="prot-blog">
                                                                <div class="d-flex post justify-content-between mb-3 align-items-center">
                                                                    <h2 class="text d-inline mb-3">
                                                                        No documents yet.  Here is how you can add some.
                                                                    </h2>
                                                                </div>

                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">Add a document with a template</h2>
                                                                    <a href="{% url 'create_view_templates' %}">Click to use a template <i class="fas fa-arrow-circle-right ms-1"></i></a>
                                                                </div>
                                                                <p class="mb-5">Allows you to choose a template that will guide you while creating.</p>


                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">Add a document creating content</h2>
                                                                    <a href="{% url 'create_document' '1' %}">Click to use content creator <i class="fas fa-info-circle ms-1"></i></a>
                                                                </div>
                                                                <p class="mb-5">Allows you to creat a document with the content creator.</p>


                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">Use SEO Assistant Chat</h2>
                                                                    <a href="{% url 'view_chat_new' %}">Click to use SEO Assistant Chat <i class="fas fa-arrow-circle-right ms-1"></i></a>
                                                                </div>
                                                                <p class="mb-3">Use the AI chat to create new documents.</p>




                                                                <div class="shape">
                                                                    <svg width="488" height="353" viewBox="0 0 488 353" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <mask id="mask0_51_1209" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="438" height="283">
                                                                            <rect width="438" height="283" fill="url(#paint0_linear_51_1209)"></rect>
                                                                        </mask>
                                                                        <g mask="url(#mask0_51_1209)">
                                                                            <path d="M165 410.5H15L465.5 88H487.5L165 410.5Z" fill="#ccecff"></path>
                                                                            <path d="M264 369.5H114L564.5 47H586.5L264 369.5Z" fill="#ccecff"></path>
                                                                        </g>
                                                                        <defs>
                                                                            <linearGradient id="paint0_linear_51_1209" x1="308.075" y1="-143.042" x2="316.634" y2="468.334" gradientUnits="userSpaceOnUse">
                                                                                <stop offset="0" stop-color="#363B64"></stop>
                                                                                <stop offset="1" stop-color="#4CBC9A"></stop>
                                                                            </linearGradient>
                                                                        </defs>
                                                                    </svg>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>























                                {% endif %}

							</div>
							<!--column-->
                            <!--
							<div class="col-xl-12">
								<div class="table-pagenation">
									<p>Showing <span>1-5</span>from <span>100</span>data</p>
									<nav>
										<ul class="pagination pagination-gutter pagination-primary no-bg">
											<li class="page-item page-indicator">
												<a class="page-link" href="javascript:void(0)">
													<i class="fa-solid fa-angle-left"></i></a>
											</li>
											<li class="page-item "><a class="page-link" href="javascript:void(0)">1</a>
											</li>
											<li class="page-item active"><a class="page-link" href="javascript:void(0)">2</a></li>
											<li class="page-item"><a class="page-link" href="javascript:void(0)">3</a></li>
											<li class="page-item page-indicator">
												<a class="page-link" href="javascript:void(0)">
													<i class="fa-solid fa-angle-right"></i></a>
											</li>
										</ul>
									</nav>
								</div>
							</div>
							-->
							</div>
							<!--/row-->

						</div>
						<!--/column-->
					</div>
				</div>
                    
{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}

    <script src="/static/assets/vendor/tourguidejs/tour.js"></script>
    <script>
    $(document).ready(function() {

        {% include 'includes/tour_script.html' %}
        /*
         * Event handler for help/info button
         */
        $('#run-tutorial-button').on('click', function(e) {
            tourGuide.start();
        });

        $('#document-search-input').keyup(function(e){
            //if(e.keyCode == 13)
            //{
                const value = $('#document-search-input').val().toLowerCase();
                console.log('Searching...');
                $('.folder-link').removeClass('active');
                $('.folder-item').addClass('hidden');

                $('.folder-item').each(function(i, obj) {
                    let parent = $(this);
                    parent.find("h4").each(function() {
                        console.log($(this).text());
                        if($(this).text().toLowerCase().includes(value)) {
                            parent.removeClass('hidden', 1000);
                        }
                    });
                });
            //}
        });

        $('.folder-link').on('click', function () {
            // download-all-button
            const folderID = $(this).data('folder');
            
            $("#download-all-button-txt").attr("href", "/create/download/documents/" + folderID + "/txt/");
            $("#download-all-button-doc").attr("href", "/create/download/documents/" + folderID + "/doc/");
            $("#download-all-button-pdf").attr("href", "/create/download/documents/" + folderID + "/pdf/");

            $('.folder-link').removeClass('active');
            $('#folder-link-' + folderID).addClass('active');
            $('.folder-item').addClass('hidden');

            $('.folder-item').each(function(i, obj) {
                if($(this).hasClass('in-folder-' + folderID)) {
                    $(this).removeClass('hidden', 1000);
                }
            });
        })

        $('#add-folder-form').submit(function () {
            popWorkingOverlay(true);
        });

        $('.folder-layout-tab').on('click', function () {
            $('.folder-layout-tab, .folder-structure ').toggleClass('grid');
        });

    });
	</script>

{% endblock javascripts %}