{% extends "layouts/base.html" %}

{% block title %} Create Content {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link href="/assets-auth/css/editor.css" rel="stylesheet" type="text/css" />
{% endblock stylesheets %}

{% block content-body %}mh-auto{% endblock content-body %}
{% block content-fluid %}p-0{% endblock content-fluid %}
{% block content %}






    <div class="d-flex flex-column flex-column-fluid container-fluid ps-0-important">

        <div class="content flex-column-fluid" id="kt_content">







                <!-- row -->
                <!--
                <div class="row" id="tour-document-start">
                    <div class="col-lg-12">
                 -->


                        <div class="card d-flex justify-content-center justify-content-xl-start flex-row-auto w-100">


                        <!--<div class="card rounded-0 mb-0 h-auto">-->
                            <div class="card-body p-0 ">




								<div class="row m-5">

									<div class="col-lg-5 col-xl-5 col-xxl5">
										<div class="email-right-box ms-0 mt-3 ">
											<div class="compose-wrapper " id="compose-content">
												<div class="compose-content">
                                                    <h4>Creating: {{ active_template.name }}</h4>
                                                    <hr class="pb-2" />
													<form id="create-content-form">
                                                        {% csrf_token %}
                                                        <input type="hidden" name="is_form" value="y">
                                                        {% for variable in active_template_variables %}
                                                            <h5 class="mt-3 mb-1">
                                                                {{ variable.name }}
                                                            </h5>
                                                            {% if variable.auto_suggest_type != 0 %}
                                                                <div class="auto-suggest" data-token="{{ variable.token }}" data-type="{{ variable.auto_suggest_type }}">
                                                                    <p><strong>Click a suggestion below or enter in your own:</strong></p>
                                                                </div>
                                                            {% endif %}
                                                            {% if variable.dropdown_choices_list %}
                                                                <div class="mb-3">
                                                                    <select class="form-select form-select-lg" data-control="select2"
                                                                    data-allow-clear="false" data-hide-search="true"
                                                                           id="variable_{{ variable.token }}"
                                                                           name="variable_{{ variable.token }}">
                                                                        {% for choice in variable.dropdown_choices_list %}
                                                                            <option value="{{ choice }}">{{ choice|title }}</option>
                                                                        {% endfor %}
                                                                    </select>
                                                                </div>







                                                            {% else %}
                                                                {% if variable.is_text_area == True %}
                                                                <div class="mb-3">
                                                                    <textarea
                                                                       id="variable_{{ variable.token }}"
                                                                       name="variable_{{ variable.token }}"
                                                                       class="textarea_editor form-control bg-transparent"
                                                                       rows="5"
                                                                       placeholder="{{ variable.description }}"></textarea>
                                                                </div>
                                                                {% else %}
                                                                <div class="mb-3">
                                                                    <input type="text"
                                                                           class="form-control bg-transparent"
                                                                           id="variable_{{ variable.token }}"
                                                                           name="variable_{{ variable.token }}"
                                                                           placeholder="{{ variable.description }}">
                                                                </div>
                                                                {% endif %}
                                                            {% endif %}
                                                        {% endfor %}
													</form>
												</div>
												<div class="text-start mt-4 mb-3">
													<button class="btn btn-primary btn-sl-sm me-2 w-100"{% if document %} disabled{% endif %}
                                                            id="create-content-button" type="button">
                                                        <span class="me-2">
                                                            <i class="fas fa-magic" id="create-content-icon"></i>
                                                        </span>
                                                        Create Content
                                                    </button>
												</div>
											</div>
										</div>
									</div>







									<div class="col-lg-7 col-xl-7 col-xxl-7 ps-3 h-100" id="tour-document-section">
                                        <h4 class="mt-3">Content Results:</h4>
                                                    <hr class="pb-2" />



                                            <div class="fv-row mb-2 fv-plugins-icon-container">
                                                <!--begin::Label-->
                                                <h5 class="mt-3 mb-1">
                                                    Title
                                                </h5>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <input name="title" id="title" class="form-control" value="">
                                                <!--end::Input-->
                                            </div>

                                            <div class="fv-row mb-2 fv-plugins-icon-container">
                                                <!--begin::Label-->
                                                <h5 class="mt-3 mb-1">
                                                    Keywords
                                                </h5>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <input name="keywords" id="keywords" class="form-control" value="">
                                                <!--end::Input-->
                                            </div>

                                        <div class="ck-editor-wrapper">
                                            <!--
                                            <button class="ck-button-custom ck-button-custom-start" id="editor-button-rewrite">
                                                Re-write
                                                <i class="fa-regular fas fa-redo-alt align-middle"></i>
                                            </button>
                                            <button class="ck-button-custom" id="editor-button-expand">
                                                Expand
                                                <i class="fa-regular fas fa-expand-arrows-alt align-middle"></i>
                                            </button>
                                            <button class="ck-button-custom" id="editor-button-add">
                                                Insert Content
                                                <i class="fa-regular fas fa-reply-all align-middle"></i>
                                            </button>
                                            <button class="ck-button-custom" id="editor-button-copyscape">
                                                CopyScape
                                                <i class="fa-regular fas fa-check align-middle"></i>
                                            </button>


                                            <button class="ck-button-custom" id="editor-button-spin">
                                                Spin
                                                <i class="fa-regular fas fa-sync-alt align-middle"></i>
                                            </button>





                                            <button class="ck-button-custom" id="editor-button-help">
                                                <i class="fa-regular fas fa-info-circle align-middle"></i>
                                            </button>
                                            -->





                                            <div id="ckeditor">{% if document.text %}{{ document.text|safe }}{% endif %}</div>
                                        </div>

                                        <div class="text-start mt-3 mb-3 me-4">

                                            <input type="hidden" value="{% if document %}{{ document.pk }}{% endif %}" id="document-id">

                                            <div class="row" id="tour-save-section">
                                                <div class="col-sm-12 mt-2 mt-sm-0 pe-0">
                                                    <button class="btn btn-primary w-100 h-100" id="editor-button-save" type="button">
                                                        <i class="fas fa-save"></i>
                                                        Save
                                                    </button>
                                                </div>
                                            </div>

                                        </div>
                                    </div>







								</div>


                            </div>
                        </div>





            <!--</div>
                </div>-->

















        </div>
    </div>





{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/assets-auth/js/ckeditor.js"></script>
    <script>
    $(document).ready(function() {
        let autoSuggestTitles = [];  // 1
        let autoSuggestSubjects = [];  // 2
        let autoSuggestCategories = [];  // 3

        async function getSuggestions() {
            if (autoSuggestTitles.length === 0) {
                popWorkingOverlay(true, 'Getting content<br />suggestions...');
                formData = {
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                    'type': '1',
                };
                await $.ajax({
                    url: '{% url 'create_api_get_auto_suggestions' %}',
                    type: 'POST',
                    data: formData,
                    success: function(result) {
                        // console.log(result);
                        autoSuggestTitles = result.titles;
                        autoSuggestSubjects = result.topics;
                        autoSuggestCategories = result.categories;
                        // Swal.fire('Success!', 'Your success' );
                    },
                    error: function (response) {
                        Swal.fire(
                            "Error",
                            "There was an error your document. Please try again later.",
                            "error"
                        );
                    },
                    complete: function(data) {
                        popWorkingOverlay(false, '');
                    }
                });
            }
            return;
        }

        $('.auto-suggest').each(async function (i, obj) {
            let asType = $(this).data('type');
            let tokenName = $(this).data('token');
            await getSuggestions();
            if (asType == '1') {
                for (const title of autoSuggestTitles) {
                    $(this).append('<a href="javascript:;" class="auto-suggest-option" data-token="' + tokenName + '" data-text="' + title + '">• ' + title + '</a>');
                }
            }
            if (asType == '2') {
                for (const subject of autoSuggestSubjects) {
                    $(this).append('<a href="javascript:;" class="auto-suggest-option" data-token="' + tokenName + '" data-text="' + subject + '">• ' + subject + '</a>');
                }
            }
            if (asType == '3') {
                for (const category of autoSuggestCategories) {
                    $(this).append('<a href="javascript:;" class="auto-suggest-option" data-token="' + tokenName + '" data-text="' + category + '">• ' + category + '</a>');
                }
            }
            console.log('Auto suggest');
            console.log($(this).data('token'));
            console.log($(this).data('type'));
        });

        $('body').on('click', '.auto-suggest-option', function() {
            let text = $(this).data('text');
            let tokenName = $(this).data('token');
            $('#variable_' + tokenName).val(text);
        });



        if($("#ckeditor").length > 0) {
			ClassicEditor
			.create( document.querySelector( '#ckeditor' ), {
				toolbar: [ 'heading', '|', 'bold', 'italic', 'link', '|', 'undo', 'redo', 'toggleImageCaption', 'imageTextAlternative' ]
			} )
			.then( editor => {
				window.editor = editor;
			} )
			.catch( err => {
				console.error( err.stack );
			} );
		}

        /*
         * Returns the selected/highlighted text from the document editor.
         */
        function getSelectedText() {
            const range = editor.model.document.selection.getFirstRange();
            let selectedText = '';
            for (const item of range.getItems()) {
                console.log('Range: ' + item.data);
                selectedText = item.data;
                console.log('Bong: ' + selectedText);
            }
            return selectedText;
        }

        /*
         * Replaces or inserts text into the document editor.
         * If text is selected/highlighted, it will replace it.
         * If none is selected, it will place the text at the mouse location.
         */
        function replaceSelectedText(text) {
            editor.model.change(writer => {
                const range = editor.model.document.selection.getFirstRange();
                editor.model.insertContent(writer.createText(text), range);
                //editor.model.insertContent(writer.createText(text), range);
                //editor.model.insertContent(writer.createHTML(text), range);
            });
        }

        /*
         * API call helper function.  Dials up the API to create content.
         */
        function callCreateContentAPI(formData, promptID, isForm=false) {
            formData['csrfmiddlewaretoken'] = '{{ csrf_token }}';
            $.ajax({
                url: '/create/api/create_content/' + promptID + '/',
                type: 'POST',
                data: formData,
                success: function(result)
                {
                    console.log('ARTICLE!!!!!!!!!!!  result');
                    console.log(result);
                    if(isForm) {
                        //$('#ckeditor').html(result.text);
                        editor.data.set(result.text);
                        $('#title').val(result.title);
                        $('#keywords').val(result.keywords);
                    } else {
                        replaceSelectedText(result.text);
                    }
                    Swal.fire('Success!', 'Your content has been created.', 'success' );
                },
                error: function(result)
                {
                    //if(result.responseJSON.out_of_credits) {
                    //    outOfCredits();
                    //} else {
                        Swal.fire(
                            "Error", "There was an error creating your content. Please try again later.", "error"
                        );
                    //}
                },
                complete: function(data) {
                    popWorkingOverlay(false);
                }
            });
        }

        /*
         * Saving the created document to the server.
         */
        $('#editor-button-save').on('click', function() {
            Swal.fire("Error", "Coming Soon", "error");
            return;

            if(!$('#editor-document-name').val()) {
                Swal.fire("Error", "Please enter in the document name.", "error");
                return;
            }
            if(!editor.getData()) {
                Swal.fire("Error", "It's an empty document!", "error");
                return;
            }
            let actionName = 'created';
            if($('#document-id').val()) {
                actionName = 'updated';
            }
            formData = {
                'csrfmiddlewaretoken': '{{ csrf_token }}',
                'name': $('#editor-document-name').val(),
                'folder_id': $('#editor-folder-select').val(),
                'text': editor.getData(),
                'document_id': $('#document-id').val(),
                'template_id': {{ active_template.pk }}
            };
            console.log(formData);
            popWorkingOverlay(true);
            $.ajax({
                url: '/create/api/create_document/',
                type: 'POST',
                data: formData,
                success: function(result) {
                    $('#document-id').val(result.id);
                    Swal.fire('Success!', 'Your document has been ' + actionName + '.', 'success' );
                },
                error: function (response) {
                    Swal.fire(
                        "Error",
                        "There was an error " + actionName + " your document. Please try again later.",
                        "error"
                    );
                },
                complete: function(data) {
                    popWorkingOverlay(false);
                }
            });
        });

        /*
         * The editor 'Expand Content' button.
         * It will ask the user for a topic to write about,
         * then create a paragraph about that topic,
         * and insert it at the mouse position.
         */
        $('#editor-button-expand').on('click', function() {
            console.log('Button hit expand...');
            let selectedText = getSelectedText();
            if(selectedText.length > 0) {
                popWorkingOverlay(true);
                console.log('Found...' + selectedText);
                Swal.fire({
                    title: 'Are you sure?',
                    text: 'Would you like to have the system expand only the text you highlighted?',
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, expand it!'
                }).then((result) => {
                    console.log('Success!');
                    console.log(result);
                    if (result.value == true) {
                        callCreateContentAPI({'variable_text': selectedText}, '57');
                    } else {
                        popWorkingOverlay(false);
                    }
                });
            } else {
                Swal.fire("Error", "Please select/highlight the text you want to expand.", "error");
            }
        });

        /*
         * The editor 'Insert Content' button.
         * It will ask the user for a topic to write about,
         * then create a paragraph about that topic,
         * and insert it at the mouse position.
         */
        $('#editor-button-add').on('click', async function () {
            console.log('Button hit add...');
            const {value: text} = await Swal.fire({
                title: 'Add a paragraph',
                text: 'What should this content be about?',
                type: 'question',
                input: 'textarea',
                inputPlaceholder: 'What should this content be about?',
                inputAttributes: {
                    'aria-label': 'What should this content be about?',
                    min: 8,
                    max: 120,
                    step: 1
                },
                showCancelButton: true
            });
            if (text) {
                popWorkingOverlay(true);
                callCreateContentAPI({'variable_text': text}, '61');
            } else {
                Swal.fire("Error", "Please enter in the topic you'd like to have written about.", "error");
            }
        });

        /*
         * The editor 'Re-write Content' button.
         * It will take the highlighted/selected text and have it re-written,
         * then replace it with the new text.
         */
        $('#editor-button-rewrite').on('click', function() {
            console.log('Button hit re-write...');
            let selectedText = getSelectedText();
            if(selectedText.length > 0) {
                console.log('Found...'+selectedText);
                Swal.fire({
                    title: 'Are you sure?',
                    text: "Would you like to have the system re-write only the text you highlighted?",
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, re-write it!'
                }).then((result) => {
                    console.log('Success!');
                    console.log(result);
                    if (result.value == true) {
                        popWorkingOverlay(true);
                        callCreateContentAPI({'variable_text': selectedText}, '59');
                    } else {
                        popWorkingOverlay(false);
                    }
                });
            } else {
                Swal.fire("Error", "Please select/highlight the text you want to re-write.", "error");
            }
        });


        /*
         * This uses the template & form to create the bulk of the content of the document.
         */
        $('#create-content-button').on('click', function() {
            let noError = true;

            // Fields are dynamic so let's make sure we have everythign we need.
            // All mandatory fields start with the 'variable_' prefix.
            $('[id^=variable_]').each(function(key, value) {
                if(!$(this).val()) {
                    Swal.fire("Error", "Please fill out all fields", "error");
                    noError = false;
                }
            });

            if(noError) {
                popWorkingOverlay(true, 'Creating<br />Article');
                document.body.scrollTop = document.documentElement.scrollTop = 0;
                callCreateContentAPI($('#create-content-form').serialize(), '{{ active_template.pk }}', true);
                $('#create-content-button').prop("disabled", true);
            }
        });


    });
	</script>

{% endblock javascripts %}