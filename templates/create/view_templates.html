{% extends "layouts/base.html" %}

{% block title %} Create Content Templates {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link href="/static/assets/css/editor.css" rel="stylesheet">
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}



    <div class="row">
        <div class="col-xl-12">
            <div class="page-titles">
                <div class="d-flex align-items-center flex-wrap ">
                    <h3 class="heading">AI Writer Templates</h3>
                </div>
            </div>
        </div>
    </div>



    <!-- Nav tabs -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card dz-card">


                <div class="custom-tab-1 mt-3 mx-4">
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#all"><i class="la la-globe me-2"></i> All</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#email"><i class="la la-envelope-o me-2"></i> SEO</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#social"><i class="la la-twitter me-2"></i> Social Media</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#sales"><i class="la la-money me-2"></i> Sales & Marketing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#premium"><i class="la la-certificate me-2"></i> Premium</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#starter"><i class="la la-apple-alt me-2"></i> Starter</a>
                        </li>
                        <!--
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#custom"><i class="la la-cog me-2"></i> Custom</a>
                        </li>
                        -->
                    </ul>
                    <div class="tab-content mt-3">

                        <div class="tab-pane fade show active" id="all" role="tabpanel">


                            <div class="page-titles p-0">
                                <div class="mt-2">
                                </div>
                                <div class="d-flex mt-2">
                                    <div class="input-group search-area mx-0">
                                        <input type="text" class="form-control" id="template-search-input" placeholder="Search here...">
                                        <span class="input-group-text">
                                            <a href="javascript:void(0)">
                                                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.3" d="M16.6751 19.4916C16.2194 19.036 16.2194 18.2973 16.6751 17.8417C17.1307 17.3861 17.8694 17.3861 18.325 17.8417L22.9916 22.5084C23.4473 22.964 23.4473 23.7027 22.9916 24.1583C22.536 24.6139 21.7973 24.6139 21.3417 24.1583L16.6751 19.4916Z" fill="white"></path>
                                                    <path d="M12.8333 18.6667C16.055 18.6667 18.6667 16.055 18.6667 12.8334C18.6667 9.61169 16.055 7.00002 12.8333 7.00002C9.61166 7.00002 6.99999 9.61169 6.99999 12.8334C6.99999 16.055 9.61166 18.6667 12.8333 18.6667ZM12.8333 21C8.323 21 4.66666 17.3437 4.66666 12.8334C4.66666 8.32303 8.323 4.66669 12.8333 4.66669C17.3436 4.66669 21 8.32303 21 12.8334C21 17.3437 17.3436 21 12.8333 21Z" fill="white"></path>
                                                </svg>
                                            </a>
                                        </span>
                                    </div>
                                </div>
                            </div>


                            <!-- row -->
                            <div class="row" id="all-templates">

                                {% for template in templates %}
                                <div class="col-xl-4 col-xxl-4 col-lg-4 col-sm-4 all-templates-block">
                                    <div class="widget-stat card bg-{% if template.category == 0 %}dark{% elif template.category == 1 %}primary{% elif template.category == 2 %}danger{% elif template.category == 3 %}info{% elif template.category == 4 %}warning{% endif %} template-block"
                                         data-id="{% if is_subscriber %}{{ template.pk }}{% else %}{% if template.is_starter %}{{ template.pk }}{% else %}0{% endif %}{% endif %}">
                                        <div class="card-body p-4">
                                            <div class="media">
                                                <span class="me-3">
                                                    <i class="{{ template.icon }}"></i>
                                                </span>
                                                <div class="media-body text-white">
                                                    <p class="mb-0 text-white">{{ template.name }}</p>
                                                    <h3 class="text-white">{{ template.buzz_word }}</h3>
                                                    <small>{{ template.description }}</small>
                                                </div>
                                                {% if not template.is_starter %}
                                                <div class="mail-img">
											        <i class="fas fa-star"></i>
										        </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}


                            </div>

                        </div>

                        <div class="tab-pane fade" id="social">

                            <div class="row">
                            {% for template in templates %}
                                {% if template.category == 1 %}
                                <div class="col-xl-4 col-xxl-4 col-lg-4 col-sm-4">
                                    <div class="widget-stat card bg-{% if template.category == 0 %}dark{% elif template.category == 1 %}primary{% elif template.category == 2 %}danger{% elif template.category == 3 %}info{% endif %} template-block"
                                         data-id="{% if is_subscriber %}{{ template.pk }}{% else %}{% if template.is_starter %}{{ template.pk }}{% else %}0{% endif %}{% endif %}">
                                        <div class="card-body p-4">
                                            <div class="media">
                                                <span class="me-3">
                                                    <i class="{{ template.icon }}"></i>
                                                </span>
                                                <div class="media-body text-white">
                                                    <p class="mb-0 text-white">{{ template.name }}</p>
                                                    <h3 class="text-white">{{ template.buzz_word }}</h3>
                                                    <small>{{ template.description }}</small>
                                                </div>
                                                {% if not template.is_starter %}
                                                <div class="mail-img">
											        <i class="fas fa-star"></i>
										        </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endfor %}
                            </div>

                        </div>

                        <div class="tab-pane fade" id="sales">

                            <div class="row">
                            {% for template in templates %}
                                {% if template.category == 2 %}
                                <div class="col-xl-4 col-xxl-4 col-lg-4 col-sm-4">
                                    <div class="widget-stat card bg-{% if template.category == 0 %}dark{% elif template.category == 1 %}primary{% elif template.category == 2 %}danger{% elif template.category == 3 %}info{% endif %} template-block"
                                         data-id="{% if is_subscriber %}{{ template.pk }}{% else %}{% if template.is_starter %}{{ template.pk }}{% else %}0{% endif %}{% endif %}">
                                        <div class="card-body p-4">
                                            <div class="media">
                                                <span class="me-3">
                                                    <i class="{{ template.icon }}"></i>
                                                </span>
                                                <div class="media-body text-white">
                                                    <p class="mb-0 text-white">{{ template.name }}</p>
                                                    <h3 class="text-white">{{ template.buzz_word }}</h3>
                                                    <small>{{ template.description }}</small>
                                                </div>
                                                {% if not template.is_starter %}
                                                <div class="mail-img">
											        <i class="fas fa-star"></i>
										        </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endfor %}
                            </div>

                        </div>

                        <div class="tab-pane fade" id="email">

                            <div class="row">
                            {% for template in templates %}
                                {% if template.category == 3 %}
                                <div class="col-xl-4 col-xxl-4 col-lg-4 col-sm-4">
                                    <div class="widget-stat card bg-{% if template.category == 0 %}dark{% elif template.category == 1 %}primary{% elif template.category == 2 %}danger{% elif template.category == 3 %}info{% endif %} template-block"
                                         data-id="{% if is_subscriber %}{{ template.pk }}{% else %}{% if template.is_starter %}{{ template.pk }}{% else %}0{% endif %}{% endif %}">
                                        <div class="card-body p-4">
                                            <div class="media">
                                                <span class="me-3">
                                                    <i class="{{ template.icon }}"></i>
                                                </span>
                                                <div class="media-body text-white">
                                                    <p class="mb-0 text-white">{{ template.name }}</p>
                                                    <h3 class="text-white">{{ template.buzz_word }}</h3>
                                                    <small>{{ template.description }}</small>
                                                </div>
                                                {% if not template.is_starter %}
                                                <div class="mail-img">
											        <i class="fas fa-star"></i>
										        </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endfor %}
                            </div>

                        </div>

                        <div class="tab-pane fade" id="premium">

                            <div class="row">
                            {% for template in templates %}
                                {% if not template.is_starter %}
                                <div class="col-xl-4 col-xxl-4 col-lg-4 col-sm-4">
                                    <div class="widget-stat card bg-{% if template.category == 0 %}dark{% elif template.category == 1 %}primary{% elif template.category == 2 %}danger{% elif template.category == 3 %}info{% endif %} template-block"
                                         data-id="{% if is_subscriber %}{{ template.pk }}{% else %}{% if template.is_starter %}{{ template.pk }}{% else %}0{% endif %}{% endif %}">
                                        <div class="card-body p-4">
                                            <div class="media">
                                                <span class="me-3">
                                                    <i class="{{ template.icon }}"></i>
                                                </span>
                                                <div class="media-body text-white">
                                                    <p class="mb-0 text-white">{{ template.name }}</p>
                                                    <h3 class="text-white">{{ template.buzz_word }}</h3>
                                                    <small>{{ template.description }}</small>
                                                </div>
                                                {% if not template.is_starter %}
                                                <div class="mail-img">
											        <i class="fas fa-star"></i>
										        </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endfor %}
                            </div>

                        </div>

                        <div class="tab-pane fade" id="starter">

                            <div class="row">
                            {% for template in templates %}
                                {% if template.is_starter %}
                                <div class="col-xl-4 col-xxl-4 col-lg-4 col-sm-4">
                                    <div class="widget-stat card bg-{% if template.category == 0 %}dark{% elif template.category == 1 %}primary{% elif template.category == 2 %}danger{% elif template.category == 3 %}info{% endif %} template-block"
                                         data-id="{% if is_subscriber %}{{ template.pk }}{% else %}{% if template.is_starter %}{{ template.pk }}{% else %}0{% endif %}{% endif %}">
                                        <div class="card-body p-4">
                                            <div class="media">
                                                <span class="me-3">
                                                    <i class="{{ template.icon }}"></i>
                                                </span>
                                                <div class="media-body text-white">
                                                    <p class="mb-0 text-white">{{ template.name }}</p>
                                                    <h3 class="text-white">{{ template.buzz_word }}</h3>
                                                    <small>{{ template.description }}</small>
                                                </div>
                                                {% if not template.is_starter %}
                                                <div class="mail-img">
											        <i class="fas fa-star xs"></i>
										        </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endfor %}
                            </div>

                        </div>















                    </div>
                </div>

            </div>
        </div>
    </div>


{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script>
    $(function() {
        $('.template-block').on('click', function(e) {
            const template_id = $(this).data('id');
            console.log(template_id);
            if(template_id == '0') {
                proFeatureOnly('The templates marked with a star are for subscribers only.  Would you like to upgrade now?');
            } else {
                window.location.href = '/create/document/' + template_id + '/';
            }
        });

        /*
         * Search text box handler.  Searches the div boxes.
         */
        $('#template-search-input').keyup(function(e){
            //if(e.keyCode == 13)
            //{
            const value = $('#template-search-input').val().toLowerCase();
            console.log('Searching... ' + value);
            $('#all-templates .all-templates-block').addClass('hidden');

            $('#all-templates .all-templates-block').each(function(i, obj) {
                let parent = $(this);
                parent.children().each( () => {
                    console.log($(this).text());
                    if($(this).text().toLowerCase().includes(value)) {
                        parent.removeClass('hidden', 1000);
                    }
                });
            });
            //}
        });
    });
	</script>
{% endblock javascripts %}