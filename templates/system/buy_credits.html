{% extends "layouts/base.html" %}

{% block title %} Buy Credits {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}
    {% load humanize %}


				<div class="row ">
					<div class="col-xl-12">
						<div class="page-titles">
							<div class="d-flex align-items-center flex-wrap ">
								<h3 class="heading">Buy Credits</h3>
							</div>
						</div>
					</div>
                </div>





    <div class="row wow fadeInUp main-card" data-wow-delay="0.5s">
        <!--column-->
        <div class="col-xl-4 col-lg-4 col-xxl-4 col-sm-12">
            <div class="card coin-card secondary">
                <div class="back-image">
                    <svg width="121" height="221" viewBox="0 0 121 221" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="135.5" cy="84.5" r="40" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="135.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="109.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="86.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="64.5" stroke="#BE7CFF"/>
                    </svg>
                </div>
                <div class="card-body p-4 ">
                    <div class="title">
                        <h3 class="buy-credits-h3">10,000 Credits</h3>
                        <h4>At least 10,000 words</h4>
                        <i class="fas fa-shopping-cart buy-credits-i"></i>
                    </div>
                    <div  class="chart-num">
                        <h2 class="mb-3">$9.99</h2>
                        <a class="buy-credits-a" href="/system/buy/10000_credits/">
                            Buy 10,000 Credits <i class="fas fa-shopping-cart"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>





        <!--column-->
        <div class="col-xl-4 col-lg-4 col-xxl-4 col-sm-12">
            <div class="card coin-card blue">
                <div class="back-image">
                    <svg width="121" height="221" viewBox="0 0 121 221" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="135.5" cy="84.5" r="40" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="135.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="109.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="86.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="64.5" stroke="#BE7CFF"/>
                    </svg>
                </div>
                <div class="card-body p-4 ">
                    <div class="title">
                        <h3 class="buy-credits-h3">50,000 Credits</h3>
                        <h4>At least 50,000 words</h4>
                        <i class="fas fa-shopping-cart buy-credits-i"></i>
                    </div>
                    <div  class="chart-num">
                        <h2 class="mb-3">$39.99</h2>
                        <a class="buy-credits-a" href="/system/buy/50000_credits/">
                            Buy 50,000 Credits <i class="fas fa-shopping-cart"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>







        <!--column-->
        <div class="col-xl-4 col-lg-4 col-xxl-4 col-sm-12">
            <div class="card coin-card green">
                <div class="back-image">
                    <svg width="121" height="221" viewBox="0 0 121 221" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="135.5" cy="84.5" r="40" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="135.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="109.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="86.5" stroke="#BE7CFF"/>
                        <circle cx="136" cy="85" r="64.5" stroke="#BE7CFF"/>
                    </svg>
                </div>
                <div class="card-body p-4 ">
                    <div class="title">
                        <h3 class="buy-credits-h3">100,000 Credits</h3>
                        <h4>At least 100,000 words</h4>
                        <i class="fas fa-shopping-cart buy-credits-i"></i>
                    </div>
                    <div  class="chart-num">
                        <h2 class="mb-3">$74.99</h2>
                        <a class="buy-credits-a" href="/system/buy/100000_credits/">
                            Buy 100,000 Credits <i class="fas fa-shopping-cart"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>


    </div>




{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script>
    {% if is_success_page %}
    $(document).ready(async function () {
        popWorkingOverlay(true);
        let count = 0;

        var client = {
            get: function () {
                return new Promise(function (resolve, reject) {
                    count ++;
                    setTimeout(function () {
                        $.ajax({
                            url: '/system/api/purchase_status/',
                            type: 'GET',
                            success: function(result)
                            {
                                resolve({status:result.pending});
                            },
                        });
                    }, 2000);
                });
            }
        }

        async function someFunction() {
            while (true) {
                let dataResult = await client.get('/system/api/purchase_status/');
                console.log(dataResult);
                if (dataResult.status) {
                    console.log('Success...')
                    Swal.fire({
                        title: 'Success!',
                        text: "Your purchase was successful.",
                        type: 'success',
                    }).then((result) => {
                        location.href = '/system/buy/success/';
                    });
                    return dataResult;
                }
            }
        }

        (async () => { let r = await someFunction(); console.log(r); })();

    });
    {% endif %}
    </script>
{% endblock javascripts %}