{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} Projects {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link rel="stylesheet" href="/static/assets/vendor/swiper/css/swiper-bundle.min.css">
    <link rel="stylesheet" href="/static/assets/vendor/dropzone/dist/dropzone.css">
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}


    <div class="row">
        <div class="col-xl-12">



            <div class="card position-relative overflow-hidden">
                <div class="shop-part d-flex w-100">
                    <div class="card-body p-4 pb-0">

                        <div class="d-flex justify-content-between align-items-center gap-6 mb-4">
                            <a class="btn btn-primary d-lg-none d-flex" data-bs-toggle="offcanvas" href="#filtercategory" role="button" aria-controls="filtercategory">
                                <i class="ti ti-menu-2 fs-6"></i>
                            </a>
                            <h5 class="fs-5 mb-0 d-none d-lg-block">Products</h5>
                            <form class="position-relative">
                                <input type="text" class="form-control search-chat py-2 ps-5" id="text-srh" placeholder="Search Product">
                                <i class="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                            </form>
                        </div>
                        <div class="row">
                            {% for project in projects %}
                            {% if project.type == "fixed" %}
                            <h4 class="card-title mb-3">
                                {{ project.title }} -
                                Payout {{ project.currency.sign }}{{ project.payout|floatformat:2|intcomma }} {{ project.currency.name }}
                                {% if project.currency.code != "USD" %}<small>(estimated ${{ project.payout_usd|floatformat:2|intcomma }} USD)</small>{% endif %} -
                                {{ project.id }}
                            </h4>
                            <div class="accordion mb-5" id="accordionExample{{ project.id }}">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading{{ project.id }}">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ project.id }}" aria-expanded="true" aria-controls="collapse{{ project.id }}">
                                            {{ project.preview_description }}
                                        </button>
                                    </h2>
                                    <div id="collapse{{ project.id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ project.id }}" data-bs-parent="#accordionExample{{ project.id }}">
                                        <div class="accordion-body">

                                            {{ project.description|linebreaksbr }}

                                            <form action="" method="post" class="mt-3 mb-3">
                                                {% csrf_token %}
                                                <input type="hidden" name="api_id" value="{{ project.id }}">

                                                <div class="mb-4">
                                                    <label class="form-label">Bid in {{ project.currency.name }}</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text px-6" id="basic-addon1">
                                                            <i class="ti ti-currency-dollar fs-6"></i>
                                                        </span>
                                                        <input type="text" class="form-control ps-2" value="{{ project.payout|floatformat:2|intcomma }}"
                                                               name="price" id="price">
                                                    </div>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="form-label">Message</label>
                                                    <div class="input-group">
                                                      <textarea class="form-control p-2 ps-2" name="description" id="description" cols="20" rows="15">We have created similar application for Sierra Chart and have all the skills required. AI is all we do. Our expertise covers all the areas you need to build the perfect application for your project. Simply put, I’m the best fit for this job.</textarea>
                                                    </div>
                                                </div>
                                                <button type="submit" class="btn btn-primary">
                                                    Place Bid
                                                    <i class="ti ti-circle-arrow-right me-0 ms-1 fs-4"></i>
                                                </button>

                                            </form>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% endfor %}
                        </div>

                    </div>
                </div>
            </div>



        </div>
    </div>








{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script>
        $(document).ready(function() {
            $('form').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');

                // Disable the button
                submitBtn.prop('disabled', true);

                // Change the icon to a spinning animation and update text
                submitBtn.html(`
                    Placing Bid...
                    <i class="ti ti-loader-2 ti-spin me-0 ms-1 fs-4"></i>
                `);
            });
        });
    </script>
{% endblock javascripts %}