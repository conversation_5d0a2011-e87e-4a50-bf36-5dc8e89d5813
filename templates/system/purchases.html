{% extends "layouts/base.html" %}

{% block title %} Your Purchases {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
{% load humanize %}


				<div class="row ">
					<div class="col-xl-12">
						<div class="page-titles">
							<div class="d-flex align-items-center flex-wrap ">
								<h3 class="heading">Your Purchases</h3>
							</div>
						</div>
					</div>
                </div>





                <div class="row wow fadeInUp main-card" data-wow-delay="0.7s">
							<!--column-->
							<div class="col-xxl-11 col-xl-11">

								<div class="row">
									<div class="col-xl-12 wow fadeInUp" data-wow-delay="1.5s">
										<div class="card">
											<div class="card-header border-0 flex-wrap">
												<h2 class="heading">Subscriptions Purchases</h2>
												<!--
												<div class="d-flex align-items-center">
													<select class="image-select default-select dashboard-select me-4" aria-label="Default">
														<option selected>This Month</option>
														<option value="1">Weeks</option>
														<option value="2">This Day</option>
													</select>
													<div class="dropdown">
														<a href="javascript:void(0);" class="btn-link btn sharp tp-btn btn-primary pill" data-bs-toggle="dropdown" aria-expanded="false">
														<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M8.33319 9.99985C8.33319 10.9203 9.07938 11.6665 9.99986 11.6665C10.9203 11.6665 11.6665 10.9203 11.6665 9.99986C11.6665 9.07938 10.9203 8.33319 9.99986 8.33319C9.07938 8.33319 8.33319 9.07938 8.33319 9.99985Z" fill="#ffffff"/>
														<path d="M8.33319 3.33329C8.33319 4.25376 9.07938 4.99995 9.99986 4.99995C10.9203 4.99995 11.6665 4.25376 11.6665 3.33329C11.6665 2.41282 10.9203 1.66663 9.99986 1.66663C9.07938 1.66663 8.33319 2.41282 8.33319 3.33329Z" fill="#ffffff"/>
														<path d="M8.33319 16.6667C8.33319 17.5871 9.07938 18.3333 9.99986 18.3333C10.9203 18.3333 11.6665 17.5871 11.6665 16.6667C11.6665 15.7462 10.9203 15 9.99986 15C9.07938 15 8.33319 15.7462 8.33319 16.6667Z" fill="#ffffff"/>
														</svg>

														</a>
														<div class="dropdown-menu dropdown-menu-end">
															<a class="dropdown-item" href="javascript:void(0);">Delete</a>
															<a class="dropdown-item" href="javascript:void(0);">Edit</a>
														</div>
													</div>
												</div>
												-->
											</div>
											<div class="card-body py-0">
                                                {% if subscription_payments %}
												<div class="table-responsive">
													<table class="table-responsive-lg table display mb-4 order-table card-table text-black no-footer student-tbl">
														<tbody>
                                                            {% for payment in subscription_payments %}
															<tr>
																<td class="whitesp-no p-0">
																	<div class="d-flex py-sm-3 py-1 align-items-center trans-info">
                                                                        {% if payment.is_cancellation %}
                                                                        <span class="icon me-3">
																			<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
																				<path d="M17.1657 -4.57764e-05H3.83236C2.9483 -4.57764e-05 2.10046 0.351144 1.47533 0.976265C0.850213 1.60139 0.499023 2.44923 0.499023 3.33329V16.6666C0.499023 17.5507 0.850213 18.3985 1.47533 19.0236C2.10046 19.6488 2.9483 20 3.83236 20H17.1657C18.0497 20 18.8976 19.6488 19.5227 19.0236C20.1478 18.3985 20.499 17.5507 20.499 16.6666V3.33329C20.499 2.44923 20.1478 1.60139 19.5227 0.976265C18.8976 0.351144 18.0497 -4.57764e-05 17.1657 -4.57764e-05ZM12.2074 12.5H7.56569L8.53236 13.475C8.68757 13.6311 8.77469 13.8423 8.77469 14.0625C8.77469 14.2826 8.68757 14.4938 8.53236 14.65C8.45449 14.7272 8.36214 14.7883 8.26061 14.8298C8.15908 14.8712 8.05036 14.8923 7.94069 14.8916C7.72207 14.8907 7.51258 14.8039 7.35736 14.65L4.96569 12.25C4.84866 12.1337 4.76877 11.9853 4.73613 11.8236C4.70349 11.6619 4.71958 11.4942 4.78236 11.3416C4.84487 11.1894 4.95104 11.0592 5.08747 10.9672C5.22391 10.8753 5.3845 10.8258 5.54902 10.825H12.2157C12.4367 10.825 12.6487 10.9128 12.8049 11.069C12.9612 11.2253 13.049 11.4373 13.049 11.6583C13.049 11.8793 12.9612 12.0913 12.8049 12.2475C12.6487 12.4038 12.4367 12.4916 12.2157 12.4916L12.2074 12.5ZM16.2574 8.68329C16.1904 8.82794 16.0834 8.95036 15.949 9.03603C15.8146 9.12169 15.6584 9.16702 15.499 9.16662H8.83236C8.61134 9.16662 8.39938 9.07882 8.2431 8.92254C8.08682 8.76626 7.99902 8.5543 7.99902 8.33329C7.99902 8.11228 8.08682 7.90031 8.2431 7.74403C8.39938 7.58775 8.61134 7.49996 8.83236 7.49996H13.474L12.5074 6.53329C12.3521 6.37715 12.265 6.16594 12.265 5.94579C12.265 5.72563 12.3521 5.51442 12.5074 5.35829C12.6635 5.20308 12.8747 5.11596 13.0949 5.11596C13.315 5.11596 13.5262 5.20308 13.6824 5.35829L16.074 7.74162C16.1961 7.85992 16.2792 8.01273 16.3119 8.17956C16.3447 8.34639 16.3257 8.51926 16.2574 8.67496V8.68329Z" fill="#FF4646"></path>
																			</svg>
																		</span>
                                                                        {% else %}
																		<span class="icon me-3">
																			<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
																				<path d="M17.1657 -4.57764e-05H3.83236C2.9483 -4.57764e-05 2.10046 0.351144 1.47533 0.976265C0.850213 1.60139 0.499023 2.44923 0.499023 3.33329V16.6666C0.499023 17.5507 0.850213 18.3985 1.47533 19.0236C2.10046 19.6488 2.9483 20 3.83236 20H17.1657C18.0497 20 18.8976 19.6488 19.5227 19.0236C20.1478 18.3985 20.499 17.5507 20.499 16.6666V3.33329C20.499 2.44923 20.1478 1.60139 19.5227 0.976265C18.8976 0.351144 18.0497 -4.57764e-05 17.1657 -4.57764e-05ZM12.2074 12.5H7.56569L8.53236 13.475C8.68757 13.6311 8.77469 13.8423 8.77469 14.0625C8.77469 14.2826 8.68757 14.4938 8.53236 14.65C8.45449 14.7272 8.36215 14.7883 8.26061 14.8298C8.15908 14.8712 8.05036 14.8923 7.94069 14.8916C7.72208 14.8907 7.51257 14.8039 7.35736 14.65L4.96569 12.25C4.84866 12.1337 4.76877 11.9853 4.73613 11.8236C4.70349 11.6619 4.71958 11.4942 4.78236 11.3416C4.84487 11.1894 4.95104 11.0592 5.08747 10.9672C5.22391 10.8753 5.3845 10.8258 5.54902 10.825H12.2157C12.4367 10.825 12.6487 10.9128 12.8049 11.069C12.9612 11.2253 13.049 11.4373 13.049 11.6583C13.049 11.8793 12.9612 12.0913 12.8049 12.2475C12.6487 12.4038 12.4367 12.4916 12.2157 12.4916L12.2074 12.5ZM16.2574 8.68329C16.1904 8.82794 16.0834 8.95036 15.949 9.03603C15.8146 9.12169 15.6584 9.16702 15.499 9.16662H8.83236C8.61134 9.16662 8.39938 9.07882 8.2431 8.92254C8.08682 8.76626 7.99902 8.5543 7.99902 8.33329C7.99902 8.11228 8.08682 7.90031 8.2431 7.74403C8.39938 7.58775 8.61134 7.49996 8.83236 7.49996H13.474L12.5074 6.53329C12.3521 6.37715 12.265 6.16594 12.265 5.94579C12.265 5.72563 12.3521 5.51442 12.5074 5.35829C12.6635 5.20308 12.8747 5.11596 13.0949 5.11596C13.315 5.11596 13.5262 5.20308 13.6824 5.35829L16.074 7.74162C16.1961 7.85992 16.2792 8.01273 16.3119 8.17956C16.3447 8.3464 16.3257 8.51926 16.2574 8.67496V8.68329Z" fill="#1EBA62"/>
																			</svg>
																		</span>
                                                                        {% endif %}
																		<div>
																			<h6 class="font-w500 fs-15 mb-0">
                                                                                {{ payment.date_created|date:'F d, Y - g:i A' }}
                                                                            </h6>
																		</div>
																	</div>
																</td>
																<td class="whitesp-no">
                                                                    {% if payment.is_cancellation %}
                                                                    {{ payment.subscription_type.name }} Subscription Canceled
                                                                    {% else %}
                                                                    {{ payment.subscription_type.name }} Subscription Payment
                                                                    {% endif %}
																</td>
																<td class="text-end">
                                                                    {% if payment.is_cancellation %}
                                                                        <span class=" btn light btn-danger btn-sm ">
                                                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                <path d="M5.50912 14.5C5.25012 14.5 4.99413 14.4005 4.80013 14.2065L1.79362 11.2C1.40213 10.809 1.40213 10.174 1.79362 9.78302C2.18512 9.39152 2.81913 9.39152 3.21063 9.78302L5.62812 12.2005L12.9306 7.18802C13.3866 6.87502 14.0106 6.99102 14.3236 7.44702C14.6371 7.90352 14.5211 8.52702 14.0646 8.84052L6.07613 14.324C5.90363 14.442 5.70612 14.5 5.50912 14.5Z" fill="#fd5353"/>
                                                                                <path d="M5.50912 8.98807C5.25012 8.98807 4.99413 8.88857 4.80013 8.69457L1.79362 5.68807C1.40213 5.29657 1.40213 4.66207 1.79362 4.27107C2.18512 3.87957 2.81913 3.87957 3.21063 4.27107L5.62812 6.68857L12.9306 1.67607C13.3866 1.36307 14.0106 1.47907 14.3236 1.93507C14.6371 2.39157 14.5211 3.01507 14.0646 3.32857L6.07613 8.81257C5.90363 8.93057 5.70612 8.98807 5.50912 8.98807Z" fill="#fd5353"/>
                                                                            </svg>
                                                                            Cancellation
                                                                        </span>
                                                                    {% else %}
                                                                    <span class="btn light btn-success btn-sm">
                                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
																	        <path d="M5.50912 14.5C5.25012 14.5 4.99413 14.4005 4.80013 14.2065L1.79362 11.2C1.40213 10.809 1.40213 10.174 1.79362 9.78302C2.18512 9.39152 2.81913 9.39152 3.21063 9.78302L5.62812 12.2005L12.9306 7.18802C13.3866 6.87502 14.0106 6.99102 14.3236 7.44702C14.6371 7.90352 14.5211 8.52702 14.0646 8.84052L6.07613 14.324C5.90363 14.442 5.70612 14.5 5.50912 14.5Z" fill="#1EBA62"/>
																	        <path d="M5.50912 8.98807C5.25012 8.98807 4.99413 8.88857 4.80013 8.69457L1.79362 5.68807C1.40213 5.29657 1.40213 4.66207 1.79362 4.27107C2.18512 3.87957 2.81913 3.87957 3.21063 4.27107L5.62812 6.68857L12.9306 1.67607C13.3866 1.36307 14.0106 1.47907 14.3236 1.93507C14.6371 2.39157 14.5211 3.01507 14.0646 3.32857L6.07613 8.81257C5.90363 8.93057 5.70612 8.98807 5.50912 8.98807Z" fill="#1EBA62"/>
																	    </svg>
																	    Complete
                                                                    </span>
                                                                    {% endif %}
																</td>
																<td class= "doller">${{ payment.price|floatformat:2|intcomma }}</td>
															</tr>
                                                            {% endfor %}
														</tbody>
													</table>
												</div>
                                                {% else %}

                                                <div class="col-xl-12">
                                                    <div class="card">
                                                        <div class="">
                                                            <div class="prot-blog">
                                                                <div class="d-flex post justify-content-between mb-3 align-items-center">
                                                                    <h2 class="text d-inline mb-3">You don't have any subscription purchases</h2>
                                                                </div>
                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">View Subscription Options</h2>
                                                                    <a href="/system/view_subscriptions/">Click to View <i class="fas fa-arrow-circle-right"></i></a>
                                                                </div>
                                                                <p class="mb-1">Purchase a subscription to get access to all our amazing features.</p>

                                                                <!--
                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">50,000 Credits $39.99</h2>
                                                                    <a href="/system/buy/50000_credits/">Buy 50,000 Credits <i class="fas fa-shopping-cart"></i></a>
                                                                </div>
                                                                <p class="mb-5">Purchase 50,000 Word Genius AI Credits (Equal to at least 50,000 words)</p>

                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">100,000 Credits $74.99</h2>
                                                                    <a href="/system/buy/100000_credits/">Buy 100,000 Credits <i class="fas fa-shopping-cart"></i></a>
                                                                </div>
                                                                <p class="mb-3">Purchase 100,000 Word Genius AI Credits (Equal to at least 100,000 words)</p>
                                                                -->



                                                                <div class="shape">
                                                                    <svg width="488" height="353" viewBox="0 0 488 353" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <mask id="mask0_51_1209" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="438" height="283">
                                                                        <rect width="438" height="283" fill="url(#paint0_linear_51_1209)"></rect>
                                                                        </mask>
                                                                        <g mask="url(#mask0_51_1209)">
                                                                        <path d="M165 410.5H15L465.5 88H487.5L165 410.5Z" fill="#ccecff"></path>
                                                                        <path d="M264 369.5H114L564.5 47H586.5L264 369.5Z" fill="#ccecff"></path>
                                                                        </g>
                                                                        <defs>
                                                                        <linearGradient id="paint0_linear_51_1209" x1="308.075" y1="-143.042" x2="316.634" y2="468.334" gradientUnits="userSpaceOnUse">
                                                                        <stop offset="0" stop-color="#363B64"></stop>
                                                                        <stop offset="1" stop-color="#4CBC9A"></stop>
                                                                        </linearGradient>
                                                                        </defs>
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                {% endif %}

											</div>
										</div>
									</div>
									<!--/column-->
                                </div>
								<!-- /row-->
							</div>
							<!--/column-->









                            <!--column-->
							<div class="col-xxl-11 col-xl-11">

								<div class="row">
									<div class="col-xl-12 wow fadeInUp" data-wow-delay="1.5s">
										<div class="card">
											<div class="card-header border-0 flex-wrap">
												<h2 class="heading">Credit Purchases</h2>
											</div>
											<div class="card-body py-0">
                                                {% if purchase_payments %}
												<div class="table-responsive">
													<table class="table-responsive-lg table display mb-4 order-table card-table text-black no-footer student-tbl">
														<tbody>
                                                            {% for payment in purchase_payments %}
															<tr>
																<td class="whitesp-no p-0">
																	<div class="d-flex py-sm-3 py-1 align-items-center trans-info">
																		<span class="icon me-3">
																			<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
																				<path d="M17.1657 -4.57764e-05H3.83236C2.9483 -4.57764e-05 2.10046 0.351144 1.47533 0.976265C0.850213 1.60139 0.499023 2.44923 0.499023 3.33329V16.6666C0.499023 17.5507 0.850213 18.3985 1.47533 19.0236C2.10046 19.6488 2.9483 20 3.83236 20H17.1657C18.0497 20 18.8976 19.6488 19.5227 19.0236C20.1478 18.3985 20.499 17.5507 20.499 16.6666V3.33329C20.499 2.44923 20.1478 1.60139 19.5227 0.976265C18.8976 0.351144 18.0497 -4.57764e-05 17.1657 -4.57764e-05ZM12.2074 12.5H7.56569L8.53236 13.475C8.68757 13.6311 8.77469 13.8423 8.77469 14.0625C8.77469 14.2826 8.68757 14.4938 8.53236 14.65C8.45449 14.7272 8.36215 14.7883 8.26061 14.8298C8.15908 14.8712 8.05036 14.8923 7.94069 14.8916C7.72208 14.8907 7.51257 14.8039 7.35736 14.65L4.96569 12.25C4.84866 12.1337 4.76877 11.9853 4.73613 11.8236C4.70349 11.6619 4.71958 11.4942 4.78236 11.3416C4.84487 11.1894 4.95104 11.0592 5.08747 10.9672C5.22391 10.8753 5.3845 10.8258 5.54902 10.825H12.2157C12.4367 10.825 12.6487 10.9128 12.8049 11.069C12.9612 11.2253 13.049 11.4373 13.049 11.6583C13.049 11.8793 12.9612 12.0913 12.8049 12.2475C12.6487 12.4038 12.4367 12.4916 12.2157 12.4916L12.2074 12.5ZM16.2574 8.68329C16.1904 8.82794 16.0834 8.95036 15.949 9.03603C15.8146 9.12169 15.6584 9.16702 15.499 9.16662H8.83236C8.61134 9.16662 8.39938 9.07882 8.2431 8.92254C8.08682 8.76626 7.99902 8.5543 7.99902 8.33329C7.99902 8.11228 8.08682 7.90031 8.2431 7.74403C8.39938 7.58775 8.61134 7.49996 8.83236 7.49996H13.474L12.5074 6.53329C12.3521 6.37715 12.265 6.16594 12.265 5.94579C12.265 5.72563 12.3521 5.51442 12.5074 5.35829C12.6635 5.20308 12.8747 5.11596 13.0949 5.11596C13.315 5.11596 13.5262 5.20308 13.6824 5.35829L16.074 7.74162C16.1961 7.85992 16.2792 8.01273 16.3119 8.17956C16.3447 8.3464 16.3257 8.51926 16.2574 8.67496V8.68329Z" fill="#1EBA62"/>
																			</svg>
																		</span>
																		<div>
																			<h6 class="font-w500 fs-15 mb-0">
                                                                                {{ payment.date_created|date:'F d, Y - g:i A' }}
                                                                            </h6>
																		</div>
																	</div>
																</td>
																<td class="whitesp-no">
                                                                    {{ payment.purchase_type.name }} Purchase Payment
																</td>
																<td class="text-end">
                                                                    <span class="btn light btn-success btn-sm">
                                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
																	        <path d="M5.50912 14.5C5.25012 14.5 4.99413 14.4005 4.80013 14.2065L1.79362 11.2C1.40213 10.809 1.40213 10.174 1.79362 9.78302C2.18512 9.39152 2.81913 9.39152 3.21063 9.78302L5.62812 12.2005L12.9306 7.18802C13.3866 6.87502 14.0106 6.99102 14.3236 7.44702C14.6371 7.90352 14.5211 8.52702 14.0646 8.84052L6.07613 14.324C5.90363 14.442 5.70612 14.5 5.50912 14.5Z" fill="#1EBA62"/>
																	        <path d="M5.50912 8.98807C5.25012 8.98807 4.99413 8.88857 4.80013 8.69457L1.79362 5.68807C1.40213 5.29657 1.40213 4.66207 1.79362 4.27107C2.18512 3.87957 2.81913 3.87957 3.21063 4.27107L5.62812 6.68857L12.9306 1.67607C13.3866 1.36307 14.0106 1.47907 14.3236 1.93507C14.6371 2.39157 14.5211 3.01507 14.0646 3.32857L6.07613 8.81257C5.90363 8.93057 5.70612 8.98807 5.50912 8.98807Z" fill="#1EBA62"/>
																	    </svg>
																	    Complete
                                                                    </span>
																</td>
																<td class= "doller">${{ payment.purchase_type.price|floatformat:2|intcomma }}</td>
															</tr>
                                                            {% endfor %}
														</tbody>
													</table>
												</div>
                                                {% else %}

                                                <div class="col-xl-12">
                                                    <div class="card">
                                                        <div class="">
                                                            <div class="prot-blog">
                                                                <div class="d-flex post justify-content-between mb-3 align-items-center">
                                                                    <h2 class="text d-inline mb-3">You don't have any credit purchases</h2>
                                                                </div>
                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">10,000 Credits $9.99</h2>
                                                                    <a href="/system/buy/10000_credits/">Buy 10,000 Credits <i class="fas fa-shopping-cart"></i></a>
                                                                </div>
                                                                <p class="mb-5">Purchase 10,000 Word Genius AI Credits (Equal to at least 10,000 words)</p>

                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">50,000 Credits $39.99</h2>
                                                                    <a href="/system/buy/50000_credits/">Buy 50,000 Credits <i class="fas fa-shopping-cart"></i></a>
                                                                </div>
                                                                <p class="mb-5">Purchase 50,000 Word Genius AI Credits (Equal to at least 50,000 words)</p>

                                                                <div class="d-flex fill justify-content-between align-items-center">
                                                                    <h2 class="text">100,000 Credits $74.99</h2>
                                                                    <a href="/system/buy/100000_credits/">Buy 100,000 Credits <i class="fas fa-shopping-cart"></i></a>
                                                                </div>
                                                                <p class="mb-1">Purchase 100,000 Word Genius AI Credits (Equal to at least 100,000 words)</p>

                                                                <div class="shape">
                                                                    <svg width="488" height="353" viewBox="0 0 488 353" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <mask id="mask0_51_1209" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="438" height="283">
                                                                        <rect width="438" height="283" fill="url(#paint0_linear_51_1209)"></rect>
                                                                        </mask>
                                                                        <g mask="url(#mask0_51_1209)">
                                                                        <path d="M165 410.5H15L465.5 88H487.5L165 410.5Z" fill="#ccecff"></path>
                                                                        <path d="M264 369.5H114L564.5 47H586.5L264 369.5Z" fill="#ccecff"></path>
                                                                        </g>
                                                                        <defs>
                                                                        <linearGradient id="paint0_linear_51_1209" x1="308.075" y1="-143.042" x2="316.634" y2="468.334" gradientUnits="userSpaceOnUse">
                                                                        <stop offset="0" stop-color="#363B64"></stop>
                                                                        <stop offset="1" stop-color="#4CBC9A"></stop>
                                                                        </linearGradient>
                                                                        </defs>
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                {% endif %}

											</div>
										</div>
									</div>
									<!--/column-->
                                </div>
								<!-- /row-->
							</div>
							<!--/column-->




























                </div>









{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
{% endblock javascripts %}