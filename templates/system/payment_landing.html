{% extends "layouts/base.html" %}
{% load humanize %}

{% block title %} Referral Commission Payout Form {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link rel='stylesheet' id='forms-style-css' href='/static/assets/site/css/forms.css' type='text/css' media='all' />
    <link rel='stylesheet' id='forms-theme-style-css' href='/static/assets/site/css/forms-theme.css' type='text/css' media='all' />
    <link href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
{% endblock stylesheets %}

<!-- Specific <body> classes goes HERE -->
{% block body_classes %}page-template page-template-page-register page-template-page-register-php page page-id-5 logged-in unscrolled{% endblock body_classes %}

{% block content %}






    <div id="content" class="m-b-50">

        <div class = "container">

            <div class="row">

                <div class="col-sm-3"></div>

                <div class="col-sm-6">

                    <h4>{{ referral.title }} Commission Payment</h4>



                {% if referral.commission_paid_in %}
                        <span class="message message-success p-20 gform_wrapper gravity-theme gfield" id="cc_error">
                            <i class="fas fa-exclamation-circle m-r-10"></i>
                            Thank you for your payment.  This Referral is now complete.
                        </span>
                {% else %}

                    {% if error %}
                        <span class="message message-danger p-20 gform_wrapper gravity-theme gfield" id="cc_error">
                            <i class="fas fa-exclamation-circle m-r-10"></i>
                            {{ error }}
                        </span>
                    {% endif %}

                    <hr class="m-b-20" />

                    <h5><b>Commission Amount:</b> <span class="justify-right">${{ referral.commission_amount|stringformat:".2f"|intcomma }}</span></h5>

                    {% if service_fee %}
                        <h5><b>Service Fee:</b> <span class="justify-right">${{ service_fee|stringformat:".2f"|intcomma }}</span></h5>
                        <h5><b>Final Total:</b> <span class="justify-right">${{ final_total|stringformat:".2f"|intcomma }}</span></h5>
                    {% endif %}

                    <hr class="m-b-30" />



                    <div class="gf_browser_chrome gform_wrapper gravity-theme m-b-50" id="gform_wrapper_1" style="">
                        <form method="post" enctype="multipart/form-data" id="payment_submit_form" action="">
                            {% csrf_token %}

                            <div class="gform_body gform-body">
                                <div id="gform_fields_1" class="gform_fields top_label form_sublabel_below description_below">


                                    <span class="message message-danger p-20 gform_wrapper gravity-theme gfield hidden" id="cc_error">
                                        <i class="fas fa-exclamation-circle m-r-10"></i>
                                        <span id="cc_error_message">Test 123</span>
                                    </span>


                                    <fieldset id="field_1_1" class="gfield gfield_contains_required field_sublabel_below field_description_below gfield_visibility_visible" data-js-reload="field_1_1">
                                        <legend class="gfield_label gfield_label_before_complex">
                                            Name
                                            <span class="gfield_required">
                                                <span class="gfield_required gfield_required_text">(Required)</span>
                                            </span>
                                        </legend>
                                        <div class="ginput_complex ginput_container no_prefix has_first_name no_middle_name has_last_name no_suffix gf_name_has_2 ginput_container_name" id="input_1_1">
                                            <span id="input_1_1_3_container" class="name_first">
                                                <input type="text" name="first_name" value="" placeholder="First Name" class="form-control" id="first_name" required>
                                            </span>
                                            <span id="input_1_1_6_container" class="name_last">
                                                <input type="text" name="last_name" value="" placeholder="Last Name" class="form-control" id="last_name" required>
                                            </span>
                                        </div>
                                    </fieldset>
                                    <fieldset id="field_1_10" class="gfield gfield--width-full gfield_contains_required field_sublabel_below field_description_below gfield_visibility_visible" data-js-reload="field_1_10">
                                        <legend class="gfield_label gfield_label_before_complex">
                                            Address
                                            <span class="gfield_required">
                                                <span class="gfield_required gfield_required_text">(Required)</span>
                                            </span>
                                        </legend>
                                        <div class="ginput_complex ginput_container has_zip ginput_container_address" id="input_1_10">
                                            <input type="text" name="address_1" value="" placeholder="Address" class="form-control" id="address_1" required>
                                        </div>
                                    </fieldset>
                                    <fieldset id="field_1_10" class="gfield gfield--width-full gfield_contains_required field_sublabel_below field_description_below gfield_visibility_visible" data-js-reload="field_1_10">
                                        <legend class="gfield_label gfield_label_before_complex">
                                            Address
                                            <span class="gfield_required">
                                                <span class="gfield_required gfield_required_text">(Required)</span>
                                            </span>
                                        </legend>
                                        <div class="ginput_complex ginput_container has_zip ginput_container_address" id="input_1_10">
                                            <input type="text" name="address_2" value="" placeholder="Address" class="form-control" id="address_2">
                                        </div>
                                    </fieldset>
                                    <fieldset id="field_1_10" class="gfield gfield--width-full gfield_contains_required field_sublabel_below field_description_below gfield_visibility_visible" data-js-reload="field_1_10">
                                        <legend class="gfield_label gfield_label_before_complex">
                                            City
                                            <span class="gfield_required">
                                                <span class="gfield_required gfield_required_text">(Required)</span>
                                            </span>
                                        </legend>
                                        <div class="ginput_complex ginput_container has_zip ginput_container_address" id="input_1_10">
                                            <input type="text" name="suburb" value="" placeholder="City" class="form-control" id="suburb" required>
                                        </div>
                                    </fieldset>
                                    <fieldset id="field_1_1" class="gfield gfield_contains_required field_sublabel_below field_description_below gfield_visibility_visible" data-js-reload="field_1_1">
                                        <legend class="gfield_label gfield_label_before_complex">
                                            State / Zip
                                            <span class="gfield_required">
                                                <span class="gfield_required gfield_required_text">(Required)</span>
                                            </span>
                                        </legend>
                                        <div class="ginput_complex ginput_container no_prefix has_first_name no_middle_name has_last_name no_suffix gf_name_has_2 ginput_container_name" id="input_1_1">
                                            <span id="input_1_1_3_container" class="name_first">
                                                <input type="text" name="state" value="" placeholder="State" class="form-control" id="state" required>
                                            </span>
                                            <span id="input_1_1_6_container" class="name_last">
                                                <input type="text" name="post_code" value="" placeholder="Zip"
                                                       class="form-control" id="post_code" required>
                                            </span>
                                        </div>
                                    </fieldset>

                                    <hr class="m-t-5" />

                                    <fieldset id="field_1_10" class="gfield gfield--width-full gfield_contains_required field_sublabel_below field_description_below gfield_visibility_visible" data-js-reload="field_1_10">
                                        <legend class="gfield_label gfield_label_before_complex">
                                            Credit Card Number
                                            <span class="gfield_required">
                                                <span class="gfield_required gfield_required_text">(Required)</span>
                                            </span>
                                        </legend>
                                        <div class="ginput_complex ginput_container has_zip ginput_container_address" id="input_1_10">
                                            <input type="hidden" name="strip_id" id="stripe_id" value="">
                                            <input type="text" name="credit_card_number" value="" placeholder="Credit Card Number"
                                                   class="form-control" id="credit_card_number" required>
                                        </div>
                                    </fieldset>
                                    <fieldset id="field_1_1" class="gfield gfield_contains_required field_sublabel_below field_description_below gfield_visibility_visible" data-js-reload="field_1_1">
                                        <legend class="gfield_label gfield_label_before_complex">
                                            Expire Date / CVC
                                            <span class="gfield_required">
                                                <span class="gfield_required gfield_required_text">(Required)</span>
                                            </span>
                                        </legend>
                                        <div class="ginput_complex ginput_container no_prefix has_first_name no_middle_name has_last_name no_suffix gf_name_has_2 ginput_container_name" id="input_1_1">
                                            <span id="input_1_1_3_container" class="name_first">
                                                <select class="exp_select" id="expiry_month" name="expiry_month" required>
                                                    <option value="">Month</option>
                                                    <option value="01">Jan</option>
                                                    <option value="02">Feb</option>
                                                    <option value="03">Mar</option>
                                                    <option value="04">Apr</option>
                                                    <option value="05">May</option>
                                                    <option value="06">Jun</option>
                                                    <option value="07">Jul</option>
                                                    <option value="08">Aug</option>
                                                    <option value="09">Sep</option>
                                                    <option value="10">Oct</option>
                                                    <option value="11">Nov</option>
                                                    <option value="12">Dec</option>
                                                </select> /
                                                <select class="exp_select" id="expiry_year" name="expiry_year" required>
                                                    <option value="">Year</option>
                                                    <option value="2022">2022</option>
                                                    <option value="2023">2023</option>
                                                    <option value="2024">2024</option>
                                                    <option value="2025">2025</option>
                                                    <option value="2026">2026</option>
                                                    <option value="2027">2027</option>
                                                    <option value="2028">2028</option>
                                                    <option value="2029">2029</option>
                                                    <option value="2030">2030</option>
                                                </select>
                                            </span>
                                            <span id="input_1_1_6_container" class="name_last">
                                                <input type="text" name="cvc" value="{{ user.postal_code }}" placeholder="CVC" class="form-control" id="cvc" required>
                                            </span>
                                        </div>
                                    </fieldset>


                                    <fieldset id="field_1_1" class="gfield m-t-20 gfield_contains_required field_sublabel_below field_description_below gfield_visibility_visible" data-js-reload="field_1_1">
                                        <button type="submit" id="profileButton" class="m-b-20">
                                            Pay Referral Fee <i class="fas fa-arrow-circle-right"></i>
                                        </button>
                                    </fieldset>
<!--
                                    <div class="gform_footer top_label m-b-40" id="submit_button_igot_cont">
                                        <input type="submit" id="payment_submit_button" class="gform_button button" value="Update Profile">
                                    </div>
-->




                                </div>
                            </div>
                        </form>
                    </div>


                {% endif %}



                </div>

                <div class="col-sm-3"></div>

            </div>

        </div>
    </div>



{{ soon }}



{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.1/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="https://js.stripe.com/v2/"></script>
    <script>
        jQuery(function() {
            Stripe.setPublishableKey('{{ stripe_publishable_key }}');

            jQuery('#payment_submit_form').submit(function(event) {
                event.preventDefault();
                var form = this;

                var valid = true;
                jQuery.each(jQuery("#payment_submit_form input.required"), function (index, value) {
                    alert("comes here");
                    if (!jQuery(value).val()){
                       valid = false;
                    }
                });
                if (!valid) {
                    jQuery('#cc_error_message').text('Please complete all missing fields.');
                    jQuery('#cc_error').show();
                    window.scrollTo(0, 0);
                    return;
                }






                var card = {
                    number:     jQuery('#credit_card_number').val(),
                    exp_month:   jQuery('#expiry_month').val(),
                    exp_year:    jQuery('#expiry_year').val(),
                    cvc:        jQuery('#cvc').val()
                };
                console.log(card);

                Stripe.card.createToken(card, function(status, response) {
                    if (response.error) {
                        // Show appropriate error to user cc_error cc_error_message
                        jQuery('#cc_error_message').text(response.error.message);
                        jQuery('#cc_error').show();
                        window.scrollTo(0, 0);
                        console.log(response.error.message)
                    } else {
                        // Get the token ID:
                        jQuery('#cc_error').hide();
                        var token = response.id;
                        jQuery('#stripe_id').val(response.id);
                        console.log(token);
                        form.submit();
                    }
                });

                return false;
            });

        });
    </script>
{% endblock javascripts %}


