{% extends "layouts/base.html" %}
{% load humanize %}

{% block title %} Profile {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
<style>
.prim-card::after {
    background-color: #3cd0a4 !important;
}
</style>
{% endblock stylesheets %}

{% block content %}


    <div class="row ">
        <div class="col-xl-12">
            <div class="page-titles">
                <div class="d-flex align-items-center flex-wrap ">
                    <h3 class="heading">Technical Support</h3>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row wow fadeInUp main-card" data-wow-delay="0.7s">
    
        <div class="col-xl-4 col-lg-4">
            <div class="widget-stat card bg-primary prim-card">
                <div class="card-body  p-4">
                    <div class="media">
                        <span class="me-3">
                            <i class="la la-users"></i>
                        </span>
                        <div class="media-body text-white">
                            <p class="mb-0 text-white">Customer</p>
                            <h3 class="text-white mb-2">Support</h3>
                            <div class="progress mb-2 bg-secondary">
                                <div class="progress-bar progress-animated bg-white" style="width: {{ days_percent }}%"></div>
                            </div>
                            <small>We've been Beta testing for {{ days_since }} days</small>
                        </div>
                    </div>
                    <hr class="text-white" />
                    <p class="text-white">
                        Prospect Connect is a brand-new application and currently in Beta testing
                        mode. As with any new software, there may be bugs or issues that impact
                        your experience.
                    </p>
                    <p class="text-white">
                        However, we want to assure you that we're committed to resolving any
                        difficulties you encounter. Even if you're facing minor issues, please
                        don't hesitate to contact us.
                    </p>
                </div>
            </div>
        </div>









        <div class="col-xl-8 col-lg-8">
            <div class="card">
                <div class="card-body">




                    <div class="comment-respond" id="respond">
                        <h4 class="comment-reply-title text-primary mb-3" id="reply-title">Contact Technical Support </h4>
                        <form class="comment-form" id="supportForm" method="post">
                            {% csrf_token %}
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <label for="section" class="text-black font-w600 form-label">Section <span class="required">*</span></label>
                                        <select class="default-select form-control wide form-control-lg" name="section" id="section" required>
                                            <option value="">Select One</option>
                                            <option value="suggestion request">Suggestion / Request</option>
                                            <option value="recommendation">Recommendation</option>
                                            <option value="leads">Leads</option>
                                            <option value="bots">Bots</option>
                                            <option value="salesman">Salesman</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <label for="subject" class="text-black font-w600 form-label">Subject <span class="required">*</span></label>
                                        <input type="text" class="form-control form-control-lg" placeholder="Subject" name="subject" id="subject" required>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <label for="comment" class="text-black font-w600 form-label">Message <span class="required">*</span></label>
                                        <textarea rows="5" class="form-control" name="message" placeholder="Message" id="message" required></textarea>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <button type="submit" class="submit btn btn-primary btn-lg" id="submit" name="submit">
                                            Send Message <i class="fas fa-paper-plane ms-2"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>



                </div>
            </div>
        </div>

    </div>
                





{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script>
    $(function() {
        
        $('#submit').click(function () {
            if($('#section').val() === '' || $('#subject').val() === '' || $('#message').val() === '') {
                Swal.fire(
                   'Missing Fields',
                   'Please complete all fields before submitting.',
                   'error'
                );
            } else {
                $(':submit').prop('disabled', true);
                $(':submit i').removeClass('fa-paper-plane').addClass('fa-spin fa-spinner');
            }
        });
    });
    </script>
{% endblock javascripts %}