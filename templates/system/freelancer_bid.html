{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} Bid {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link rel="stylesheet" href="/static/assets/vendor/swiper/css/swiper-bundle.min.css">
    <link rel="stylesheet" href="/static/assets/vendor/dropzone/dist/dropzone.css">
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}


    <div class="row">
        <div class="col-xl-12">



            <div class="card">
                <div class="card-body">
                    {% if project.bid_sent %}

                    <div class="d-flex align-items-start justify-content-between">
                        <div>
                          <h5 class="card-title fw-semibold">{{ project.title }}</h5>
                        </div>
                    </div>
                    <span class="fs-30 text-success fw-semibold lh-lg">
                        Bid Sent!
                    </span>

                    {% else %}

                    <div class="d-flex align-items-start justify-content-between">
                        <div>
                          <h5 class="card-title fw-semibold">{{ project.title }}</h5>
                          <p class="card-subtitle mb-0">
                              Budget
                              {{ project.currency_sign }}{{ project.budget_minimum|floatformat:2|intcomma }} -
                              {{ project.currency_sign }}{{ project.budget_maximum|floatformat:2|intcomma }}
                          </p>
                        </div>
                        <span class="fs-30 text-success fw-semiboldlh-lg">
                            {{ project.currency_sign }}{{ project.price|floatformat:2|intcomma }}
                            {% if project.currency_code != "USD" %}(${{ project.price_usd|floatformat:2|intcomma }} USD){% endif %}
                            - {{ project.bid_count }} bids
                        </span>
                    </div>

                    <p class="card-subtitle mb-3 mt-3 lh-base">
                        {{ project.description|linebreaksbr }}
                    </p>

                    <form action="" method="post">
                        {% csrf_token %}

                        <div class="mb-4">
                            <label class="form-label">Bid</label>
                            <div class="input-group">
                                <span class="input-group-text px-6" id="basic-addon1">
                                    <i class="ti ti-currency-dollar fs-6"></i>
                                </span>
                                <input type="text" class="form-control ps-2" value="{{ project.price|floatformat:2|intcomma }}" name="bid" id="bid">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Message</label>
                            <div class="input-group">
                              <textarea class="form-control p-2 ps-2" name="description" id="description" cols="20" rows="15">⚠️ IF YOU'RE NOT HAPPY YOU DON’T PAY ⚠️

I'm the most experienced developer on Freelancer, and I'm offering you something no one else will—a 100% guarantee of my work before you spend a single penny (I'll explain how below). This is the only true guarantee you’ll find here.

We have created many of these applications, including just in the past two months. One for an insurance company and another for a business that sells services to studios. Our expertise covers all the areas you need to build the perfect application for your project. Simply put, I’m the best fit for this job.

Here are a few of my recent projects that showcase my experience:

https://gptenterprise.ai/ (as seen on Fox, NBC, ABC & CBS)
https://socialspark.ai/
https://wordgenius.ai/
https://aighostwriter.co/
https://intelliclabs.com/

Before you invest anything, I will build the entire application. You risk nothing until the project is 100% complete and you're happy with the results. No one else offers a risk-free guarantee like this—because my track record speaks for itself.

Even though most of our business is conducted off Freelancer, my experience, speed, and results are unmatched. I can deliver your app in record time and with complete confidence.

Thank you for considering my bid, and I look forward to working with you. Let’s create something extraordinary together.</textarea>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            Place Bid
                            <i class="ti ti-circle-arrow-right me-0 ms-1 fs-4"></i>
                        </button>

                    </form>

                    {% endif %}

                </div>
            </div>

        </div>
    </div>



{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script>
        $(document).ready(function() {
            $('form').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');

                // Disable the button
                submitBtn.prop('disabled', true);

                // Change the icon to a spinning animation and update text
                submitBtn.html(`
                    Placing Bid...
                    <i class="ti ti-loader-2 ti-spin me-0 ms-1 fs-4"></i>
                `);
            });
        });
    </script>
{% endblock javascripts %}