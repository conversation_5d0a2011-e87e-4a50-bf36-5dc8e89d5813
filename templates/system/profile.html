{% extends "layouts/base.html" %}
{% load humanize %}

{% block title %} User Profile {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}


    <div class="row ">
        <div class="col-xl-12">
            <div class="page-titles">
                <div class="d-flex align-items-center flex-wrap ">
                    <h3 class="heading">Your Profile</h3>
                </div>
            </div>
        </div>
    </div>





    <div class="row wow fadeInUp main-card" data-wow-delay="0.7s">





        <div class="col-xl-3 col-lg-4">
            <div class="clearfix">
                <div class="card card-bx profile-card author-profile m-b30">
                    <div class="card-body">
                        <div class="p-5">
                            <div class="author-profile">
                                <div class="author-media">
                                    <img src="{% if user.avatar %}/get_image/{{ user.avatar }}{% else %}/static/assets/images/profile/default.png{% endif %}"
                                         alt="{{ user.first_name }} {{ user.last_name }} Avatar" id="profile-picture-img">
                                    <div class="upload-link" title="" id="update-profile-image">
                                        <!--<input type="file" class="update-flie">-->
                                        <i class="fa fa-camera"></i>
                                    </div>
                                </div>
                                <div class="author-info">
                                    <h6 class="title">{{ user.first_name }} {{ user.last_name }}</h6>
                                </div>
                            </div>
                        </div>
                        <div class="info-list">
                            <ul>
                                <!--
                                <li>Subscription:<span> {{ user.subscription_type }}</span></li>
                                <li>Expire:<span> {{ user.subscription_expire|date:'M d, Y' }}</span></li>
                                <li>Credits:<span> {{ user.credits|intcomma }}</span></li>
                                -->
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>









        <div class="col-xl-9 col-lg-8">
            <div class="card">
                <div class="card-body">

                    <form method="post">
                        {% csrf_token %}
                        <h5>Information</h5>{{ test }}
                        <hr>
                        <div class="row">

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label" for="first_name">First Name</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name"
                                           placeholder="First Name" value="{% if user.first_name %}{{ user.first_name }}{% endif %}">
                                </div>
                            
                                <div class="form-group mb-4">
                                    <label class="form-label" for="exampleInputEmail1">Email
                                        address</label>
                                    <input type="email" class="form-control" id="exampleInputEmail1"
                                           aria-describedby="emailHelp" placeholder="Enter email"
                                           value="{{ user.email }}" disabled>
                                    <small id="emailHelp" class="form-text text-muted">We'll never
                                        share your email with anyone else.
                                    </small>
                                </div>
                            
                            </div>
                            <div class="col-md-6">

                                <div class="form-group mb-3">
                                    <label class="form-label" for="last_name">Last Name</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name"
                                           placeholder="Last Name" value="{% if user.last_name %}{{ user.last_name }}{% endif %}">
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="phone">Phone Number</label>
                                    <input type="text" class="form-control" id="phone" name="phone"
                                           placeholder="Phone" value="{% if user.phone %}{{ user.phone }}{% endif %}">
                                </div>

                            </div>
                            <div class="col-12">
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="check1" value="" checked>
                                    <label class="form-check-label" for="check1">Check this box to receive SMS messages regarding your leads. Read the terms and privacy policy <a href="/">here</a>. Up to 5 messages per month, msg and data fees may apply. Reply STOP to cancel and HELP for help.</label>
                                </div>
                            </div>

                        </div>
                    
                        <!--
                        <h5>CopyScape Api Data</h5>
                        <p>A CopyScape account is required to use this feature.  Please visit the <a href="https://www.copyscape.com/apiconfigure.php#key" target="_blank" class="font-w800 text-primary">CopyScape API page</a> to get your information.</p>
                        <hr>
                        -->
                        <div class="row">

                            <div class="col-md-6">
                                <!--
                                <div class="form-group mb-3">
                                    <label class="form-label" for="copyscape_username">Copyscape Username</label>
                                    <input type="text" class="form-control" id="copyscape_username" name="copyscape_username"
                                           placeholder="CopyScape Username" value="{% if user.copyscape_username %}{{ user.copyscape_username }}{% endif %}">
                                </div>
                                -->
                                <button type="submit" class="btn btn-primary mt-3 mb-5">
                                    Update Profile
                                    <i class="fas fa-arrow-alt-circle-right me-0 ms-2"></i>
                                </button>
                            </div>
                            <div class="col-md-6">

                                <!--
                                <div class="form-group mb-4">
                                    <label class="form-label" for="copyscape_key">CopyScape Key</label>
                                    <input type="text" class="form-control" id="copyscape_key" name="copyscape_key"
                                           placeholder="CopyScape Key" value="{% if user.copyscape_key %}{{ user.copyscape_key }}{% endif %}">
                                </div>
                                -->

                            </div>
                        
                        </div>
                    
                    </form>
                
                </div>
            </div>
        </div>

      
    
    
    
    
    
    
    
    
    
    
    
    
    </div>





{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script>
    /*
     *
     */
    $(function() {
        $('form').on('submit', function() {
            $(':submit').prop('disabled', true);
            $(':submit i').removeClass('fa-arrow-alt-circle-right').addClass('fa-spin fa-spinner');
        });
        
        $('#update-profile-image').on('click', async function (e) {

            Swal.fire({
                title: 'Upload Your Avatar',
                input: 'file',
                type: 'info',
                inputAttributes: {
                    'accept': 'image/*',
                    'aria-label': 'Upload your avatar picture'
                },
                showCancelButton: true,
                confirmButtonText: 'Upload Avatar',
                showLoaderOnConfirm: true,
                preConfirm: (file) => {
                    const formData = new FormData();
                    formData.append('avatar', file);
                    formData.append('csrfmiddlewaretoken', $('input[name=csrfmiddlewaretoken]').val());

                    return fetch('/system/api/upload_avatar/', {
                        method: "POST",
                        body: formData
                    }).then(response => {
                        if (!response.ok) {
                            throw new Error(response.statusText)
                        }
                        return response.json()
                    }).catch(error => {
                        Swal.showValidationMessage(
                            'There was and error. Please try again.'
                        )
                    })
                },
                allowOutsideClick: () => !Swal.isLoading()
            }).then((result) => {
                const avatar = '/get_image/' + result.value.avatar;
                if(avatar) {
                    $('#profile-picture-img').attr('src', avatar);
                    $('#nav-profile-picture-img').attr('src', avatar);
                    Swal.fire(
                        'Success',
                        'Your avatar was successfully uploaded.',
                        'success'
                    );
                }
            });









            /*
            const {value: file} = await Swal.fire({
                title: 'Select image',
                input: 'file',
                inputAttributes: {
                    'accept': 'image/*',
                    'aria-label': 'Upload your profile picture'
                }
            });

            if (file) {

                let form_data = new FormData();
                form_data.append('avatar', file);

                $.ajax({
                    type:'POST',
                    url:'/system/api/upload_avatar/',
                    processData: false,
                    contentType: false,
                    data:{
                        logo:form_data,
                        csrfmiddlewaretoken:$('input[name=csrfmiddlewaretoken]').val(),
                    },
                    success: function(response)
                    {

                    },
                    error: function (response)
                    {

                    }
                });

            }
            */

        });
    });
    </script>
{% endblock javascripts %}