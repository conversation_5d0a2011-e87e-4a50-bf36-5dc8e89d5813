{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} Upload Leads {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link rel="stylesheet" href="/static/assets/vendor/swiper/css/swiper-bundle.min.css">
    <!--<link rel="stylesheet" href="/static/assets/vendor/dropzone/dist/dropzone.css">-->
    <link rel="stylesheet" href="/static/assets/libs/dropzone/dist/min/dropzone.min.css">
    <style>
    .custom-hr {
        border: none;
        border-top: 1px solid #333;
        width: 80%;
    }
    </style>
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}

    
        <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">
                        Upload Leads
                    </h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Leads
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Upload Leads
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    




            <div class="row">

                <!-- Box of article links -->
                <div class="col-xl-6">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-4 mb-sm-0 card-title">Your Upload File</h4>
                        </div>
                        <div class="card-body pt-0">
                            
                            
                            
                            <div class="profile-news">
                                
                                
                                {% if lead_upload %}
                                    
                                <div class="row">
                                    <div class="col-12">
                                        
                                        <span class="card text-bg-info text-white w-100 card-hover">
                                            <div class="card-body">
                                                <div class="d-flex align-items-center">
                                                    <i class="ti ti-file-ai display-6"></i>
                                                    <div class="ms-auto">
                                                        <i class="ti ti-arrow-right fs-8"></i>
                                                    </div>
                                                </div>
                                                <div class="mt-4">
                                                    <h4 class="card-title mb-1 text-white">
                                                        {{ lead_upload.lead_file_name }}
                                                    </h4>
                                                    <p class="card-text fw-normal text-white opacity-75">
                                                        Uploaded file
                                                    </p>
                                                </div>
                                            </div>
                                        </span>
                                        
                                    </div>    
                                </div>
                                    
                                {% else %}

                                <p>Upload a contact file to import leads into the bot system. One file at a time.</p>
                                <div class="dz-default mb-3">	
                                    <div class="dropzone upload-img text-center mb-3" id="documents-dropzone" xid="file-upload" enctype="multipart/form-data">
												
                                        <div class="fallback">
                                            <input name="file" type="file">
                                        </div>
                                        <svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M27.1666 26.6667L20.4999 20L13.8333 26.6667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M20.5 20V35" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M34.4833 30.6501C36.1088 29.7638 37.393 28.3615 38.1331 26.6644C38.8731 24.9673 39.027 23.0721 38.5703 21.2779C38.1136 19.4836 37.0724 17.8926 35.6111 16.7558C34.1497 15.619 32.3514 15.0013 30.4999 15.0001H28.3999C27.8955 13.0488 26.9552 11.2373 25.6498 9.70171C24.3445 8.16614 22.708 6.94647 20.8634 6.1344C19.0189 5.32233 17.0142 4.93899 15.0001 5.01319C12.9861 5.0874 11.015 5.61722 9.23523 6.56283C7.45541 7.50844 5.91312 8.84523 4.7243 10.4727C3.53549 12.1002 2.73108 13.9759 2.37157 15.959C2.01205 17.9421 2.10678 19.9809 2.64862 21.9222C3.19047 23.8634 4.16534 25.6565 5.49994 27.1667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M27.1666 26.6667L20.4999 20L13.8333 26.6667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
												
                                    </div>
                                    <small>
                                        <strong class="text-black">Important:</strong> 
                                        It's highly recommended hat the file be in CSV or XLS (Excel) format.  
                                        While the system does support many formats, the lead file should somewhat
                                        resemble your typical lead file with at least names and a phone number or email.
                                    </small>
                                </div>
                                    
                                {% endif %}
                            
                            </div>
                            
                        </div>
                    </div>
                </div>

            
            
            
            
            

                <!-- Box of links -->
                <div class="col-xl-6">

                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-4 mb-sm-0 card-title">Upload Details</h4>
                        </div>
                        <div class="card-body">
                            <div class="profile-news">
                                <!-- <h3 class="text-primary mb-3">Upload Details</h3> -->
                                
                                
                                {% if lead_upload %}
                                <div class="basic-form">
                                    <form method="post" class="needs-validation" novalidate>
                                        {% csrf_token %}
                                        <div class="row">
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label">Name <small>(for internal use only)</small></label>
                                                <input type="text" name="name" class="form-control" placeholder="Name of this upload" required>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">Lead Category</label>
                                                <select name="lead_category" class="default-select form-control form-select wide" required>
                                                    <option selected>Choose...</option>
                                                    {% for category in categories %}
                                                    <option value="{{ category.pk }}">{{ category.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                        
                                        
                                            <div class="mb-1 mt-1 row">
                                                <div class="col-sm-5">Skip Header Row?</div>
                                                <div class="col-sm-5">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="skip_header" checked>
                                                        <label class="form-check-label">
                                                            Yes
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        
                                            <div class="d-flex justify-content-center mb-3">
                                                <hr class="custom-hr" />
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    First Name <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_first_name" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    Last Name <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_last_name" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    Address <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_address" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    City <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_city" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    State <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_state" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    Zip <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_zip" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    Date of Birth <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_date_of_birth" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    Home Phone <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_phone_home" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    Work Phone <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_phone_work" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    Cell Phone <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_phone_cell" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    Email <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_email" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            <div class="mb-3 col-md-12">
                                                <label class="form-label mb-2">
                                                    IP Address <i class="fas fa-arrow-right"></i>
                                                </label>
                                                <select name="index_ip_address" class="default-select form-control form-select wide" required>
                                                    <option value="-1" selected>--Skip this field--</option>
                                                    {% for header in headers %}
                                                    <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        
                                            
                                        
                                            
                                        </div>
                                
                                        
                                        <button type="submit" class="btn btn-primary float-end">
                                            Import Leads <i class="ti ti-chevron-right fs-4 ms-2"></i>
                                        </button>
                                    

                                    
                                    </form>
                                </div>
                                {% endif %}
                                
                            </div>
                        </div>
                    </div>


                </div>
            
            
            
            
            
            </div>



{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <!--<script src="/static/assets/vendor/dropzone/dist/dropzone.js"></script>-->
    <script src="/static/assets/libs/dropzone/dist/min/dropzone.min.js"></script>
    <script src="/static/assets/vendor/apexchart/apexchart.js"></script>
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>
    <script>
    Dropzone.autoDiscover = false;
    $(document).ready(function() {
        /*
        $('form').on('submit', function() {
            $(':submit').prop('disabled', true);
            $(':submit i').removeClass('fa-cloud-upload-alt').addClass('fa-spin fa-spinner');
        });
        */
        
        $('#file-upload').submit(function() {
            popWorkingOverlay(true);
            return true;
        });
        
        
        // lead_api_upload_lead_file
        const csrf_token_header = {"X-CSRFToken": "{{ csrf_token }}"};
        $('#documents-dropzone').dropzone({
            url: "{% url 'lead_api_upload_lead_file' %}",
            headers: csrf_token_header,
            paramName: "file", // The name that will be used to transfer the file
            maxFilesize: 200, // MB
            method: "post",
            acceptedFiles: "text/plain,application/vnd.ms-excel,.xls,.xlsx",
            success: function (file, response) {
                popWorkingOverlay(true);
                console.log('success');
                console.log(response);
                console.log(response.id);
                console.log(response.url);
                window.location.href = response.url;
                //$('#' + drops[i] + '-preview').attr('src', drops_pre[i] + response.file);
                //$('#' + drops[i] + '-preview').removeClass('hide');
            },
        });
        
        
        
        
        var options = {
			series: [{{ percent }}],
			chart: {
				width: 180,
				height: 180,
				type: 'radialBar',
			},
			colors:["#20C997"],
			plotOptions: {
				radialBar: {
					startAngle: -180,
					endAngle: 180,
                    hollow: {
                        size: '60%',
						background: 'var(--rgba-primary-1)',
						margin:15
                    },
					dataLabels: {
                      show: true,
                      name: {
                        offsetY: 20,
                        show: true,
                        color: '#888',
                        fontSize: '12px'
                      },
                      value: {
                        formatter: function(val) {
                          return val + "%"
                        },
                        offsetY: -10,
                        color: '#000000',
                        fontWeight:700,
                        fontSize: '18px',
                        show: true,
                      }
                    },
					track: {
						background: '#FFF',
					}
				}
			},
            stroke: {
              lineCap: 'round'
            },
			labels: [''],
			responsive: [{
				breakpoint: 575,
				options: {
					chart: {
						height: 200,
					},
				}
			}],
        };
		var chart = new ApexCharts(document.querySelector("#redial"), options);
		chart.render();
    });
	</script>

{% endblock javascripts %}