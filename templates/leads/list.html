{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} List Leads {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link rel="stylesheet" href="/static/assets/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css" />
    <style>
    .xmore-btn {
        width: 23px !important;
        height: 23px !important;
        border-radius: 5px !important;
    }
    .dataTables_length {
        display: inline !important;
    }
    #prospect-leads_filter {
        display: inline !important;
        float: right !important;
    }
    .dataTables_info {
        display: inline !important;
        margin-top: 10px !important;
    }
    .dataTables_paginate {
        display: inline !important;
        float: right !important;
        margin-top: 10px !important;
    }
    .table-responsive {
        xoverflow-x: visible !important;
        height: auto;
    }
    table.table-bordered.dataTable th:first-child {
        border-top-left-radius: 10px;
    }
    table.table-bordered.dataTable th:last-child {
        border-top-right-radius: 10px;
    }
    table.table-bordered.dataTable tr:last-child td:first-child {
        border-bottom-left-radius: 10px;
    }
    table.table-bordered.dataTable tr:last-child td:last-child {
        border-bottom-right-radius: 10px;
    }
    </style>
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}
    
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Your Leads</h4>
                    <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#addLeadModal" class="ms-3">
                        <span class="badge rounded-pill  bg-secondary-subtle text-secondary">
                            Create New Lead <i class="fas fa-plus ms-2"></i>
                        </span>
                    </a>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Leads
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Your Leads
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    
    <div class="row">
        <div class="col-xl-12">
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="prospect-leads" class="table table-striped table-bordered text-nowrap align-middle">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Company</th>
                                    <th>State</th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
                
        </div>
    </div>
            
            
    <!-- -->
    <div class="modal fade" id="addLeadModal" tabindex="-1" aria-labelledby="addLeadModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    <div class="modal-header">
                        <h5 class="modal-title" id="addLeadModalLabel">Add Lead</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            {% for field in form %}
                            <div class="col-6">
                                <div class="mb-3 d-block">
                                    <label class="form-label d-block mb-2 for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    {{ field }}
                                    {% if field.help_text %}
                                    <small class="form-text text-muted">{{ field.help_text }}</small>
                                    {% endif %}
                                    {% if field.errors %}
                                    <div class="invalid-feedback">
                                        {{ field.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger light" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">
                            Add Lead
                            <i class="ti ti-chevron-right fs-4 ms-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    
{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <!--
    <script src="/static/assets/vendor/datatables/js/jquery.dataTables.min.js"></script>
    <script src="/static/assets/js/plugins-init/datatables.init.js"></script>
    -->
    <script src="/static/assets/libs/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>
    <script>
        
        
        $(document).ready(function() {
            function setTableHeight() {
                var contentHeight = $('.dataTables_wrapper')[0].scrollHeight;
                console.log(contentHeight);
                contentHeight += 40;
                console.log(contentHeight);
                $('.table-responsive').css('height', contentHeight + 'px');
            }
            /*
            $('form').on('submit', function() {
                $(':submit').prop('disabled', true);
                $(':submit i').removeClass('fa-arrow-alt-circle-right').addClass('fa-spin fa-spinner');
            });
             */
            
            
            
            
            
            
            $(document).on('click', '.sms-with-bot', function(){
                let id = $(this).data("id");
                let name = $(this).data("name");
                let phone = $(this).data("phone");
                console.log("ID: " + id);
                console.log("Name: " + name);
                console.log("Phone: " + phone);
                Swal.fire({
                    title: `Are you sure?`,
                    text: `Are you sure you want bot ${name} to SMS this contact?`,
                    showCancelButton: true,
                    confirmButtonText: "Yes",
                    cancelButtonText: "No",
                    icon: "question"
                }).then(result => {
                    console.log(result);
                    console.log(result.value);
                    if(result.value){
                        $.get("/leads/api/sms/" + id + "/" + phone + "/", {id: id, name: name})
                        .done(function(response){
                            Swal.fire('Queued!', 'Your contact was queued to receive a SMS from the bot "' + name + '."', 'success');
                        })
                        .fail(function(){
                            Swal.fire('Error!', 'An error occurred while making the request.', 'error');
                        });
                    }
                });
            });
            
            
            
            
            
            
            $(document).on('click', '.call-with-bot', function(){
                let id = $(this).data("id");
                let name = $(this).data("name");
                let phone = $(this).data("phone");
                console.log("ID: " + id);
                console.log("Name: " + name);
                console.log("Phone: " + phone);
                Swal.fire({
                    title: `Are you sure?`,
                    text: `Are you sure you want bot ${name} to call this contact?`,
                    showCancelButton: true,
                    confirmButtonText: "Yes",
                    cancelButtonText: "No",
                    icon: "question"
                }).then(result => {
                    console.log(result);
                    console.log(result.value);
                    if(result.value){
                        $.get("/leads/api/call/" + id + "/" + phone + "/", {id: id, name: name})
                        .done(function(response){
                            Swal.fire('Queued!', 'Your contact was queued to receive a call from the bot "' + name + '."', 'success');
                        })
                        .fail(function(){
                            Swal.fire('Error!', 'An error occurred while making the request.', 'error');
                        });
                    }
                });
            });
                        
            //lead_api_leads
            $('#prospect-leads').DataTable({
                processing: true,
                serverSide: true,
                drawCallback: function () {
                    setTableHeight();
                },
                ajax: '{% url "lead_api_leads" %}',
                language: {
                    paginate: {
                        previous: '<<', 
                        next: '>>' 
                    }
                },
                columns: [
                    { 
                        data: "name", 
                        orderable: true,
                        render: function(data, type, row) {
                            try {
                                return row.first_name + ' ' + row.last_name;
                            } catch (error) {
                                return '';    
                            }
                        }
                    },
                    { 
                        data: "lead_category__name", 
                        orderable: true
                    },
                    { data: "company" },
                    { data: "state" },
                    { 
                        data: "phone_work", 
                        orderable: false,
                        render: function(data, type, row) {
                            try {
                                return '<strong>(' + row.phone_work.slice(0, 3) + ') ' + row.phone_work.slice(3, 6) + '-' + row.phone_work.slice(6) + '</strong>';
                            } catch (error) {
                                return '';
                            }
                        }
                    },
                    { 
                        data: "email", 
                        orderable: false,
                        render: function(data, typ, row) {
                            try {
                                let email = row.email;
                                if (email.length > 30) {
                                    email = email.substr(0, 30) + "...";
                                }
                                return '<a href="mailto:' + row.email + '" class="tb-mail"><svg width="19" height="14" viewBox="0 0 19 14" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M18 0.889911C18.0057 0.823365 18.0057 0.756458 18 0.689911L17.91 0.499911C17.91 0.499911 17.91 0.429911 17.86 0.399911L17.81 0.349911L17.65 0.219911C17.6062 0.175413 17.5556 0.138269 17.5 0.109911L17.33 0.0499115H17.13H0.93H0.73L0.56 0.119911C0.504246 0.143681 0.453385 0.177588 0.41 0.219911L0.25 0.349911C0.25 0.349911 0.25 0.349911 0.25 0.399911C0.25 0.449911 0.25 0.469911 0.2 0.499911L0.11 0.689911C0.10434 0.756458 0.10434 0.823365 0.11 0.889911L0 0.999911V12.9999C0 13.2651 0.105357 13.5195 0.292893 13.707C0.48043 13.8946 0.734784 13.9999 1 13.9999H10C10.2652 13.9999 10.5196 13.8946 10.7071 13.707C10.8946 13.5195 11 13.2651 11 12.9999C11 12.7347 10.8946 12.4803 10.7071 12.2928C10.5196 12.1053 10.2652 11.9999 10 11.9999H2V2.99991L8.4 7.79991C8.5731 7.92973 8.78363 7.99991 9 7.99991C9.21637 7.99991 9.4269 7.92973 9.6 7.79991L16 2.99991V11.9999H14C13.7348 11.9999 13.4804 12.1053 13.2929 12.2928C13.1054 12.4803 13 12.7347 13 12.9999C13 13.2651 13.1054 13.5195 13.2929 13.707C13.4804 13.8946 13.7348 13.9999 14 13.9999H17C17.2652 13.9999 17.5196 13.8946 17.7071 13.707C17.8946 13.5195 18 13.2651 18 12.9999V0.999911C18 0.999911 18 0.929911 18 0.889911ZM9 5.74991L4 1.99991H14L9 5.74991Z" fill="#01A3FF"/> </svg><strong>' + email + '</strong></a>';
                            } catch (error) {
                                return '';
                            }
                        }
                    },
                    { 
                        data: "do_not_call", 
                        orderable: false,
                        render: function(data, type, row) {
                            try {
                                if (row.do_not_call) {
                                    return '<span class="badge  bg-danger-subtle text-danger"><i class="fa fa-circle text-danger me-1"></i>DNC</span>'; 
                                } else if (row.opted_out) {
                                    return '<span class="badge  bg-danger-subtle text-danger"><i class="fa fa-circle text-danger me-1"></i>Opted Out</span>';
                                } else {
                                    return '<span class="badge bg-success-subtle text-success border-2"><i class="fa fa-circle text-success me-1"></i>Active 1</span>';
                                }
                            } catch (error) {
                                return '';
                            }
                        }
                    },
                    {
                        data: 'id',
                        orderable: false,
                        render: function(data, type, row) {
                            console.log(row);
                            try {    
                                let botsLinkList = `{% for bot in bots %}<a class="dropdown-item call-with-bot" data-id="{{ bot.pk }}" data-name="{{ bot.name }}" data-phone="` + row.phone_cell + `" href="javascript:;">{{ bot.name }} - Cell Phone</a><a class="dropdown-item call-with-bot" data-id="{{ bot.pk }}" data-name="{{ bot.name }}" data-phone="` + row.phone_home + `" href="javascript:;">{{ bot.name }} - Home Phone</a><a class="dropdown-item sms-with-bot" data-id="{{ bot.pk }}" data-name="{{ bot.name }}" data-phone="` + row.phone_home + `" href="javascript:;">{{ bot.name }} SMS - Home Phone</a><a class="dropdown-item sms-with-bot" data-id="{{ bot.pk }}" data-name="{{ bot.name }}" data-phone="` + row.phone_cell + `" href="javascript:;">{{ bot.name }} SMS - Cell Phone</a>{% endfor %}`;
                                return '<div class="d-flex"> <a href="/leads/view/' + row.pk + '/" class="badge rounded-pill bg-secondary-subtle text-secondary me-1"> <i class="fas fa-pencil-alt"></i> </a> <a href="#" class="badge rounded-pill bg-danger-subtle text-danger me-1 me-1"> <i class="fa fa-trash"></i> </a> <a href="#" class="badge rounded-pill bg-danger-subtle text-danger me-1"> <i class="fas fa-phone-slash"></i> </a> <div class="dropdown text-end"> <div class="badge rounded-pill bg-secondary-subtle text-secondary more-btn" data-bs-toggle="dropdown"> <i class="fas fa-ellipsis-v"></i> </div> <div class="dropdown-menu dropdown-menu-end"> <h5 class="dropdown-header">Call with bot</h5> <div class="dropdown-divider"></div> ' + botsLinkList + ' </div> </div> </div>';
                            } catch (error) {
                                return '';
                            }
                        }
                    }
                ]
            });
        });
    </script>
{% endblock javascripts %}