{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} View / Edit Lead {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">
                        View / Edit Lead - {{ lead.first_name }} {{ lead.last_name }}
                    </h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Leads
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    View / Edit Lead
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    
    
    <div class="row">
            
        <div class="col-xl-5 col-lg-12 col-sm-12">
            <div class="card">
                    
                <div class="card-header p-0">
                    <img src="/static/assets/images/backgrounds/profilebg.jpg" alt="matdash-img" class="img-fluid rounded-top">
                    <div class="row align-items-center">
                        <div class="col-lg-3 order-lg-1 order-2"></div>
                        <div class="col-lg-6 mt-n3 order-lg-2 order-1">
                            <div class="mt-n5">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <div class="d-flex align-items-center justify-content-center round-110">
                                        <div class="border border-4 border-white d-flex align-items-center justify-content-center rounded-circle overflow-hidden round-100">
                                            <img src="/static/assets/images/profile/user-1.jpg" alt="matdash-img" class="w-100 h-100">
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mb-3">
                                    <h5 class="mb-0">{{ lead.first_name }} {{ lead.last_name }}</h5>
                                    <p class="mb-0">{{ lead.email }}</p>
                                    {% if lead.phone_cell %}
                                    <p class="mb-0">
                                        ({{ lead.phone_cell|slice:"0:3" }})
                                        {{ lead.phone_cell|slice:"3:6" }}-{{ lead.phone_cell|slice:"6:10" }}
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    
                <div class="card-body py-3">
                
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row p-0">
                            {% for field in form %}
                            <div class="col-6">
                                <div class="mb-3 d-block">
                                    <label class="form-label d-block mb-2 for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    {{ field }}
                                    {% if field.help_text %}
                                    <small class="form-text text-muted">{{ field.help_text }}</small>
                                    {% endif %}
                                    {% if field.errors %}
                                    <div class="invalid-feedback">
                                    {{ field.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        							
                        <button type="submit" class="btn btn-primary btn-lg btn-block w-100">
                            Update Lead
                            <i class="ti ti-chevron-right fs-4 ms-2"></i>							
                        </button>
                    </form>
                    
                    
                    <div class="row p-0">
                        <div class="col-12">
                            <hr />
                            <h4 class="card-title">{{ lead.first_name }} {{ lead.last_name }} Contact Notes</h4>
                                
                            {% for note in lead.lead_notes.all %}
                                
                                <span class="card text-bg-warning text-white w-100 card-hover">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-note display-6"></i>
                                            <div class="ms-auto">
                                                <h4 class="card-title mb-1 text-white">
                                                  {{ note.date_created|date:'F d, Y' }}
                                                </h4>
                                            </div>
                                        </div>
                                        <div class="mt-4">
                                            <h4 class="card-title mb-1 text-white">Note by: {{ note.user }}</h4>
                                            <p class="card-text fw-normal text-white opacity-75">
                                                {{ note.note }}
                                            </p>
                                        </div>
                                    </div>
                                </span>
                                
                            {% endfor %}
                                
                            <form method="post" action="{% url 'leads_add_note' lead.pk %}" class="needs-validation" novalidate>
                                {% csrf_token %}
                                    
                                <div class="mb-3">
                                    <label class="form-label d-block mb-2 mt-3 for="note">Add Note:</label>
                                    <textarea class="form-control" rows="5" id="note" name="note"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-lg btn-block  w-100">
                                    Add Lead Note
                                    <i class="ti ti-chevron-right fs-4 ms-2"></i>							
                                </button>
                            </form>
                                
                                
                        </div>
                    </div>
                    
                    
                    
                </div>
                    
                    
            </div>
        </div>
            
        <div class="col-xl-7 col-lg-12 col-sm-12">
            <div class="card">
                <div class="card-body py-3">
                    <h4 class="card-title">{{ lead.first_name }} {{ lead.last_name }} Contact History</h4>
                            
                    {% if connects %}
                    <div class="accordion mt-4" id="accordion-contacts">
                                    {% for connect in connects %}
                                    <div class="accordion-item">
                                        <div class="accordion-header {% if not forloop.first %}collapsed {% endif %}rounded-lg" id="heading{{ connect.id }}" data-bs-toggle="collapse" data-bs-target="#collapse{{ connect.id }}" aria-controls="collapse{{ connect.id }}" aria-expanded="true" role="button">
                                            <!--<span class="accordion-header-icon"></span>
                                            <span class="accordion-header-text">{{ connect.chat_bot__name }} - {% if connect.contact_type == 1 %}Call{% elif connect.contact_type == 2 %}Web Call{% elif connect.contact_type == 3 %}SMS{% else %}Unknown{% endif %} Contact</span>
                                            <span class="accordion-header-indicator"></span>
                                            --><button class="accordion-button" type="button" data-bs-toggle="collapse"
                                                data-bs-target="#collapse{{ connect.id }}" aria-expanded="true" aria-controls="collapse{{ connect.id }}">
                                                {{ connect.chat_bot__name }} - {% if connect.contact_type == 1 %}Call{% elif connect.contact_type == 2 %}Web Call{% elif connect.contact_type == 3 %}SMS{% else %}Unknown{% endif %} Contact
                                            </button>
                                        </div>
                                        <div id="collapse{{ connect.id }}" class="accordion-collapse collapse{% if forloop.first %} show{% endif %}" aria-labelledby="heading{{ connect.id }}" data-bs-parent="#accordion-contacts">
                                            <div class="accordion-body xp-2 mb-5">
                                                
                                                
                                                <div class="row exchange">
                                                    
                                                    <div class="col-12 mb-0 mt-3">
                                                        <h4>{% if connect.contact_type == 1 %}Call{% elif connect.contact_type == 2 %}Web Call{% elif connect.contact_type == 3 %}SMS{% else %}Unknown{% endif %} Summary</h4>
                                                        <p>{{ connect.analysis_summary }}</p>
                                                    </div>



                                                    <div class="col-4 mb-0">
                                                        <div class="card primary-gradient">
                                                            <div class="card-body text-center px-9 pb-4">
                                                              <div class="d-flex align-items-center justify-content-center round-48 rounded text-bg-primary flex-shrink-0 mb-3 mx-auto">
                                                                <iconify-icon icon="solar:clock-circle-outline" class="fs-7 text-white"></iconify-icon>
                                                              </div>
                                                              <h6 class="fw-normal fs-3 mb-1">{% if connect.contact_type == 1 %}Call{% elif connect.contact_type == 2 %}Web Call{% elif connect.contact_type == 3 %}SMS{% else %}Unknown{% endif %} Duration</h6>
                                                              <h4 class="mb-3 d-flex align-items-center justify-content-center gap-1">
                                                                {{ connect.duration_seconds }} Seconds<br />&nbsp;</h4>
                                                            </div>
                                                          </div>
                                                    </div>

                                                    <div class="col-4 mb-0">
                                                        <div class="card warning-gradient">
                                                        <div class="card-body text-center px-9 pb-4">
                                                          <div class="d-flex align-items-center justify-content-center round-48 rounded text-bg-warning flex-shrink-0 mb-3 mx-auto">
                                                            <iconify-icon icon="solar:hand-money-outline" class="fs-7 text-white"></iconify-icon>
                                                          </div>
                                                          <h6 class="fw-normal fs-3 mb-1">{% if connect.contact_type == 1 %}Call{% elif connect.contact_type == 2 %}Web Call{% elif connect.contact_type == 3 %}SMS{% else %}Unknown{% endif %} Cost</h6>
                                                          <h4 class="mb-3 d-flex align-items-center justify-content-center gap-1">
                                                            ${{ connect.cost }}<br />&nbsp;</h4>
                                                        </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-4 mb-0">
                                                    <div class="card secondary-gradient">
                                                        <div class="card-body text-center px-9 pb-4">
                                                          <div class="d-flex align-items-center justify-content-center round-48 rounded text-bg-secondary flex-shrink-0 mb-3 mx-auto">
                                                            <iconify-icon icon="solar:clock-circle-outline" class="fs-7 text-white"></iconify-icon>
                                                          </div>
                                                          <h6 class="fw-normal fs-3 mb-1">{% if connect.contact_type == 1 %}Call{% elif connect.contact_type == 2 %}Web Call{% elif connect.contact_type == 3 %}SMS{% else %}Unknown{% endif %} Start</h6>
                                                          <h4 class="mb-3 d-flex align-items-center justify-content-center gap-1">
                                                            {{ connect.date_contacted_start|date:"M d, y" }}<br />
                                                          {{ connect.date_contacted_start|date:"g:i a" }}</h4>
                                                        </div>
                                                      </div>
                                                    </div>



                                                
                                                    <div class="col-4 mb-0">

                                                        <div class="card text-white text-center {% if connect.success_evaluation %}text-bg-success{% else %}text-bg-danger{% endif %}">
                                                          <div class="card-body p-4">
                                                            <span>
                                                              {% if connect.success_evaluation %}
                                                              <iconify-icon icon="solar:check-circle-outline" class="fs-8 text-white display-6"></iconify-icon>
                                                              {% else %}
                                                              <iconify-icon icon="solar:close-circle-outline" class="fs-8 text-white display-6"></iconify-icon>
                                                              {% endif %}
                                                            </span>
                                                            <h3 class="card-title mt-3 mb-0 text-white font-weight-bolder"><strong>Success</strong></h3>
                                                          </div>
                                                        </div>

                                                    </div>
                                                    
                                                    <div class="col-4 mb-0">

                                                        <div class="card text-white text-center {% if connect.forwarded_call %}text-bg-success{% else %}text-bg-danger{% endif %}">
                                                          <div class="card-body p-4">
                                                            <span>
                                                              {% if connect.forwarded_call %}
                                                              <iconify-icon icon="solar:check-circle-outline" class="fs-8 text-white display-6"></iconify-icon>
                                                              {% else %}
                                                              <iconify-icon icon="solar:close-circle-outline" class="fs-8 text-white display-6"></iconify-icon>
                                                              {% endif %}
                                                            </span>
                                                            <h3 class="card-title mt-3 mb-0 text-white font-weight-bolder"><strong>Forwarded</strong></h3>
                                                          </div>
                                                        </div>

                                                    </div>
                                                    
                                                    <div class="col-4 mb-0">

                                                        <div class="card text-white text-center {% if connect.scheduled_appointment %}text-bg-success{% else %}text-bg-danger{% endif %}">
                                                          <div class="card-body p-4">
                                                            <span>
                                                              {% if connect.scheduled_appointment %}
                                                              <iconify-icon icon="solar:check-circle-outline" class="fs-8 text-white display-6"></iconify-icon>
                                                              {% else %}
                                                              <iconify-icon icon="solar:close-circle-outline" class="fs-8 text-white display-6"></iconify-icon>
                                                              {% endif %}
                                                            </span>
                                                            <h3 class="card-title mt-3 mb-0 text-white font-weight-bolder"><strong>Appointment</strong></h3>
                                                          </div>
                                                        </div>

                                                    </div>
                                                
                                                    {% if connect.contact_type == 1 or connect.contact_type == 2 %}
                                                    <div class="col-6 mb-4">
                                                        <h4>Call Recording</h4>
                                                        <audio controls class="w-100">
                                                            <source src="/get_audio/{{ connect.recording_file }}" type="audio/wav">
                                                            Your browser does not support the audio element.
                                                        </audio>
                                                    </div>
                                                    
                                                    <div class="col-6 mb-4">
                                                        <h4>Call Recording (Stereo)</h4>
                                                        <audio controls class="w-100">
                                                            <source src="/get_audio/{{ connect.stereo_recording_file }}" type="audio/wav">
                                                            Your browser does not support the audio element.
                                                        </audio>
                                                    </div>
                                                    {% endif %}
                                                    
                                                    <div class="col-12">


                                                            <h4>Conversation Transcript</h4>

                      <div class="chat-box rounded-4 border w-100">
                        <div class="chat-box-inner p-9" data-simplebar>
                          <div class="chat-list chat active-chat" data-user-id="1">
                              {% for message in connect.messages %}

                                                                    {% if message.is_ai_message %}
                            <div class="hstack gap-3 align-items-start mb-7 justify-content-start">
                              <img src="/static/assets/images/profile/user-8.jpg" alt="user8" width="40" height="40" class="rounded-circle" />
                              <div>
                                <h6 class="fs-2 text-muted">
                                  AI Bot
                                </h6>
                                <div class="p-2 text-bg-light rounded-1 d-inline-block text-dark fs-3">
                                  {{ message.message }}
                                </div>
                              </div>
                            </div>
                                  {% else %}
                            <div class="hstack gap-3 align-items-start mb-7 justify-content-end">
                              <div class="text-end">
                                <h6 class="fs-2 text-muted">{{ message.chat_bot_contact.name }}</h6>
                                <div class="p-2 bg-info-subtle text-dark rounded-1 d-inline-block fs-3">
                                  {{ message.message }}
                                </div>
                              </div>
                            </div>
                                  {% endif %}
                                  {% endfor %}

                          </div>
                        </div>
                      </div>




                                                    </div>
                                                    
                                                </div>
                                                
                                                
                                                
                                                
                                                
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                
                                </div>
                    {% else %}
                    <!--
                    <h5 class="mt-3">This lead has not yet been contacted</h5>
                    -->
                    
                    <span class="card text-bg-primary text-white w-100 card-hover">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-phone-calling display-6"></i>
                                <div class="ms-auto">
                                    <i class="ti ti-arrow-down fs-8"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="card-title mb-1 text-white">
                                    This lead has not yet been contacted
                                </h4>
                                <p class="card-text fw-normal text-white opacity-75"></p>
                            </div>
                        </div>
                    </span>
                        
                    {% endif %} 
                </div>
            </div>
        </div>
            
    </div>
            
            
            
            


{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>
    <script>
        $(document).ready(function() {
            
           /*
           $('form').on('submit', function() {
                $(':submit').prop('disabled', true);
                $(':submit i').removeClass('fa-arrow-alt-circle-right').addClass('fa-spin fa-spinner');
            });
            */
        
        });
    </script>
{% endblock javascripts %}
