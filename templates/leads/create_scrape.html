{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} Create Scrape {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link rel="stylesheet" href="/static/assets/libs/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-3-typeahead/4.0.2/bootstrap3-typeahead.min.css">
    <style>
    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        position: relative !important;
        top: 100% !important;
    }
    </style>
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}





   
    
            
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Create Scrape</h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Leads
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Create Scrape
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
            
            
            
            
            
    <div class="row">
        <div class="col-12">
                    
                    
                    
                    
                    
                    
            <div class="card">
                <div class="card-body">

                    <!--
                    <h4 class="card-title">Nav Pills Tabs - Left Align</h4>
                    -->

                    <div>
                        <!-- Nav tabs -->
                        <ul class="nav nav-pills nav-fill" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link d-flex active" data-bs-toggle="tab" href="#tab-google" role="tab">
                                    <span>
                                        <i class="ti ti-map-pin fs-4"></i>
                                    </span>
                                    <span class="d-none d-md-block ms-2">Google Maps</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex" data-bs-toggle="tab" href="#tab-yelp" role="tab">
                                    <span>
                                        <i class="fa-brands fa-yelp fs-4"></i>
                                    </span>
                                    <span class="d-none d-md-block ms-2">Yelp</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex" data-bs-toggle="tab" href="#tab-yellow" role="tab">
                                    <span>
                                        <i class="ti ti-book fs-4"></i>
                                    </span>
                                    <span class="d-none d-md-block ms-2">Yellow Pages</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex" data-bs-toggle="tab" href="#tab-page" role="tab">
                                    <span>
                                        <i class="ti ti-file-arrow-left fs-4"></i>
                                    </span>
                                    <span class="d-none d-md-block ms-2">Page Scrape</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link d-flex" data-bs-toggle="tab" href="#tab-directory" role="tab">
                                    <span>
                                        <i class="ti ti-sitemap fs-4"></i>
                                    </span>
                                    <span class="d-none d-md-block ms-2">Directory Scrape</span>
                                </a>
                            </li>
                        </ul>
                        
                        <!-- Tab panes -->
                        <div class="tab-content border mt-2">
                            <div class="tab-pane active p-3" id="tab-google" role="tabpanel">
                                
                                <form action="" method="post" class="needs-validation" novalidate>
                                    {% csrf_token %}
                                    <input type="hidden" name="job_type" value="0">
                                            
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label" for="name">Name :</label>
                                                <input type="text" class="form-control" id="name" name="name" required />
                                                <span class="help-block">
                                                    <small>
                                                        The name is for internal use only, allowing you to easily track and manage scrapes. Be detailed.
                                                    </small>
                                                </span>
                                            </div>
                                        </div>
                                    
                                        <div class="col-md-12 mb-3 mt-1">
                                            <h5 class="card-title fw-semibold">Google Scrape Details</h5>
                                            <p class="card-subtitle mb-0 lh-base">Please enter in at least one term, location or category to search for on Google Maps/Places.</p>
                                        </div>
                                    
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="search_term">Search Term :</label>
                                                <input type="text" class="form-control" id="search_term" name="search_term" />
                                            </div>
                                        </div>
                                    
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="location">Location :</label>
                                                <div id="location">
                                                    <input type="text" class="typeahead form-control" id="location" name="location" />
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="category">Category :</label>
                                                <select class="select2 border form-select" id="category" name="category">
                                                    <option value="">Select Category</option>
                                                    {% for category in categories %}
                                                    <option value="{{ category.value }}">{{ category.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    
                                        <hr />
                                    
                                        <div class="col-md-12 mb-3 mt-1">
                                            <h5 class="card-title fw-semibold">Internal Details</h5>
                                            <p class="card-subtitle mb-0 lh-base">The information below is used to keep track of leads internally.</p>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="lead_category">Lead Category :</label>
                                                <select class="select2 border form-select" id="lead_category" name="lead_category">
                                                    <option value="">Select Lead Category</option>
                                                    {% for lead_category in lead_categories %}
                                                    <option value="{{ lead_category.pk }}">{{ lead_category.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="language">Language :</label>
                                                <select class="form-select" id="language" name="language" required>
                                                    <!-- <option value="">Select language</option> -->
                                                    <option value="en">English</option>
                                                </select>
                                            </div>
                                        </div>
                                    
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="lead_quantity">Max Leads :</label>
                                                <input type="number" class="form-control" name="lead_quantity" id="lead_quantity" value="50" disabled>
                                                <span class="help-block">
                                                    <small>
                                                        The maximum number of leads to scrape.
                                                    </small>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                
                                    <div class="row">  
                                        <div class="col-12">
                                            <div class="d-md-flex align-items-center">
                                                <div class="ms-auto mt-3 mt-md-0">
                                                    <button type="submit" class="btn btn-primary hstack gap-6">
                                                        Create Scrape
                                                        <i class="ti ti-chevron-right fs-4 ms-2"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>    
                                    </div>
                                
                                </form>
                                    
                            </div>
                        
                            <div class="tab-pane p-3" id="tab-yelp" role="tabpanel">
                                
                                <form action="" method="post" class="needs-validation" novalidate>
                                    {% csrf_token %}
                                    <input type="hidden" name="job_type" value="2">
                                            
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label" for="name">Name :</label>
                                                <input type="text" class="form-control" id="name" name="name" required />
                                                <span class="help-block">
                                                    <small>
                                                        The name is for internal use only, allowing you to easily track and manage scrapes. Be detailed.
                                                    </small>
                                                </span>
                                            </div> 
                                        </div>
                                    
                                        <div class="col-md-12 mb-3 mt-1">
                                            <h5 class="card-title fw-semibold">Yelp Scrape Details</h5>
                                            <p class="card-subtitle mb-0 lh-base">Please enter in at least one term or location to search for on Yelp.</p>
                                        </div>
                                    
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="search_term">Search Term :</label>
                                                <input type="text" class="form-control" id="search_term" name="search_term" />
                                            </div>
                                        </div>
                                    
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="location">Location :</label>
                                                <div id="location">
                                                    <input type="text" class="typeahead form-control" id="location" name="location" />
                                                </div>
                                            </div>
                                        </div>
                                    
                                        <hr />
                                    
                                        <div class="col-md-12 mb-3 mt-1">
                                            <h5 class="card-title fw-semibold">Internal Details</h5>
                                            <p class="card-subtitle mb-0 lh-base">The information below is used to keep track of leads internally.</p>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="lead_category2">Lead Category :</label>
                                                <select class="select2 border form-select" id="lead_category2" name="lead_category">
                                                    <option value="">Select Lead Category</option>
                                                    {% for lead_category in lead_categories %}
                                                    <option value="{{ lead_category.pk }}">{{ lead_category.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="language2">Language :</label>
                                                <select class="form-select" id="language2" name="language" required>
                                                    <!-- <option value="">Select language</option> -->
                                                    <option value="en">English</option>
                                                </select>
                                            </div>
                                        </div>
                                    
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="lead_quantity">Max Leads :</label>
                                                <input type="number" class="form-control" name="lead_quantity" id="lead_quantity" value="50" disabled>
                                                <span class="help-block">
                                                    <small>
                                                        The maximum number of leads to scrape.
                                                    </small>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                
                                    <div class="row">  
                                        <div class="col-12">
                                            <div class="d-md-flex align-items-center">
                                                <div class="ms-auto mt-3 mt-md-0">
                                                    <button type="submit" class="btn btn-primary hstack gap-6">
                                                        Create Scrape
                                                        <i class="ti ti-chevron-right fs-4 ms-2"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>    
                                    </div>
                                
                                </form>
                                    
                            </div>
                        
                            <div class="tab-pane p-3" id="tab-yellow" role="tabpanel">
                                
                                <form action="" method="post" class="needs-validation" novalidate>
                                    {% csrf_token %}
                                    <input type="hidden" name="job_type" value="3">
                                            
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label" for="name">Name :</label>
                                                <input type="text" class="form-control" id="name" name="name" required />
                                                <span class="help-block">
                                                    <small>
                                                        The name is for internal use only, allowing you to easily track and manage scrapes. Be detailed.
                                                    </small>
                                                </span>
                                            </div>
                                        </div>
                                    
                                        <div class="col-md-12 mb-3 mt-1">
                                            <h5 class="card-title fw-semibold">Yellow Pages Scrape Details</h5>
                                            <p class="card-subtitle mb-0 lh-base">Please enter in at least one term or location to search for on Yellow Pages.</p>
                                        </div>
                                    
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="search_term">Search Term :</label>
                                                <input type="text" class="form-control" id="search_term" name="search_term" />
                                            </div>
                                        </div>
                                    
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label" for="location">Location :</label>
                                                <div id="location">
                                                    <input type="text" class="typeahead form-control" id="location" name="location" />
                                                </div>
                                            </div>
                                        </div>
                                    
                                        <hr />
                                    
                                        <div class="col-md-12 mb-3 mt-1">
                                            <h5 class="card-title fw-semibold">Internal Details</h5>
                                            <p class="card-subtitle mb-0 lh-base">The information below is used to keep track of leads internally.</p>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="lead_category3">Lead Category :</label>
                                                <select class="select2 border form-select" id="lead_category3" name="lead_category">
                                                    <option value="">Select Lead Category</option>
                                                    {% for lead_category in lead_categories %}
                                                    <option value="{{ lead_category.pk }}">{{ lead_category.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="language3">Language :</label>
                                                <select class="form-select" id="language3" name="language" required>
                                                    <!-- <option value="">Select language</option> -->
                                                    <option value="en">English</option>
                                                </select>
                                            </div>
                                        </div>
                                    
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label" for="lead_quantity">Max Leads :</label>
                                                <input type="number" class="form-control" name="lead_quantity" id="lead_quantity" value="50" disabled>
                                                <span class="help-block">
                                                    <small>
                                                        The maximum number of leads to scrape.
                                                    </small>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                
                                    <div class="row">  
                                        <div class="col-12">
                                            <div class="d-md-flex align-items-center">
                                                <div class="ms-auto mt-3 mt-md-0">
                                                    <button type="submit" class="btn btn-primary hstack gap-6">
                                                        Create Scrape
                                                        <i class="ti ti-chevron-right fs-4 ms-2"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>    
                                    </div>
                                
                                </form>
                                    
                            </div>
                        
                            <div class="tab-pane p-3" id="tab-page" role="tabpanel">
                                <div class="row mt-3">
                                    <div class="col-md-8">
                                        <div class="alert customize-alert alert-dismissible rounded-pill text-primary border border-primary fade show remove-close-icon" role="alert">
                                            <div class="d-flex align-items-center  me-3 me-md-0">
                                                <i class="ti ti-info-circle fs-5 me-2 flex-shrink-0 text-primary"></i>
                                                This feature is pending discussion. 
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                            <div class="tab-pane p-3" id="tab-directory" role="tabpanel">
                                <div class="row mt-3">
                                    <div class="col-md-8">
                                        <div class="alert customize-alert alert-dismissible rounded-pill text-primary border border-primary fade show remove-close-icon" role="alert">
                                            <div class="d-flex align-items-center  me-3 me-md-0">
                                                <i class="ti ti-info-circle fs-5 me-2 flex-shrink-0 text-primary"></i>
                                                This feature is pending discussion. 
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                        
                        
                        
                        
                        </div>
                        
                    </div>
                </div>
            </div>
            
            
            
            
            
            
        </div>
    </div>
            
            
            
            
            
            
            
            












    
    
    
    

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/libs/select2/dist/js/select2.full.min.js"></script>
    <script src="/static/assets/libs/select2/dist/js/select2.min.js"></script>
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>
    <!--
    <script src="/static/assets/libs/typeahead.js/dist/typeahead.jquery.min.js"></script>
    <script src="/static/assets/libs/typeahead.js/dist/bloodhound.min.js"></script>
    -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-3-typeahead/4.0.2/bootstrap3-typeahead.min.js"></script>
    <script>
    $(document).ready(function(){
        $(".select2").select2();
        
        var autocompleteBox = $('#location .typeahead');

        autocompleteBox.typeahead({
            source: function(query, process) {
                console.log(query);
                console.log(process);
                if (query.length >= 4) {  // Check if query length is more than or equal to 4
                    return $.get('{% url 'lead_api_locations' %}', { query: query, 'format': 'json' }, function (data) {
                        console.log(data);
                        return process(data);
                    });
                }
            }
        });
        
    });
    </script>
{% endblock javascripts %}