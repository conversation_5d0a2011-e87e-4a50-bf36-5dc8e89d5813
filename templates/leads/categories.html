{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} Lead Categories {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link rel="stylesheet" href="/static/assets/vendor/swiper/css/swiper-bundle.min.css">
    <link rel="stylesheet" href="/static/assets/vendor/dropzone/dist/dropzone.css">
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Lead Categories</h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Leads
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Lead Categories
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    
    
    
    
    
    
    
    


        
    <div class="row">

        <!-- Box of links -->
        <div class="col-xl-8">
                

                    <div class="card">
                        <div class="card-body py-3">
                        
                            <h4 class="card-title">Your Lead Categories</h4>
                            
                            <div class="row mt-4">
                                
                                {% for category in lead_categories %}
                                <div class="col-md-6">
                                
                      <div class="card primary-gradient">
                        <div class="card-body text-center px-9 pb-4">
                          <div class="d-flex align-items-center justify-content-center round-48 rounded text-bg-primary flex-shrink-0 mb-3 mx-auto">
                            <iconify-icon icon="solar:pin-circle-line-duotone" class="fs-7 text-white"></iconify-icon>
                          </div>
                          <h6 class="fw-normal fs-3 mb-1">{{ category.0 }}</h6>
                            {% if category.1 %}<small>{{ category.1 }}</small>{% endif %}
                          <h4 class="mb-3 d-flex align-items-center justify-content-center gap-1">
                            {{ category.2 }} Leads</h4>
                        </div>
                      </div>
                                
                                
                                
                                
                                </div>
                                {% endfor %}
                            </div>
                            
                        </div>
                    </div>

                
        </div>

                <!-- Box of article links -->
                <div class="col-xl-4">
                    <div class="card">
                        <div class="card-body py-3">
                                <h4 class="card-title">Add Lead Category</h4>

                                <form method="post" class="needs-validation mt-4" novalidate>
                                    {% csrf_token %}
                                    
                                    <div class="row">
                                    
                                        <div class="mb-4 col-md-12">
                                            <label class="form-label mb-1" for="name">Name:</label>
                                            <input type="text" id="name" name="name" class="form-control" placeholder="Category Name" required>
                                            <small>Name your category, for your eyes only.</small>
                                            {% if form.name.errors %}
                                                <small class="text-danger">{{ form.name.errors }}</small>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-4 col-md-12">
                                            <label class="form-label mb-1" for="description">Description (optional):</label>
                                            <textarea class="form-control" name="description" id="description" rows="5" placeholder="Category Description"></textarea>
                                            <small>Describe your category, this is for your benefit.</small>
                                        </div>
                                    
                                        <div class="mb-4 col-md-12">
                                            <button class="btn btn-primary me-2" type="submit">
                                                Create Lead Category 
                                                <i class="ti ti-chevron-right fs-4 ms-2"></i>
                                            </button>
                                        </div>
                                    
                                    </div>
                                
                                </form>
                                    
                            
                        </div>
                    </div>
                </div>

            </div>

    


{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>
    <script>
        /*
        $('form').on('submit', function() {
            $(':submit').prop('disabled', true);
            $(':submit i').removeClass('fa-arrow-alt-circle-right').addClass('fa-spin fa-spinner');
        });
        */
	</script>
{% endblock javascripts %}