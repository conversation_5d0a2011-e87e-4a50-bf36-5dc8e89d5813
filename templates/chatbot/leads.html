{% extends "layouts/base.html" %}

{% block title %} Chat <PERSON>t Leads {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link href="/assets-auth/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="/assets-auth/css/datatables2.css">
{% endblock stylesheets %}

{% block content %}






    <div class="d-flex flex-column flex-column-fluid container-fluid ps-0-important">

        <div class="content flex-column-fluid" id="kt_content">

            <div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column me-3">
                    <!--begin::Title-->
                    <h1 class="d-flex text-dark fw-bold my-1 fs-3">
                        Your Chat Bot <strong>'{{ chat_bot.name }}'</strong> Leads
                    </h1>
                    <!--end::Title-->
                </div>
                <!--end::Page title-->
            </div>








                    <!-- [ Main Content ] start -->
                        <div class="row">
                            <!-- [ basic-table ] start -->
                            <div class="col-xl-12">
                                <div class="card">
                                    <div class="card-block table-border-style m-5">
                                        <button class="btn btn-primary csv">Export CSV</button>
                                        <button class="btn btn-primary sql">Export SQL</button>
                                        <button class="btn btn-primary txt">Export TXT</button>
                                        <button class="btn btn-primary json">Export JSON</button>
                                        <div class="table-responsive">
                                            <table class="table" id="pc-dt-export">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Email</th>
                                                        <th>Phone</th>
                                                        <th data-type="date" data-format="YYYY/DD/MM">Created Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for lead in leads %}
                                                    <tr>
                                                        <td>{% if lead.name %}{{ lead.name }}{% endif %}</td>
                                                        <td>{{ lead.email }}</td>
                                                        <td>{% if lead.phone %}{{ lead.phone }}{% endif %}</td>
                                                        <td>{{ lead.created_date|date:"Y/d/m" }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- [ basic-table ] end -->
                        </div>
                        <!-- [ Main Content ] end -->





        </div>
    </div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script src="/assets-auth/plugins/custom/datatables/datatables.bundle.js"></script>
<script src="/assets-auth/js/datatables2.js"></script>
<script>
    const table = new simpleDatatables.DataTable("#pc-dt-export");
    document.querySelector("button.csv").addEventListener("click", () => {
        table.export({
            type: "csv",
            filename: "chat_bot_leads.csv",
            download: true,
            lineDelimiter: "\n",
            columnDelimiter: ","
        })
    })
    document.querySelector("button.sql").addEventListener("click", () => {
        table.export({
            type: "sql",
            filename: "chat_bot_leads.sql",
            download: true,
            tableName: "chat_bot_leads"
        })
    })
    document.querySelector("button.txt").addEventListener("click", () => {
        table.export({
            type: "txt",
            filename: "chat_bot_leads.txt",
            download: true,
        })
    })
    document.querySelector("button.json").addEventListener("click", () => {
        table.export({
            type: "json",
            filename: "chat_bot_leads.json",
            download: true,
            escapeHTML: true,
            space: 4
        })
    })
</script>
{% endblock javascripts %}