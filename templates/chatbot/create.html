{% extends "layouts/base.html" %}

{% block title %} Create Your Chat Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}




    <div class="d-flex flex-column flex-column-fluid container-fluid ps-0-important">

        <div class="content flex-column-fluid" id="kt_content">

            <div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column me-3">
                    <!--begin::Title-->
                    <h1 class="d-flex text-dark fw-bold my-1 fs-3">Create a Chat Bot</h1>
                    <!--end::Title-->
                </div>
                <!--end::Page title-->
            </div>




            <form method="post">
                {% csrf_token %}
                {% if form.errors %}

                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-danger alert-dismissible fade show">
                                <div class="media align-items-center">
                                    <i class="feather icon-alert-circle h2 mb-0"></i>
                                    <div class="media-body ms-3 ml-2">
                                        Please fix the errors below.
                                    </div>
                                </div>
                                <button type="button" class="btn-close" data-BS-dismiss="alert" aria-label="Close"></button>
                            </div>
                        </div>
                    </div>

                {% endif %}

                <div class="row">

                            <!-- [ form-element ] start -->
                            <div class="col-sm-8 mb-10">
                                <!-- Basic Inputs -->
                                <div class="card card-flush">

                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">About Your Chat Bot</span>
                                            <span class="text-gray-400 mt-1 fw-semibold fs-6">Information to train the Chat Bot.</span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>

                                    {{ test }}
                                    <div class="card-body">
                                        <div class="form-group input-group-lg mb-5">
                                            <label class="form-label">Chat Bot Name:</label>
                                            <input type="text" name="name" class="form-control form-control-lg" placeholder="Chat Bot Name" required>
                                            <small>Name your chat bot, for your eyes only.</small>
                                            {% if form.name.errors %}
                                                <small class="text-danger">{{ form.name.errors }}</small>
                                            {% endif %}
                                        </div>
                                        <div class="form-group input-group-lg mb-5">
                                            <label class="form-label">Chat Default Prompt (optional):</label>
                                            <input type="text" name="description" class="form-control form-control-lg" placeholder="Chat Bot Default Prompt">
                                            <small>The first thing your bot says to the user.</small>
                                        </div>

                                        <div class="form-group input-group-lg mb-5">
                                            <label class="form-label">Site URL:</label>
                                            <input type="text" name="site_url" class="form-control form-control-lg" placeholder="Site URL" required>
                                            <small>The URL of the site this chat bot will appear on.</small>
                                        </div>


                                        <hr class="mb-10" />


                                        <div class="alert alert-primary">
                                            <div class="media align-items-center">
                                                <i class="feather icon-alert-circle h2 mb-0"></i>
                                                <div class="media-body ms-3 ml-2">
                                                    The next two inputs are important and will be used to train your AI chat bot.
                                                    Please be as descriptive as possible.  Every sentence you write here will teach the AI how to sell and convert your leads.
                                                    Please DO NOT include answers to question in these text boxes.  Those will be handled on the next screen.
                                                    General information only.
                                                </div>
                                            </div>
                                        </div>



                                        <div class="form-group mb-5">
                                            <label class="form-label" for="exampleTextarea">Information</label>
                                            <textarea class="form-control" name="entity_info" id="entity_info" rows="6" required></textarea>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label" for="exampleTextarea">Custom Persona (optional)</label>
                                            <textarea class="form-control" name="entity_description" id="entity_description" rows="6"></textarea>
                                        </div>






                                    </div>
                                    <div class="card-footer">
                                        <button class="btn btn-primary me-2" type="submit">Create Chat Bot & Continue to Q & A Section <i class="fas fa-arrow-alt-circle-right me-0 ms-2"></i></button>
                                        <!--
                                        <button type="reset" class="btn btn-light">Reset</button>
                                        -->
                                    </div>
                                </div>
                            </div>
                            <!-- [ form-element ] end -->


                            <div class="col-sm-4">
                                <!-- Basic Inputs -->
                                <div class="card card-flush">

                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Chat Bot Details</span>
                                            <span class="text-gray-400 mt-1 fw-semibold fs-6">How you want the Chat Bot to perform.</span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>

                                    <div class="card-body">


                                        <div class="alert alert-primary">
                                            <div class="media align-items-center">
                                                <i class="feather icon-alert-circle h2 mb-0"></i>
                                                <div class="media-body ms-3 ml-2">
                                                    Optional settings to further power your Chat Bot.
                                                </div>
                                            </div>
                                        </div>


                                        <div class="form-group">
                                            <label class="form-label">Chatbot Primary Objective:</label>
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <div class="border card p-3 mb-5">
                                                        <div class="form-check custom-radio">
                                                            <input type="radio" name="goal_type" value="2" class="form-check-input input-primary" id="goal_type_2" checked>
                                                            <label class="form-check-label d-block" for="goal_type_2">
                                                                <span>
                                                                    <span class="h5 d-block">Education</span>
                                                                    <span class="f-12 text-muted">
                                                                        To inform the client
                                                                        (&nbsp;This bot is primarily to answer user question and information them about us&nbsp;)
                                                                    </span>
                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12">
                                                    <div class="border card p-3 mb-5">
                                                        <div class="form-check custom-radio">
                                                            <input type="radio" name="goal_type" value="1" class="form-check-input input-primary" id="goal_type_1">
                                                            <label class="form-check-label d-block" for="goal_type_1">
                                                                <span>
                                                                    <span class="h5 d-block">Lead Generation</span>
                                                                    <span class="f-12 text-muted">
                                                                        To generate leads
                                                                        (&nbsp;This bot is primarily used to generate leads from website traffic&nbsp;)
                                                                    </span>
                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12">
                                                    <div class="border card p-3 mb-5">
                                                        <div class="form-check custom-radio">
                                                            <input type="radio" name="goal_type" value="0" class="form-check-input input-primary" id="goal_type_0"
                                                                {% if chat_bot.goal_type == 0 %}checked{% endif %}>
                                                            <label class="form-check-label d-block" for="goal_type_0">
                                                                <span>
                                                                    <span class="h5 d-block">Click Link</span>
                                                                    <span class="f-12 text-muted">
                                                                        Forward user to URL
                                                                        (&nbsp;This bot is primarily used to get the user to visit a specific URL, like a sign-up page&nbsp;)
                                                                    </span>

                                                                    <div class="form-group mt-3">
                                                                        <label class="form-label">Action URL:</label>
                                                                        <input type="text" name="action_url" class="form-control" placeholder="Action URL"
                                                                            value="{% if chat_bot.action_url %}{{ chat_bot.action_url }}{% endif %}">
                                                                        <small>The URL of the site you want the chat bot to send users to. For example a registration page.</small>
                                                                    </div>


                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-12">
                                                <div class="form-group">
                                                    <label class="form-label">Chatbot Primary Objective Message:</label>
                                                    <input type="text" name="goal_type_message" class="form-control" placeholder="Message"
                                                           value="{% if chat_bot.goal_type_message %}{{ chat_bot.goal_type_message }}{% endif %}">
                                                    <small>
                                                        The message the chat bot should send with the URL or when asking for the users email (depending on your choice above).
                                                        Write it as if you're the chat bot talking to your client. This is optional if 'Education' was chosen.
                                                    </small>
                                                </div>
                                            </div>
                                        </div>


                                        <hr />


                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Force Lead?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="force_lead" id="force_lead">
                                                <label for="force_lead" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should chatbot force the user to give their email before chatting? Not recommended as you will receive far fewer leads and users.</small>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Ask for name?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="ask_for_name" id="ask_for_name">
                                                <label for="ask_for_name" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should we also ask for the user's name in addition to their email?</small>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Name mandatory?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="force_name" id="force_name">
                                                <label for="force_name" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should we make the name mandatory?</small>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Ask for phone?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="ask_for_phone" id="ask_for_phone">
                                                <label for="ask_for_phone" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should we also ask for the user's phone in addition to their email?</small>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Phone mandatory?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="force_phone" id="force_phone">
                                                <label for="force_phone" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should we make the phone mandatory?</small>
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>
                        </form>
                        <!-- [ Main Content ] end -->








        </div>
    </div>





















{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}{% endblock javascripts %}
