{% extends "layouts/base.html" %}

{% block title %} Update Your Chat Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <!--
<link rel="stylesheet" href="/static/assets/plugins/animate.min.css">
<link rel="stylesheet" href="/static/assets/css/perfect-scrollbar.css">
-->
{% endblock stylesheets %}

{% block content %}










    <div class="d-flex flex-column flex-column-fluid container-fluid ps-0-important">

        <div class="content flex-column-fluid" id="kt_content">

            <div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column me-3">
                    <!--begin::Title-->
                    <h1 class="d-flex text-dark fw-bold my-1 fs-3">
                        Update Chat Bot - {{ chat_bot.name }}
                    </h1>
                    <!--end::Title-->
                </div>
                <!--end::Page title-->
            </div>








                        <!-- [ Main Content ] start -->
                        <div class="row">
                            <!-- [ form-element ] start -->
                            <div class="col-sm-4">



                                <form method="post" id="chat-form">
                                {% csrf_token %}
                                <div class="card chat-sanders">
                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Test Your Chat Bot</span>
                                            <span class="text-gray-400 mt-1 mb-5 fw-semibold fs-6">
                                            </span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>




                                    <div class="card-block m-t-30 p-0">
                                        <div class="scroll-y me-n5 pe-5 h-300px ms-5 mt-5" id="chat-scroll" data-kt-element="messages" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_header, #kt_app_header, #kt_app_toolbar, #kt_toolbar, #kt_footer, #kt_app_footer, #kt_chat_messenger_header, #kt_chat_messenger_footer" data-kt-scroll-wrappers="#kt_content, #kt_app_content, #kt_chat_messenger_body" data-kt-scroll-offset="5px">

                                            <!--begin::Message(in)-->
													<div class="d-flex justify-content-start mb-10">
														<!--begin::Wrapper-->
														<div class="d-flex flex-column align-items-start">
															<!--begin::User-->
															<div class="d-flex align-items-center mb-2">
																<!--begin::Details-->
																<div class="ms-3">
																	<a href="javascript:;" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">AI Bot</a>
																</div>
																<!--end::Details-->
															</div>
															<!--end::User-->
															<!--begin::Text-->
															<div class="p-5 rounded bg-light-info text-dark fw-semibold mw-lg-400px text-start" data-kt-element="message-text">
                                                                {% if chat_bot.description %}{{ chat_bot.description }}{% else %}How may I help you?{% endif %}
                                                            </div>
															<!--end::Text-->
														</div>
														<!--end::Wrapper-->
													</div>
													<!--end::Message(in)-->

                                        </div>
                                    </div>







                                    <div class="right-icon-control border-top">
                                        <div class="input-group input-group-button p-5">

                                            <input type="text" id="chat_form" class="form-control border-0 text-muted" placeholder="Write your message">
                                            <div class="input-group-append">
                                                <button class="btn pr-3" id="message-submit" type="button"{% if chat_bot.openai_should_tune %} xdisabled{% endif %}><i class="fas f-20 fa-paper-plane mr-0"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </form>






                                <div class="card mt-10">
                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Add A Chat Bot Q &amp; A</span>
                                            <span class="text-gray-400 mt-1 mb-5 fw-semibold fs-6">
                                            </span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>


                                    <div class="card-body">

                                        <form method="post">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="add">
                                            <div class="form-group mb-5">
                                                <label class="form-label" for="question">Question:</label>
                                                <input type="text" name="question" class="form-control" placeholder="Question" required="">
                                            </div>

                                            <div class="form-group mb-0">
                                                <label class="form-label" for="answer">Answer</label>
                                                <textarea class="form-control" id="answer" name="answer" rows="3" required=""></textarea>
                                            </div>

                                            <button class="btn btn-primary me-2 mt-4" type="submit">
                                                Add Q &amp; A <i class="fas fa-arrow-alt-circle-right me-0 ms-2"></i>
                                            </button>
                                        </form>

                                    </div>
                                </div>




                                <div class="card mt-10 mb-10">
                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Cat Bot Questions &amp; Answers</span>
                                            <span class="text-gray-400 mt-1 mb-5 fw-semibold fs-6">
                                            </span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>




                                    <div class="card-body">

                                        {% if questions %}
                                            {% for question in questions %}


                                                <div class="xoverflow-auto pb-5">
																<!--begin::Notice-->
																<div class="notice d-flex bg-light-primary rounded border-primary border border-dashed flex-shrink-0 p-6">

																	<!--begin::Wrapper-->
																	<div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
																		<!--begin::Content-->
																		<div class="mb-3 mb-md-0 fw-semibold">
																			<h4 class="text-gray-900 fw-bold">{{ question.question }}?</h4>
																			<div class="fs-6 text-gray-700 pe-7">{{ question.answer }}</div>
																		</div>
																		<!--end::Content-->
																		<!--begin::Action-->
																		<a href="#" class="btn btn-primary px-0 py-0 align-self-center text-nowrap"
                                                                           data-bs-toggle="modal" data-bs-target="#deleteModal" data-id="{{ question.pk }}">

                                                                            <i class="ki-duotone ki-cross fs-2x pe-0">
                                                                                <span class="path1"></span>
                                                                                <span class="path2"></span>
                                                                            </i>
                                                                        </a>
																		<!--end::Action-->
																	</div>
																	<!--end::Wrapper-->
																</div>
																<!--end::Notice-->
															</div>



                                                <!--
                                        <div class="card widget-content">
                                            <div class="card-block">
                                                <button type="button" class="btn-close btn-delete" data-bs-toggle="modal"
                                                        data-bs-target="#deleteModal" data-id="{{ question.pk }}"></button>
                                                <div class="row">
                                                    <div class="col-sm-12 m-b-20">
                                                        <div class="widget-lorem">
                                                            <div class="media align-items-center justify-content-center receive-bar">
                                                                <div class="me-3 photo-table">
                                                                    <h5 class="theme-bg text-white d-flex align-items-center justify-content-center h-30px w-30px">
                                                                        Q
                                                                    </h5>
                                                                </div>
                                                                <div class="media-body ml-2">
                                                                    <h4>{{ question.question }}</h4>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12 m-b-0">
                                                        <div class="widget-lorem">
                                                            <div class="media send-bar">
                                                                <div class="me-3 photo-table">
                                                                    <h5 class="text-white d-flex theme-bg2 align-items-center justify-content-center h-30px w-30px">
                                                                        A
                                                                    </h5>
                                                                </div>
                                                                <div class="media-body ml-2">
                                                                    <p>{{ question.answer }}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                                -->
                                            {% endfor %}
                                        {% else %}
                                                <h3>You don't have any questions & answers anymore!</h3>
                                        {% endif %}
                                    </div>
                                </div>










                            </div>





                            <!-- [ form-element ] start -->

                            <div class="col-sm-4">
                                <form method="post">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="update_about">
                                <div class="card">
                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">About Your Chat Bot</span>
                                            <span class="text-gray-400 mt-1 mb-5 fw-semibold fs-6">
                                            </span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>


                                    <div class="card-body">
                                        <div class="form-group mb-5">
                                            <label class="form-label">Chat Bot Name:</label>
                                            <input type="text" name="name" class="form-control" placeholder="Chat Bot Name"
                                                   value="{{ chat_bot.name }}" required>
                                            <small>Name your chat bot, for your eyes only.</small>
                                        </div>
                                        <div class="form-group mb-5">
                                            <label class="form-label">Chat Bot Default Prompt (optional):</label>
                                            <input type="text" name="description" class="form-control" placeholder="Chat Bot Default Prompt"
                                                   value="{% if chat_bot.description %}{{ chat_bot.description }}{% endif %}">
                                            <small>The first thing your bot says to the user.</small>
                                        </div>
                                        <div class="form-group mb-5">
                                            <label class="form-label">Site URL:</label>
                                            <input type="text" name="site_url" class="form-control" placeholder="Site URL"
                                                   value="{{ chat_bot.site_url }}"required>
                                            <small>The URL of the site this chatbot will appear on.</small>
                                        </div>
                                        <!--
                                        <div class="form-group">
                                            <label class="form-label">Action URL (optional):</label>
                                            <input type="text" name="action_url" class="form-control" placeholder="Action URL"
                                                value="{{ chat_bot.action_url }}">
                                            <small>The URL of the site you want the chatbot to send users to. For example a registration page.</small>
                                        </div>
                                        -->

                                        <hr class="mb-7" />

                                        <div class="alert alert-primary">
                                            <div class="media align-items-center">
                                                <i class="feather icon-alert-circle h2 mb-0"></i>
                                                <div class="media-body ms-3 ml-2">
                                                    Please be as descriptive as possible.  Every sentence you write here will teach the AI how to sell and convert your leads.
                                                    Please DO NOT include answers to question in these text boxes.
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label" for="exampleTextarea">Information</label>
                                            <textarea class="form-control" name="entity_info" id="entity_info" rows="10" required>{{ chat_bot.entity_info }}</textarea>
                                        </div>
                                        <div class="form-group mb-0 mt-2">
                                            <label class="form-label" for="exampleTextarea">Custom Persona (optional)</label>
                                            <textarea class="form-control" name="entity_description" id="entity_description" rows="10">{% if chat_bot.entity_description %}{{ chat_bot.entity_description }}{% endif %}</textarea>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <button type="submit" class="btn btn-primary me-2"{% if chat_bot.openai_should_tune %} xdisabled{% endif %}>
                                            Update Chatbot <i class="fas fa-sync-alt me-0 ms-2"></i>
                                        </button>
                                    </div>
                                </div>
                                </form>


                            </div>
                            <!-- [ form-element ] end -->


                            <div class="col-sm-4">

                                <form method="post">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="update_detail">
                                <div class="card">
                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Chat Bot Details</span>
                                            <span class="text-gray-400 mt-1 mb-5 fw-semibold fs-6">
                                            </span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>


                                    <div class="card-body">

                                        <div class="alert alert-primary">
                                            <div class="media align-items-center">
                                                <i class="feather icon-alert-circle h2 mb-0"></i>
                                                <div class="media-body ms-3 ml-2">
                                                    Optional settings to further power your chat bot.
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Chatbot Primary Objective:</label>
                                            <div class="row">
                                                <div class="col-lg-12 mb-5">
                                                    <div class="border card p-3">
                                                        <div class="form-check custom-radio">
                                                            <input type="radio" name="goal_type" value="2" class="form-check-input input-primary" id="goal_type_2"
                                                                   {% if chat_bot.goal_type == 2 %}checked{% endif %}>
                                                            <label class="form-check-label d-block" for="goal_type_2">
                                                                <span>
                                                                    <span class="h5 d-block">Education</span>
                                                                    <span class="f-12 text-muted">
                                                                        To inform the client
                                                                        (&nbsp;This bot is primarily to answer user question and information them about us&nbsp;)
                                                                    </span>
                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12 mb-5">
                                                    <div class="border card p-3">
                                                        <div class="form-check custom-radio">
                                                            <input type="radio" name="goal_type" value="1" class="form-check-input input-primary" id="goal_type_1"
                                                                {% if chat_bot.goal_type == 1 %}checked{% endif %}>
                                                            <label class="form-check-label d-block" for="goal_type_1">
                                                                <span>
                                                                    <span class="h5 d-block">Lead Generation</span>
                                                                    <span class="f-12 text-muted">
                                                                        To generate leads
                                                                        (&nbsp;This bot is primarily used to generate leads from website traffic&nbsp;)
                                                                    </span>
                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12 mb-5">
                                                    <div class="border card p-3">
                                                        <div class="form-check custom-radio">
                                                            <input type="radio" name="goal_type" value="0" class="form-check-input input-primary" id="goal_type_0"
                                                                {% if chat_bot.goal_type == 0 %}checked{% endif %}>
                                                            <label class="form-check-label d-block" for="goal_type_0">
                                                                <span>
                                                                    <span class="h5 d-block">Click Link</span>
                                                                    <span class="f-12 text-muted">
                                                                        Forward user to URL
                                                                        (&nbsp;This bot is primarily used to get the user to visit a specific URL, like a sign-up page&nbsp;)
                                                                    </span>

                                                                    <div class="form-group mt-3">
                                                                        <label class="form-label">Action URL:</label>
                                                                        <input type="text" name="action_url" class="form-control" placeholder="Action URL"
                                                                            value="{% if chat_bot.action_url %}{{ chat_bot.action_url }}{% endif %}">
                                                                        <small>The URL of the site you want the chat bot to send users to. For example a registration page.</small>
                                                                    </div>


                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12 mb-5">
                                                    <div class="form-group">
                                                        <label class="form-label">Chatbot Primary Objective Message:</label>
                                                        <input type="text" name="goal_type_message" class="form-control" placeholder="Message"
                                                               value="{% if chat_bot.goal_type_message %}{{ chat_bot.goal_type_message }}{% endif %}">
                                                        <small>
                                                            The message the chat bot should send with the URL or when asking for the users email (depending on your choice above).
                                                            Write it as if you're the chat bot talking to your client. This is optional if 'Education' was chosen.
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <hr />

                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Force Lead?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="force_lead" id="force_lead" {% if chat_bot.force_lead %}checked{% endif %}>
                                                <label for="force_lead" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should chatbot force the user to give their email before chatting? Not recommended.</small>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Ask for name?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="ask_for_name" id="ask_for_name" {% if chat_bot.ask_for_name %}checked{% endif %}>
                                                <label for="ask_for_name" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should we also ask for the user's name in addition to their email?</small>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Name mandatory?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="force_name" id="force_name" {% if chat_bot.force_name %}checked{% endif %}>
                                                <label for="force_name" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should we make the name mandatory?</small>
                                        </div>

                                        <div class="form-group mb-5">
                                            <label class="form-label mr-4 d-block">Ask for phone?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="ask_for_phone" id="ask_for_phone" {% if chat_bot.ask_for_phone %}checked{% endif %}>
                                                <label for="ask_for_phone" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should we also ask for the user's phone in addition to their email?</small>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label mr-4 d-block">Phone mandatory?</label>
                                            <div class="switch switch-primary d-inline m-r-10">
                                                <input type="checkbox" name="force_phone" id="force_phone" {% if chat_bot.force_phone %}checked{% endif %}>
                                                <label for="force_phone" class="cr"></label>
                                            </div>
                                            <small class="display-block">Should we make the phone mandatory?</small>
                                        </div>

                                    </div>
                                    <div class="card-footer">
                                        <button type="submit" class="btn btn-primary me-2"{% if chat_bot.openai_should_tune %} xdisabled{% endif %}>
                                            Update Chatbot <i class="fas fa-sync-alt mr-0 ml-2"></i>
                                        </button>
                                    </div>
                                </div>
                                </form>
                            </div>

                        </div>
                        <!-- [ Main Content ] end -->







            </div>
        </div>










    <div id="launchModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Re-launch Chat Bot?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Are you ready to re-launch and test your chat bot?
                        You only need to re-launch if you've added new Q&As.
                        Don't worry, you can make changes anytime.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <a href="/chat/launch_bot/{{ chat_bot.pk }}/" class="btn btn-success me-2">
                        Yes, Re-launch My Chat Bot <i class="fas fa-rocket mr-0 ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>


    <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Delete This Q & A?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure that you want to delete this single Q & A?  This cannot be un-done, however you can always re-add it.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="id" id="delete-id" value="">
                        <input type="hidden" name="action" value="delete">
                        <button type="submit" class="btn btn-danger me-2">
                            Yes, Delete This Q & A <i class="fas fa-times-circle mr-0 ml-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>












{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}

<script>
    /*
    var ps = new PerfectScrollbar('#chat-scroll', {
        wheelSpeed: .5,
        swipeEasing: 0,
        suppressScrollX: !0,
        wheelPropagation: 1,
        minScrollbarLength: 40,
    });

     */


    $('#deleteModal').on('show.bs.modal', function(e) {
        console.log('BOOM');
      var id = e.relatedTarget.dataset.id;
        console.log(id);
      $('#delete-id').val(id);
    });



    let messages = $('#chat-scroll'),
    d, h, m,
    i = 0;


    $(function() {
        $("#chat-form").submit(function(e){
            e.preventDefault();
        });

        $('#message-submit').click(function() {
            console.log('Submit...');
            insertMessage();
        });

        $(window).on('keydown', function(e) {
            console.log('Key...');
            if (e.which == 13) {
                console.log('down...');
                insertMessage();
                return false;
            }
        });
    });


    function updateScrollbar() {
        $("#chat-scroll").animate({ scrollTop: $('#chat-scroll').prop("scrollHeight")}, 10);
        /*
        if ($(window).width() > 992) {
            messages.mCustomScrollbar("update").mCustomScrollbar('scrollTo', 'bottom', {
                scrollInertia: 10,
                timeout: 0
            });
        } else {
            $("#chat-scroll").animate({ scrollTop: $('#chat-scroll').prop("scrollHeight")}, 10);
        }
         */
    }

    function setDate(){
        d = new Date()
        if (m != d.getMinutes()) {
            m = d.getMinutes();
            $('<div class="timestamp">' + d.getHours() + ':' + m + '</div>').appendTo($('.message:last'));
            $('<div class="checkmark-sent-delivered">&check;</div>').appendTo($('.message:last'));
            $('<div class="checkmark-read">&check;</div>').appendTo($('.message:last'));
        }
    }







    function insertUserMessage(msg) {
        $('#chat-scroll').append('<div class="d-flex justify-content-end mb-10"><div class="d-flex flex-column align-items-end"><div class="d-flex align-items-center mb-2"><div class="me-3"><a href="javascript:;" class="fs-5 fw-bold text-gray-900 text-hover-primary ms-1">You</a></div></div><div class="p-5 rounded bg-light-primary text-dark fw-semibold mw-lg-400px text-end" data-kt-element="message-text">' + msg + '</div></div></div>');
        // setDate();
        updateScrollbar();
    }

    function startAdvocateMessage() {
        $('<div class="d-flex justify-content-start mb-10" id="chats-loading"><div class="d-flex flex-column align-items-start"><div class="d-flex align-items-center mb-2"><div class="ms-3"><a href="javascript:;" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">AI Bot</a></div></div><div class="p-5 rounded bg-light-info text-dark fw-semibold mw-lg-400px text-start" data-kt-element="message-text"><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div></div></div>').appendTo($('#chat-scroll'));
        // $('<div class="row m-b-20 received-chat align-items-end" id="chats-loading"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center  theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0"><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div></h6></div></div></div>').appendTo($('#chat-scroll'));

        updateScrollbar();
    }

    function insertAdminMessage(msg) {
        $('#chats-loading').remove();
        $('#chat-scroll').append('<div class="d-flex justify-content-start mb-10"><div class="d-flex flex-column align-items-start"><div class="d-flex align-items-center mb-2"><div class="ms-3"><a href="javascript:;" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">System</a></div></div><div class="p-5 rounded bg-light-info text-dark fw-semibold mw-lg-400px text-start" data-kt-element="message-text">' + msg + '</div></div></div>');
        updateScrollbar();
    }










    function insertAdvocateMessage(msg, pause, msg2) {
        if (pause) {
            setTimeout(function () {
                $('#chats-loading').remove();
                $('#chat-scroll').append('<div class="d-flex justify-content-start mb-10"><div class="d-flex flex-column align-items-start"><div class="d-flex align-items-center mb-2"><div class="ms-3"><a href="javascript:;" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">AI Bot</a></div></div><div class="p-5 rounded bg-light-info text-dark fw-semibold mw-lg-400px text-start" data-kt-element="message-text">' + msg + '</div></div></div>');

                if (msg2 != 'null') {
                    $('#chat-scroll').append('<div class="d-flex justify-content-start mb-10"><div class="d-flex flex-column align-items-start"><div class="d-flex align-items-center mb-2"><div class="ms-3"><a href="javascript:;" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">AI Bot</a></div></div><div class="p-5 rounded bg-light-info text-dark fw-semibold mw-lg-400px text-start" data-kt-element="message-text">' + msg2 + '</div></div></div>');
                }

                updateScrollbar();
            }, 1000 + (Math.random() * 20) * 100);
        }
        else
        {
            $('#chats-loading').remove();
            $('#chat-scroll').append('<div class="d-flex justify-content-start mb-10"><div class="d-flex flex-column align-items-start"><div class="d-flex align-items-center mb-2"><div class="ms-3"><a href="javascript:;" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">AI Bot</a></div></div><div class="p-5 rounded bg-light-info text-dark fw-semibold mw-lg-400px text-start" data-kt-element="message-text">' + msg + '</div></div></div>');
            updateScrollbar();
        }
        i++;
    }


    function insertMessage() {
        msg = $('#chat_form').val();
        if ($.trim(msg) == '') {
            return false;
        }

        var formData = {message:msg, chat_bot_id:'{{ chat_bot.pk }}'};
        insertUserMessage(msg);
        $('#chat_form').val(null);
        startAdvocateMessage();

        $.ajax({
            url : "{% url 'chat_bot_get_api' %}",
            type: "POST",
            data : formData,
            success: function(data, textStatus, jqXHR)
            {
                console.log(data)
                insertAdvocateMessage(data.message, true, data.message2);
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                insertAdminMessage('There was a problem sending your message to your advocate. Please try again in a moment.');
            },
            complete: function(data)
            {
            }
        });
    }

















</script>
{% endblock javascripts %}
