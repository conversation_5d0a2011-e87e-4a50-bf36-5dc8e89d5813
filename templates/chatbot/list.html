{% extends "layouts/base.html" %}

{% block title %} Update Your Chat Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}





    <div class="d-flex flex-column flex-column-fluid container-fluid ps-0-important">

        <div class="content flex-column-fluid" id="kt_content">

            <div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column me-3">
                    <!--begin::Title-->
                    <h1 class="d-flex text-dark fw-bold my-1 fs-3">Your Chat Bots</h1>
                    <!--end::Title-->
                </div>
                <!--end::Page title-->
                <!--begin::Actions-->
                <div class="d-flex align-items-center py-2 py-md-1">
                    <!--begin::Button-->
                    <a href="{% url 'chat_bot_create_chat_bot' %}" class="btn btn-dark fw-bold">
                        <i class="ki-duotone ki-messages fs-2">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                            <span class="path4"></span>
                            <span class="path5"></span>
                        </i>
                        Create New Chat Bot
                    </a>
                    <!--end::Button-->
                </div>
                <!--end::Actions-->
            </div>




            <div class="content flex-column-fluid" id="kt_content">
                <div class="row g-5 g-xl-10 mb-xl-10">


                    {% if chat_bot_data %}
                    {% for chat_bot in chat_bot_data %}
                    <!--begin::Col-->
                    <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-6 mb-md-5 mb-xl-10">
                        <div class="card">
                            <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">{{ chat_bot.chat_bot_name }} - Chat Bot</span>
                                            <span class="text-gray-400 mt-1 mb-5 fw-semibold fs-6">
                                            </span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>
                               <!--
                                        <div class="card-header">
                                            <h5>{{ chat_bot.chat_bot_name }} - Chat Bot</h5>
                                            <div class="card-header-right">
                                                <div class="btn-group card-option">
                                                    <button type="button" class="btn dropdown-toggle"
                                                            data-bs-toggle="dropdown" aria-haspopup="true"
                                                            aria-expanded="false">
                                                        <i class="feather icon-more-horizontal"></i>
                                                    </button>
                                                    <ul class="list-unstyled card-option dropdown-menu dropdown-menu-end">
                                                        <li class="dropdown-item full-card">
                                                            <a href="#!">
                                                                <span>
                                                                    <i class="feather icon-maximize"></i>
                                                                    maximize
                                                                </span>
                                                                <span style="display:none">
                                                                    <i class="feather icon-minimize"></i>
                                                                    Restore
                                                                </span>
                                                            </a>
                                                        </li>
                                                        <li class="dropdown-item xfull-card">
                                                            <a href="/chat/update_bot/{{ chat_bot.chat_bot_id }}/">
                                                                <i class="feather icon-edit"></i>
                                                                Edit Bot
                                                            </a>
                                                        </li>
                                                        <li class="dropdown-item xreload-card">
                                                            <a href="/chat/view_chats/{{ chat_bot.chat_bot_id }}/0/">
                                                                <i class="fas fa-comments"></i>
                                                                View Chats
                                                            </a>
                                                        </li>
                                                        <li class="dropdown-item xclose-card">
                                                            <a href="#!" data-bs-toggle="modal" data-bs-target="#deleteModal"
                                                                data-id="{{ chat_bot.chat_bot_id }}">
                                                                <i class="feather icon-x-circle"></i>
                                                                Delete Bot
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                            -->
                            <div class="card-block mx-5">
                                            <div class="row mb-1">
                                                <div class="col-4 text-center">
                                                    <div class="d-grid">
                                                        <a href="{% url 'chat_bot_update' chat_bot.chat_bot_id %}"
                                                           class="btn btn-primary text-uppercase w-100">
                                                            Edit
                                                           <i class="ki-duotone ki-message-edit fs-2">
                                                               <span class="path1"></span>
                                                               <span class="path2"></span>
                                                           </i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <div class="d-grid">
                                                        <a href="{% url 'chat_bot_view_chats' chat_bot.chat_bot_id 0 %}"
                                                           class="btn text-uppercase w-100 border btn-outline-secondary">
                                                            Chats
                                                           <i class="ki-duotone ki-messages fs-2">
                                                               <span class="path1"></span>
                                                               <span class="path2"></span>
                                                               <span class="path3"></span>
                                                               <span class="path4"></span>
                                                               <span class="path5"></span>
                                                           </i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <div class="d-grid">
                                                        <a href="{% url 'chat_bot_view_leads' chat_bot.chat_bot_id %}"
                                                           class="btn text-uppercase w-100 border btn-success">
                                                            Leads
                                                           <i class="ki-duotone ki-people fs-2">
                                                               <span class="path1"></span>
                                                               <span class="path2"></span>
                                                               <span class="path3"></span>
                                                               <span class="path4"></span>
                                                               <span class="path5"></span>
                                                               <span class="path6"></span>
                                                               <span class="path7"></span>
                                                           </i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>





                                            <div class="row m-b-30">
                                                <div class="col-4 text-center">
                                                    <div class="d-grid">
                                                        <a href="{% url 'chat_bot_update' chat_bot.chat_bot_id %}"
                                                           class="btn btn-dark text-uppercase w-100">
                                                            Test
                                                            <i class="ki-duotone ki-artificial-intelligence fs-2">
                                                               <span class="path1"></span>
                                                               <span class="path2"></span>
                                                               <span class="path3"></span>
                                                               <span class="path4"></span>
                                                               <span class="path5"></span>
                                                               <span class="path6"></span>
                                                               <span class="path7"></span>
                                                               <span class="path8"></span>
                                                           </i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <div class="d-grid">
                                                        <a href="#!" class="btn text-uppercase w-100 border btn-warning"
                                                           data-bs-toggle="modal" data-bs-target="#codeModal"
                                                           data-id="{{ chat_bot.chat_bot_id }}">
                                                           Code
                                                           <i class="ki-duotone ki-code fs-2">
                                                               <span class="path1"></span>
                                                               <span class="path2"></span>
                                                               <span class="path3"></span>
                                                               <span class="path4"></span>
                                                           </i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <div class="d-grid">
                                                        <a href="#!" class="btn btn-danger text-uppercase w-100"
                                                           data-bs-toggle="modal" data-bs-target="#deleteModal"
                                                           data-id="{{ chat_bot.chat_bot_id }}">
                                                            Delete
                                                            <i class="ki-duotone ki-cross fs-2">
                                                               <span class="path1"></span>
                                                               <span class="path2"></span>
                                                            </i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>



                                            <hr />
                                            <div class="row pb-3">
                                                <div class="col-md-6 col-6 text-center m-b-15">
                                                    <h3 class="f-w-300">Total Chats</h3>
                                                    <span>{{ chat_bot.week_chat_count }}</span>
                                                </div>
                                                <div class="col-md-6 col-6 text-center m-b-15">
                                                    <h3 class="f-w-300">Total Actions</h3>
                                                    <span>{{ chat_bot.week_action_count }}</span>
                                                </div>
                                            </div>
                                            <div id="bar-chart{{ chat_bot.chat_bot_id }}"
                                                 class="bar-chart{{ chat_bot.chat_bot_id }}"></div>
                                        </div>
                                    </div>
                                </div>
                    {% endfor %}
                    {% else %}
                    <div class="col-xl-6 mb-xl-10">
                        <!--begin::Engage widget 1-->
                        <div class="card h-md-100" dir="ltr">
                            <!--begin::Body-->
                            <div class="card-body d-flex flex-column flex-center">
                                <!--begin::Heading-->
                                <div class="mb-2">
                                    <!--begin::Title-->
                                    <h1 class="fw-semibold text-gray-800 text-center lh-lg">Chat Bot
                                        <br>
                                        <span class="fw-bolder">Create your first chat bot</span></h1>
                                    <!--end::Title-->
                                    <!--begin::Illustration-->
                                    <div class="py-10 text-center">
                                        <img src="/assets-auth/media/svg/illustrations/easy/2.svg" class="theme-light-show w-200px" alt="">
                                        <img src="/assets-auth/media/svg/illustrations/easy/2-dark.svg" class="theme-dark-show w-200px" alt="">
                                    </div>
                                    <!--end::Illustration-->
                                </div>
                                <!--end::Heading-->
                                <!--begin::Links-->
                                <div class="text-center mb-1">
                                    <!--begin::Link-->
                                    <a class="btn btn-sm btn-primary me-2" href="{% url 'chat_bot_create_chat_bot' %}">Create Chat Bot</a>
                                    <!--end::Link-->
                                </div>
                                <!--end::Links-->
                            </div>
                            <!--end::Body-->
                        </div>
                        <!--end::Engage widget 1-->
                    </div>
                    {% endif %}
                </div>
            </div>









        </div>
    </div>










    <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Delete This Chat Bot?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure that you want to delete this single Chat Bot?  This cannot be un-done.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="id" id="delete-id" value="">
                        <input type="hidden" name="action" value="delete">
                        <button type="submit" class="btn btn-danger me-2">
                            Yes, Delete This Chat Bot <i class="fas fa-times-circle mr-0 ml-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>






    <div id="codeModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Your Chat Bot Code
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Please copy and paste the following code on every page where you would like your chat bot to appear.
                        Place the code just above the &lt;/body&gt; tag.
                        If unsure, please ask your tech for assistance.
                    </p>

                    <span class="p-10 hei-75 wid-75 bg-body d-inline-block me-2 border border-secondary rounded" data-bs-toggle="tooltip" title="" data-bs-original-title="rounded" aria-label="rounded">



                        <small class="text-dark">
                        &lt;!--AIROS Chat Code --&gt;<br />
                        &lt;script&gt;<br />
                        const saicChatID = '<span id="modal-id-number"></span>';<br />
                        &lt;/script&gt;<br />
                        &lt;script src="http://airos.intelliclabs.com:8000/static/assets/js/airos-chat-plugin.js"&gt;&lt;/script&gt;<br />
                        &lt;!-- End AIROS Chat Code --&gt;<br />
                        </small>

                    </span>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>













{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script src="/assets-auth/plugins/apexcharts.min.js"></script>

<script>
$('#codeModal').on('show.bs.modal', function(e) {
    var id = e.relatedTarget.dataset.id;
    $('#modal-id-number').text(id);
});
$('#deleteModal').on('show.bs.modal', function(e) {
    var id = e.relatedTarget.dataset.id;
    $('#delete-id').val(id);
});
document.addEventListener("DOMContentLoaded", function () {
    setTimeout(function () {
        floatchart()
    }, 700);
});

function floatchart() {
    (function () {
        {% for chat_bot in chat_bot_data %}
        var options{{ chat_bot.chat_bot_id }} = {
            chart: {
                type: 'bar',
                height: 255,
                toolbar: {
                    show: false,
                }
            },
            series: [{
                name: '# of Chats',
                data: {{ chat_bot.past_week_chat_count|safe }}
            }, {
                name: '# of Actions',
                data: {{ chat_bot.past_week_action_count|safe }}
            }],
            colors: ['#1de9b6', '#a389d4'],
            fill: {
                type: 'gradient',
                opacity: 1,
                gradient: {
                    shade: 'dark',
                    type: 'vertical',
                    gradientToColors: ['#1dc4e9', '#899ed4'],
                    stops: [0, 100]
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '45%',
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: {{ chat_bot.past_week_dates|safe }},
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + " total"
                    }
                }
            }
        };
        var chart{{ chat_bot.chat_bot_id }} = new ApexCharts(document.querySelector("#bar-chart{{ chat_bot.chat_bot_id }}"), options{{ chat_bot.chat_bot_id }});
        chart{{ chat_bot.chat_bot_id }}.render();
        {% endfor %}
    })();
}
</script>
{% endblock javascripts %}