









<script>
    
    const response = await fetch(url, {
        method: 'POST',
        body: formData
    });
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';
    let isBold = false;
    
    while (true) {
        const { value, done } = await reader.read();
        if (done) break;
    
        let text = decoder.decode(value);
    
        let startIndex = text.startsWith('**');
        let endIndex = text.endsWith('**');
        
        if (startIndex || endIndex) {
            if (startIndex) {
                isBold = true;
                text = text.replace('**', '<b>');
            }
            if (endIndex) {
                isBold = false;
                text = text.replace('**', '</b>');
            }  
        }
    
        fullResponse += text;
    
        // Append to div
        document.getElementById('your_div_id').innerHTML += text;  // Replace 'your_div_id' with the actual id of your div
    
        if (done) {
            // Append closing tag if text ends while bold is active
            if (isBold) {
                document.getElementById('your_div_id').innerHTML += '</b>';
            }
            break;
        }
    }
    
</script>



















