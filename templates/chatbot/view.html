{% extends "layouts/base.html" %}

{% block title %} View Chat Bot Chats {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}








    <div class="d-flex flex-column flex-column-fluid container-fluid ps-0-important">

        <div class="content flex-column-fluid" id="kt_content">

            <div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column me-3">
                    <!--begin::Title-->
                    <h1 class="d-flex text-dark fw-bold my-1 fs-3">
                        View Chat Bot Chats
                    </h1>
                    <!--end::Title-->
                </div>
                <!--end::Page title-->
            </div>






            <div class="row">
                <div class="col-12">
                    <div class="alert alert-success fade show">
                        <div class="media align-items-center">
                            <i class="feather icon-alert-circle h2 mb-0"></i>
                            <div class="media-body ms-3 ml-2">
                                Here you can view the conversations your AI Chat bot is having with
                                your customers.  Use this tool to better fine tune your bot and add
                                any information your customers are looking for.
                            </div>
                        </div>
                    </div>
                </div>
            </div>














							<!--begin::Post-->
							<div class="content flex-column-fluid" id="kt_content">
								<!--begin::Layout-->
								<div class="d-flex flex-column flex-lg-row">
									<!--begin::Sidebar-->
									<div class="flex-column flex-lg-row-auto w-100 w-lg-300px w-xl-400px mb-10 mb-lg-0">
										<!--begin::Contacts-->
										<div class="card card-flush">
											<!--begin::Card header-->
                                            <!--
											<div class="card-header pt-7" id="kt_chat_contacts_header">
												<form class="w-100 position-relative" autocomplete="off">
													<i class="ki-duotone ki-magnifier fs-3 text-gray-500 position-absolute top-50 ms-5 translate-middle-y">
														<span class="path1"></span>
														<span class="path2"></span>
													</i>
													<input type="text" class="form-control form-control-solid px-13" name="search" value="" placeholder="Search by username or email..." />
												</form>
											</div>
											-->
											<!--end::Card header-->
											<!--begin::Card body-->
											<div class="card-body pt-5" id="kt_chat_contacts_body">
												<!--begin::List-->
												<div class="scroll-y me-n5 pe-5 h-200px h-lg-auto" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_header, #kt_app_header, #kt_toolbar, #kt_app_toolbar, #kt_footer, #kt_app_footer, #kt_chat_contacts_header" data-kt-scroll-wrappers="#kt_content, #kt_app_content, #kt_chat_contacts_body" data-kt-scroll-offset="5px">



                                                    {% for chat in chat_data %}
                                                    <!--begin::User-->
													<div class="d-flex flex-stack userlist-box py-4{% if chat.is_selected %} active-chat{% endif %}" data-username="{{ chat.avatar_name }}" data-id="{{ chat.chat_pk }}" data-status="{% if chat.is_selected %}online{% else %}offline{% endif %}" data-username="{{ chat.avatar_name }}">
														<!--begin::Details-->
														<div class="d-flex align-items-center">
															<!--begin::Avatar-->
															<div class="symbol symbol-45px symbol-circle">
                                                                <a href="{% url 'chat_bot_view_chats' chat_id chat.chat_pk %}" class="symbol symbol-45px symbol-circle fs-5 fw-bold text-gray-900 text-hover-primary mb-2">
																    <img alt="Pic" src="/get_image/{{ chat.avatar_image }}" alt="{{ chat.avatar_name }} image">
                                                                </a>
															</div>
															<!--end::Avatar-->
															<!--begin::Details-->
															<div class="ms-5">
																<a href="{% url 'chat_bot_view_chats' chat_id chat.chat_pk %}" class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">
                                                                    {{ chat.avatar_name }}
                                                                </a>
																<div class="fw-semibold text-muted">
                                                                    <a href="{% url 'chat_bot_view_chats' chat_id chat.chat_pk %}" class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">
                                                                        {{ chat.created_date|date:'M d, Y - g:i A' }}
                                                                    </a>
                                                                </div>
															</div>
															<!--end::Details-->
														</div>
														<!--end::Details-->
														<!--begin::Lat seen-->
														<div class="d-flex flex-column align-items-end ms-2">
															<span class="text-muted fs-7 mb-1"></span>
															<span class="badge badge-sm badge-circle badge-light-danger">
                                                                {{ chat.message_count }}
                                                            </span>
														</div>
														<!--end::Lat seen-->
                                                    </div>
													<!--end::User-->
													<!--begin::Separator-->
													<div class="separator separator-dashed d-none"></div>
													<!--end::Separator-->
                                                    {% endfor %}


















												</div>
												<!--end::List-->
											</div>
											<!--end::Card body-->
										</div>
										<!--end::Contacts-->
									</div>
									<!--end::Sidebar-->
									<!--begin::Content-->
									<div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
										<!--begin::Messenger-->
										<div class="card h-100" id="kt_chat_messenger">
											<!--begin::Card body-->
											<div class="card-body" id="kt_chat_messenger_body">
												<!--begin::Messages-->
												<div class="scroll-y me-n5 pe-5 h-300px h-lg-auto" data-kt-element="messages" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_header, #kt_app_header, #kt_app_toolbar, #kt_toolbar, #kt_footer, #kt_app_footer, #kt_chat_messenger_header, #kt_chat_messenger_footer" data-kt-scroll-wrappers="#kt_content, #kt_app_content, #kt_chat_messenger_body" data-kt-scroll-offset="5px">




                                                {% for main_message in main_messages %}
                                                    {% if main_message.is_ai_message %}

                                                    <!--begin::Message(in)-->
													<div class="d-flex justify-content-start mb-10">
														<!--begin::Wrapper-->
														<div class="d-flex flex-column align-items-start">
															<!--begin::User-->
															<div class="d-flex align-items-center mb-2">
																<!--begin::Avatar-->
																<div class="symbol symbol-35px symbol-circle">
																	<img src="/get_image/{{ main_avatar_image }}" alt="{{ main_avatar_name }} image" />
																</div>
																<!--end::Avatar-->
																<!--begin::Details-->
																<div class="ms-3">
																	<a href="#" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">
                                                                        AI Bot
                                                                    </a>
																	<span class="text-muted fs-7 mb-1">
                                                                        {{ main_message.created_date|date:'M d, Y - g:i A' }}
                                                                    </span>
																</div>
																<!--end::Details-->
															</div>
															<!--end::User-->
															<!--begin::Text-->
															<div class="p-5 rounded bg-light-info text-dark fw-semibold mw-lg-400px text-start" data-kt-element="message-text">
                                                                {{ main_message.message }}
                                                            </div>
															<!--end::Text-->
														</div>
														<!--end::Wrapper-->
													</div>
													<!--end::Message(in)-->

                                                    {% else %}

                                                    <!--begin::Message(out)-->
													<div class="d-flex justify-content-end mb-10">
														<!--begin::Wrapper-->
														<div class="d-flex flex-column align-items-end">
															<!--begin::User-->
															<div class="d-flex align-items-center mb-2">
																<!--begin::Details-->
																<div class="me-3">
																	<span class="text-muted fs-7 mb-1">{{ main_message.created_date|date:'M d, Y - g:i A' }}</span>
																	<a href="#" class="fs-5 fw-bold text-gray-900 text-hover-primary ms-1">Customer</a>
																</div>
																<!--end::Details-->
															</div>
															<!--end::User-->
															<!--begin::Text-->
															<div class="p-5 rounded bg-light-primary text-dark fw-semibold mw-lg-400px text-end" data-kt-element="message-text">
                                                                {{ main_message.message }}
                                                            </div>
															<!--end::Text-->
														</div>
														<!--end::Wrapper-->
													</div>
													<!--end::Message(out)-->

                                                    {% endif %}
                                                {% endfor %}























												</div>
												<!--end::Messages-->
											</div>
											<!--end::Card body-->




										</div>
										<!--end::Messenger-->
									</div>
									<!--end::Content-->
								</div>
								<!--end::Layout-->

							</div>
							<!--end::Post-->


























        </div>
    </div>











{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<link rel="stylesheet" href="/assets-auth/css/perfect-scrollbar.css">
<script src="/assets-auth/js/perfect-scrollbar.min.js"></script>

<script type="text/javascript">
window.addEventListener('load', function () {
    var ps = new PerfectScrollbar('.msg-user-list.scroll-div', {
        wheelSpeed: .5,
        swipeEasing: 0,
        suppressScrollX: !0,
        wheelPropagation: 1,
        minScrollbarLength: 40,
    });
    var ps = new PerfectScrollbar('.msg-user-chat.scroll-div', {
        wheelSpeed: .5,
        swipeEasing: 0,
        suppressScrollX: !0,
        wheelPropagation: 1,
        minScrollbarLength: 40,
    });
    document.querySelector(".task-right-header-status").addEventListener("click", function (e) {
        slideToggle(document.querySelector(".taskboard-right-progress"), 200);
    });
    var tc = document.querySelectorAll(".message-mobile .media");
    for (var t = 0; t < tc.length; t++) {
        var c = tc[t];
        c.addEventListener("click", function (e) {
            console.log('Clicked...');
            var vw = window.innerWidth;
            if (vw < 992) {
                slideUp(document.querySelector(".taskboard-right-progress"), 200);
                document.querySelector(".msg-block").classList.add('dis-chat');
            }
        });
    }
});
</script>

{% endblock javascripts %}
