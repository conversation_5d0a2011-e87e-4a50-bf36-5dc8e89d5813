{% extends "layouts/base.html" %}

{% block title %} Chat <PERSON> Questions {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}






    <div class="d-flex flex-column flex-column-fluid container-fluid ps-0-important">

        <div class="content flex-column-fluid" id="kt_content">

            <div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column me-3">
                    <!--begin::Title-->
                    <h1 class="d-flex text-dark fw-bold my-1 fs-3">Chat Bot Questions & Answers</h1>
                    <!--end::Title-->
                </div>
                <!--end::Page title-->
            </div>








                        <!--
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-primary">
                                    <div class="media align-items-center">
                                        <i class="feather icon-alert-circle h2 mb-0"></i>
                                        <div class="media-body ms-3 ml-2">
                                            Use this form to add as many questions and answers about your company, brand business, or entity.
                                            The more questions with answers you add, the more accurate your chat bot will be.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        -->


                        {% if form.errors %}

                            <div class="row">
                                <div class="col-12">
                                    <div class="alert alert-danger alert-dismissible fade show">
                                        <div class="media align-items-center">
                                            <i class="feather icon-alert-circle h2 mb-0"></i>
                                            <div class="media-body ms-3 ml-2">
                                                Please fix the errors below.
                                            </div>
                                        </div>
                                        <button type="button" class="btn-close" data-BS-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                </div>
                            </div>

                        {% endif %}


                        <div class="row">





                            <div class="col-sm-4">
                                <!-- Basic Inputs -->
                                <div class="card">

                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Add Question & Answer</span>
                                            <span class="text-gray-400 mt-1 mb-5 fw-semibold fs-6">
                                                Use this form to add as many questions and answers about your company, brand business, or entity. The more questions with answers you add, the more accurate your chat bot will be.
                                            </span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>


                                    <div class="card-body">


                                        <form method="post">
                                            {% csrf_token %}
                                            <div class="form-group input-group-lg">
                                                <label class="form-label" for="question">Question:</label>
                                                <input type="text" name="question" class="form-control form-control-lg" placeholder="Question" required>
                                            </div>

                                            <div class="form-group mb-0">
                                                <label class="form-label" for="answer">Answer</label>
                                                <textarea class="form-control" id="answer" name="answer" rows="4" required></textarea>
                                            </div>

                                            <button class="btn btn-primary me-2 mt-4" type="submit">Add Q & A <i class="fas fa-arrow-alt-circle-right me-0 ms-2"></i></button>
                                        </form>





                                    </div>
                                    <div class="card-footer">
                                        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#launchModal">
                                            Launch Chat Bot <i class="fas fa-rocket me-0 ms-2"></i>
                                        </button>
                                        <!--
                                        <button type="reset" class="btn btn-light">Reset</button>
                                        -->
                                    </div>
                                </div>
                            </div>



                            <!-- [ form-element ] start -->
                            <div class="col-sm-8">
                                <!-- Basic Inputs -->
                                <div class="card">

                                    <div class="card-header pt-7">
                                        <!--begin::Title-->
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Chat Bot Questions & Answers</span>
                                            <span class="text-gray-400 mt-1 mb-5 fw-semibold fs-6">
                                            </span>
                                        </h3>
                                        <!--end::Title-->
                                    </div>



                                    <div class="card-body">




                                        <!--
                                        <div class="card card-border-c-blue">
											<div class="card-header">
                                                <strong>Question</strong>
                                                <button type="button" class="btn btn-danger btn-sm float-end" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                                    <i class="feather icon-slash mr-1"></i> Delete
                                                </button>
											</div>
											<div class="card-block card-task">
												<div class="row">
													<div class="col-sm-12">
														<p class="mb-0">
                                                            <strong>Answer</strong>
                                                        </p>
													</div>
												</div>
											</div>
										</div>
										-->







                                        {% if questions %}
                                        {% for question in questions %}
                                            <div class="overflow-auto pb-5">
																<!--begin::Notice-->
																<div class="notice d-flex bg-light-primary rounded border-primary border border-dashed min-w-lg-600px flex-shrink-0 p-6">
																	<!--begin::Icon-->
																	<i class="ki-duotone ki-message-question fs-2tx text-primary me-4">
																		<span class="path1"></span>
																		<span class="path2"></span>
																		<span class="path3"></span>
																	</i>
																	<!--end::Icon-->
																	<!--begin::Wrapper-->
																	<div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
																		<!--begin::Content-->
																		<div class="mb-3 mb-md-0 fw-semibold">
																			<h4 class="text-gray-900 fw-bold">{{ question.question }}?</h4>
																			<div class="fs-6 text-gray-700 pe-7">{{ question.answer }}</div>
																		</div>
																		<!--end::Content-->
																		<!--begin::Action-->
																		<a href="#" class="btn btn-primary px-4 align-self-center text-nowrap"
                                                                           data-bs-toggle="modal" data-bs-target="#deleteModal" data-id="{{ question.pk }}">

                                                                            <i class="ki-duotone ki-cross fs-2x pe-0">
                                                                                <span class="path1"></span>
                                                                                <span class="path2"></span>
                                                                            </i>
                                                                        </a>
																		<!--end::Action-->
																	</div>
																	<!--end::Wrapper-->
																</div>
																<!--end::Notice-->
															</div>


                                            <!--
                                        <div class="card widget-content">
                                            <div class="card-block">
                                                <button type="button" class="btn-close btn-delete" data-bs-toggle="modal"
                                                        data-bs-target="#deleteModal" data-id="{{ question.pk }}"></button>
                                                <div class="row">
                                                    <div class="col-sm-12 m-b-20">
                                                        <div class="widget-lorem">
                                                            <div class="media align-items-center justify-content-center receive-bar">
                                                                <div class="me-3 photo-table">
                                                                    <h5 class="theme-bg text-white d-flex align-items-center justify-content-center">
                                                                        Q
                                                                    </h5>
                                                                </div>
                                                                <div class="media-body ml-2">
                                                                    <h4>{{ question.question }}</h4>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12 m-b-0">
                                                        <div class="widget-lorem">
                                                            <div class="media send-bar">
                                                                <div class="me-3 photo-table">
                                                                    <h5 class="text-white d-flex theme-bg2 align-items-center justify-content-center">
                                                                        A
                                                                    </h5>
                                                                </div>
                                                                <div class="media-body ml-2">
                                                                    <p>{{ question.answer }}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        -->
                                        {% endfor %}
                                    {% else %}
                                            <h3>You don't have any questions & answers yet!</h3>
                                    {% endif %}

                                    </div>

                                </div>
                            </div>
                            <!-- [ form-element ] end -->

                        </div>
                        <!-- [ Main Content ] end -->








        </div>
    </div>









    <div id="launchModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Launch Chat Bot?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you ready to launch and test your chat bot?  Don't worry, you can make changes anytime.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <a href="{% url 'chat_bot_update' chat_bot.pk %}" class="btn btn-success me-2">
                        Yes, Launch My Chat Bot <i class="fas fa-rocket mr-0 ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>


    <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Delete This Q & A?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure that you want to delete this single Q & A?  This cannot be un-done, however you can always re-add it.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="id" id="delete-id" value="">
                        <input type="hidden" name="action" value="delete">
                        <button type="submit" class="btn btn-danger me-2">
                            Yes, Delete This Q & A <i class="fas fa-times-circle mr-0 ml-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>











{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script>
    $('#deleteModal').on('show.bs.modal', function(e) {
      var id = e.relatedTarget.dataset.id;
      $('#delete-id').val(id);
    });
    </script>
{% endblock javascripts %}
