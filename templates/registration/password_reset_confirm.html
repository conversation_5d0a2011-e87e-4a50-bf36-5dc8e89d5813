{% extends "layouts/base_auth.html" %}
{% block title %}Password Reset{% endblock %}
{% block content %}

                    {% include 'main/includes/messages.html' %}
                    {% if msg %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <strong>{{ msg | safe }}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endif %}


                    {% if validlink %}
                    <h1 class="mb-0">Set a new password!</h1>
                    <p>
                        Enter in your new password below.
                    </p>
                    <form action="" method="POST" id="commonForm">
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="username">Email address</label>
                            <input type="password" class="form-control mb-0" name="new_password1" id="new_password1" placeholder="Password">
                            {% if form.new_password1.errors %}
                                <small class="text-danger">{{ form.new_password1.errors }}</small>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="username">Email address</label>
                            <input type="password" class="form-control mb-0" name="new_password2" id="new_password2" placeholder="Password again">
                            {% if form.new_password2.errors %}
                                <small class="text-danger">{{ form.new_password2.errors }}</small>
                            {% endif %}
                        </div>

                        <div class="d-inline-block w-100">
                            <button type="submit" class="btn btn-primary float-left">Reset My Password</button>
                        </div>
                    </form>

                    {% else %}

                    <h1 class="mb-0">Invalid Password Reset</h1>
                    <p>
                        The password reset link was invalid, possibly because it has already been used.
                        Please request a new password reset.
                    </p>

                    {% endif %}

{% endblock content %}