{% extends "layouts/base_auth.html" %}
{% block title %}Register{% endblock %}
{% block content %}
                            <h1 class="mb-0">Register</h1>

                            {% if email %}
                                <div class="alert alert-success" role="alert">
                                    Thank you for subscribing to our newsletter.<br /><br />
                                    Register free for Prospect Connect below .
                                    No credit card required.
                                </div>
                            {% endif %}

                            <p>Fill out the form below to get your Prospect Connect account.</p>
                            <form action="" method="POST" id="commonForm" class="mt-4">
                                {% csrf_token %}
                                <!--
                                <div class="form-group">
                                    <label for="first_name">First Name</label>
                                    <input type="text" class="form-control mb-0"
                                           name="first_name" id="first_name" placeholder="Your First Name">
                                    {% if form.first_name.errors %}
                                        <small class="text-danger">{{ form.first_name.errors }}</small>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="last_name">Last Name</label>
                                    <input type="text" class="form-control mb-0"
                                           name="last_name" id="last_name" placeholder="Your Last Name">
                                    {% if form.last_name.errors %}
                                        <small class="text-danger">{{ form.last_name.errors }}</small>
                                    {% endif %}
                                </div>
                                {{  form.errors }}
                                -->

                                <div class="form-group">
                                    <label for="first_name">First name</label>
                                    <input type="text" class="form-control mb-0" required
                                           name="first_name" id="first_name" placeholder="First name">
                                    {% if form.first_name.errors %}
                                        <small class="text-danger">{{ form.first_name.errors }}</small>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label for="last_name">Last name</label>
                                    <input type="text" class="form-control mb-0" required
                                           name="last_name" id="last_name" placeholder="Last name">
                                    {% if form.last_name.errors %}
                                        <small class="text-danger">{{ form.last_name.errors }}</small>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label for="email">Email address</label>
                                    <input type="email" class="form-control mb-0" value="{% if email %}{{ email }}{% endif %}"
                                           name="email" id="email" placeholder="Enter email">
                                    {% if form.email.errors %}
                                        <small class="text-danger">{{ form.email.errors }}</small>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="password1">Password</label>
                                    <input type="password" class="form-control mb-0"
                                           name="password1" id="password1" placeholder="Password">
                                    {% if form.password.errors %}
                                        <small class="text-danger">{{ form.password.errors }}</small>
                                    {% endif %}
                                    {% if form.password1.errors %}
                                        <small class="text-danger">{{ form.password1.errors }}</small>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="password2">Password again</label>
                                    <input type="password" class="form-control mb-0"
                                           name="password2" id="password2" placeholder="Password again">
                                    {% if form.password2.errors %}
                                        <small class="text-danger">{{ form.password2.errors }}</small>
                                    {% endif %}
                                </div>
                            
                            
                            
                                

                                <div class="form-group mb-0">
                                    <label for="phone">Mobile Phone</label>
                                    <input type="text" class="form-control mb-0" required
                                           name="phone" id="phone" placeholder="Mobile Phone">
                                </div>
                            
                                <div class="d-inline-block w-100 mb-4">
                                    <div class="custom-control custom-checkbox d-inline-block mt-2 pt-1">
                                        <input type="checkbox" class="custom-control-input" id="customCheck1">
                                        <label class="custom-control-label" for="customCheck1">
                                            Check this box to receive SMS messages regarding your leads. Up to 5 messages per month, msg and data fees may apply. Reply STOP to cancel and HELP for help.
                                        </label>
                                    </div>
                                </div>
                            
                            
                                <div class="d-inline-block w-100">
                                    <!--
                                    <div class="custom-control custom-checkbox d-inline-block mt-2 pt-1">
                                        <input type="checkbox" class="custom-control-input" id="customCheck1">
                                        <label class="custom-control-label" for="customCheck1">I accept <a href="#">Terms and Conditions</a></label>
                                    </div>
                                    -->
                                    <button type="submit" class="btn btn-primary float-right">Register</button>
                                </div>
                                <div class="sign-info">
                                    <span class="dark-color d-inline-block line-height-2 mb-4">Already Have Account ? <a href="{% url 'login' %}">Log In</a></span>
                                    
                                    
                                    <span class="dark-color d-inline-block line-height-2 mb-4">Copyright &copy; 2024 - Comprehensive Coverage LLC.</span>
                                    <!--
                                    <ul class="iq-social-media">
                                        <li><a href="#"><i class="ri-facebook-box-line"></i></a></li>
                                        <li><a href="#"><i class="ri-twitter-line"></i></a></li>
                                        <li><a href="#"><i class="ri-instagram-line"></i></a></li>
                                    </ul>
                                    -->
                                </div>
                            </form>
{% endblock content %}

{#
                    <form action="" method="POST" id="commonForm">
                        {% csrf_token %}
                        <div class="form-group position-relative clearfix">
                            <input name="email" type="email" class="form-control" placeholder="Email Address"
                                   aria-label="Email Address" value="{% if email %}{{ email }}{% endif %}">
                            <div class="login-popover login-popover-abs" data-bs-toggle="popover"
                                 data-bs-trigger="hover" title="Username" data-bs-content="Your best email address">
                                <i class="fa fa-info-circle"></i>
                            </div>
                            {% if form.email.errors %}
                                <small class="text-danger">{{ form.email.errors }}</small>
                            {% endif %}
                        </div>
                        <div class="form-group clearfix position-relative password-wrapper">
                            <input name="password1" type="password" class="form-control" autocomplete="off" placeholder="Password" aria-label="Password">
                            <i class="fa fa-eye password-indicator"></i>
                            {% if form.password.errors %}
                                <small class="text-danger">{{ form.password.errors }}</small>
                            {% endif %}
                            {% if form.password1.errors %}
                                <small class="text-danger">{{ form.password1.errors }}</small>
                            {% endif %}
                        </div>
                        <div class="form-group clearfix position-relative password-wrapper">
                            <input name="password2" type="password" class="form-control" autocomplete="off" placeholder="Password again" aria-label="Password again">
                            <i class="fa fa-eye password-indicator"></i>
                            {% if form.password2.errors %}
                                <small class="text-danger">{{ form.password2.errors }}</small>
                            {% endif %}
                        </div>
                        <div class="form-group clearfix position-relative password-wrapper">
                            <input name="first_name" type="text" class="form-control" autocomplete="off" placeholder="First Name" aria-label="First Name">
                            {% if form.first_name.errors %}
                                <small class="text-danger">{{ form.first_name.errors }}</small>
                            {% endif %}
                        </div>
                        <div class="form-group clearfix position-relative password-wrapper">
                            <input name="last_name" type="text" class="form-control" autocomplete="off" placeholder="Last Name" aria-label="Last Name">
                            {% if form.last_name.errors %}
                                <small class="text-danger">{{ form.last_name.errors }}</small>
                            {% endif %}
                        </div>
                        <div class="form-group mb-0 clearfix">
                            <button type="submit" class="btn btn-lg btn-primary btn-theme">Register</button>
                        </div>
                        <div class="extra-login clearfix">
                        </div>
                    </form>
                    <div class="clearfix"></div>
                    <p>Already have an account? <a href="/login/" class="thembo"> Login here</a></p>

#}








