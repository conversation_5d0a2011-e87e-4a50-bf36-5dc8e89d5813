{% extends "layouts/base_auth.html" %}
{% block title %}Password Reset{% endblock %}
{% block content %}

    
    {% include 'main/includes/messages.html' %}
    {% if msg %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>{{ msg | safe }}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endif %}

    <h2 class="mb-2 mt-4 fs-7 fw-bolder">Password Reset</h2>
    <p class="mb-9">
        Forgotten your password? Enter your email address below,
        and we'll email instructions for setting a new one.
    </p>
    
    <form method="POST">
        {% csrf_token %}
        <div class="mb-3">
            <label for="username" class="form-label">Username</label>
            <input type="email" class="form-control" id="username" name="username" aria-describedby="emailHelp" />
            {% if form.email.errors %}
                <small class="text-danger">{{ form.email.errors }}</small>
            {% endif %}
        </div>
        <button type="submit" class="btn btn-primary w-100 py-8 mb-4 rounded-2">
            Reset My Password
            <i class="ti ti-chevron-right ms-2"></i>
        </button>
    </form>


{% endblock content %}