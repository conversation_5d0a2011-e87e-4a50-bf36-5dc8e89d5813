<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Favicon icon-->
    <link rel="shortcut icon" type="image/png" href="/static/assets/images/logos/favicon.png" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <!-- Iconify -->
    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>

    <!-- jsQR for QR code scanning -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>

    <style>
        .form-container {
            max-width: 600px;
            margin: 0 auto;
        }
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        #qr-video-container {
            position: relative;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
            overflow: hidden;
            border-radius: 8px;
            display: none;
        }
        #qr-video {
            width: 100%;
            border-radius: 8px;
            background-color: #000;
        }
        #qr-canvas {
            display: none;
        }
        #qr-scan-region {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 2px solid rgba(0, 255, 0, 0.5);
            box-sizing: border-box;
            pointer-events: none;
        }
        #scanner-status {
            position: absolute;
            bottom: 10px;
            left: 0;
            right: 0;
            text-align: center;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
        }
        .scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
        }
    </style>

    <title>Member Action - Restaurant</title>
</head>
<body>
    <div class="container">

    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12 text-center">
                <img src="/static/logo_4.png" class="w-25 mb-3">
                    <h4 class="mb-4 mb-sm-0 card-title">Record Member Action</h4>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-body">
                    <div class="form-container">
                        <h5 class="mb-4">Record Member Action</h5>

                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post">
                            {% csrf_token %}

                            <div class="mb-3">
                                <label for="{{ form.qr_code.id_for_label }}" class="form-label">QR Code</label>
                                {{ form.qr_code }}
                                {% if form.qr_code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.qr_code.errors }}
                                    </div>
                                {% endif %}
                                <div class="form-text">{{ form.qr_code.help_text }}</div>

                                <!-- QR Code Scanner UI -->
                                <div class="mt-2">
                                    <button type="button" id="start-scanner" class="btn btn-outline-primary">
                                        <i class="fas fa-camera"></i> Scan QR Code
                                    </button>
                                </div>

                                <div id="qr-video-container" class="mt-3">
                                    <video id="qr-video" playsinline></video>
                                    <div id="qr-scan-region"></div>
                                    <div id="scanner-status">Ready to scan</div>
                                    <div id="scanner-loading" class="scanner-overlay" style="display: none;">
                                        <div class="spinner-border text-light" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <canvas id="qr-canvas" hidden></canvas>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.action_text.id_for_label }}" class="form-label">Action Details</label>
                                {{ form.action_text }}
                                {% if form.action_text.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.action_text.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">Record Action</button>
                                <div class="d-flex justify-content-between mt-2">
                                    <a href="{% url 'restaurant:register_member' %}" class="btn btn-outline-secondary">Register New Member</a>
                                    <a href="{% url 'restaurant:export_actions' %}" class="btn btn-outline-success">Export All Actions</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // QR Code Scanner functionality
            const startScannerBtn = document.getElementById('start-scanner');
            const videoContainer = document.getElementById('qr-video-container');
            const video = document.getElementById('qr-video');
            const canvas = document.getElementById('qr-canvas');
            const qrCodeInput = document.getElementById('{{ form.qr_code.id_for_label }}');
            const scannerStatus = document.getElementById('scanner-status');
            const scannerLoading = document.getElementById('scanner-loading');
            const ctx = canvas.getContext('2d');

            let scanning = false;
            let videoStream = null;

            // Start the QR code scanner
            startScannerBtn.addEventListener('click', function() {
                if (scanning) {
                    stopScanner();
                    startScannerBtn.innerHTML = '<i class="fas fa-camera"></i> Scan QR Code';
                    videoContainer.style.display = 'none';
                } else {
                    startScanner();
                    startScannerBtn.innerHTML = '<i class="fas fa-times"></i> Cancel Scanning';
                    videoContainer.style.display = 'block';
                    scannerStatus.textContent = 'Initializing camera...';
                    scannerLoading.style.display = 'flex';
                }
            });

            // Function to start the scanner
            function startScanner() {
                scanning = true;

                // Check if the browser supports getUserMedia
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    navigator.mediaDevices.getUserMedia({ 
                        video: { 
                            facingMode: "environment",
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        } 
                    })
                    .then(function(stream) {
                        videoStream = stream;
                        video.srcObject = stream;
                        video.setAttribute("playsinline", true); // Required for iOS

                        // Wait for video to be ready
                        video.onloadedmetadata = function() {
                            scannerLoading.style.display = 'none';
                            scannerStatus.textContent = 'Scanning for QR code...';
                            video.play();
                            requestAnimationFrame(tick);
                        };
                    })
                    .catch(function(error) {
                        console.error("Error accessing the camera: ", error);
                        scannerLoading.style.display = 'none';
                        scannerStatus.textContent = 'Camera access denied';
                        alert("Could not access the camera. Please make sure you've granted camera permissions.");
                        scanning = false;
                        startScannerBtn.innerHTML = '<i class="fas fa-camera"></i> Scan QR Code';
                        videoContainer.style.display = 'none';
                    });
                } else {
                    alert("Sorry, your browser doesn't support camera access.");
                    scanning = false;
                }
            }

            // Function to stop the scanner
            function stopScanner() {
                scanning = false;
                if (videoStream) {
                    videoStream.getTracks().forEach(track => {
                        track.stop();
                    });
                    videoStream = null;
                }
                scannerStatus.textContent = 'Ready to scan';
                scannerLoading.style.display = 'none';
            }

            // Process video frames to detect QR codes
            function tick() {
                if (!scanning) return;

                if (video.readyState === video.HAVE_ENOUGH_DATA) {
                    // Set canvas dimensions to match video
                    canvas.height = video.videoHeight;
                    canvas.width = video.videoWidth;

                    // Draw the video frame to the canvas
                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

                    // Get the image data from the canvas
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                    // Scan for QR code
                    const code = jsQR(imageData.data, imageData.width, imageData.height, {
                        inversionAttempts: "dontInvert",
                    });

                    // If a QR code is found
                    if (code) {
                        console.log("QR Code detected: ", code.data);

                        // Update the status to show success
                        scannerStatus.textContent = 'QR Code detected!';

                        // Update the form field with the QR code data
                        qrCodeInput.value = code.data;

                        // Show success overlay briefly
                        scannerLoading.style.display = 'flex';
                        scannerLoading.innerHTML = '<div class="text-success"><i class="fas fa-check-circle fa-3x"></i></div>';

                        // Stop scanning after a brief delay to show success
                        setTimeout(function() {
                            stopScanner();
                            startScannerBtn.innerHTML = '<i class="fas fa-camera"></i> Scan QR Code';
                            videoContainer.style.display = 'none';

                            // Reset the loading overlay
                            scannerLoading.innerHTML = '<div class="spinner-border text-light" role="status"><span class="visually-hidden">Loading...</span></div>';
                        }, 1000);
                    }
                }

                // Continue scanning
                if (scanning) {
                    requestAnimationFrame(tick);
                }
            }
        });
    </script>
</body>
</html>
