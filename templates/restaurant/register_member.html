<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Favicon icon-->
    <link rel="shortcut icon" type="image/png" href="/static/assets/images/logos/favicon.png" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <!-- Iconify -->
    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>

    <style>
        .form-container {
            max-width: 600px;
            margin: 0 auto;
        }
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
    </style>

    <title>Register Member - Restaurant</title>
</head>
<body>
    <div class="container">

    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12 text-center">
                <img src="/static/logo_4.png" class="w-25 mb-3">
                <h4 class="card-title">Register New Member</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-body">
                    <div class="form-container">
                        <h5 class="mb-4">Enter Member Information</h5>

                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post">
                            {% csrf_token %}

                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Name</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.name.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                                {{ form.phone_number }}
                                {% if form.phone_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.phone_number.errors }}
                                    </div>
                                {% endif %}
                                <div class="form-text">Enter phone number with country code (e.g., +1234567890)</div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">Register Member</button>
                                <a href="{% url 'restaurant:member_action' %}" class="btn btn-outline-secondary">Record Member Action</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Any JavaScript needed for this page
        });
    </script>
</body>
</html>
