{% extends "layouts/base.html" %}
{% load static %}

{% block title %}{{ campaign.name }} - Campaign Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">{{ campaign.name }}</h1>
                    <p class="text-muted">{{ campaign.description }}</p>
                </div>
                <div>
                    <span class="badge badge-{% if campaign.status == 'active' %}success{% elif campaign.status == 'paused' %}warning{% elif campaign.status == 'draft' %}secondary{% else %}danger{% endif %} badge-lg">
                        {{ campaign.get_status_display }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Leads
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_leads }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Emails Sent
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.sent_emails }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-paper-plane fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Scheduled Emails
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.scheduled_emails }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                In Progress
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.in_progress_leads }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-spinner fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Campaign Details -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Target Category:</strong> {{ campaign.target_lead_category.name }}</p>
                            <p><strong>Created:</strong> {{ campaign.date_created|date:"M d, Y g:i A" }}</p>
                            {% if campaign.start_date %}
                            <p><strong>Started:</strong> {{ campaign.start_date|date:"M d, Y g:i A" }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> 
                                <span class="badge badge-{% if campaign.status == 'active' %}success{% elif campaign.status == 'paused' %}warning{% elif campaign.status == 'draft' %}secondary{% else %}danger{% endif %}">
                                    {{ campaign.get_status_display }}
                                </span>
                            </p>
                            {% if campaign.end_date %}
                            <p><strong>End Date:</strong> {{ campaign.end_date|date:"M d, Y g:i A" }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Campaign Actions -->
                    <div class="mt-3">
                        <h6 class="font-weight-bold">Actions:</h6>
                        <div class="btn-group" role="group">
                            {% if campaign.status == 'draft' %}
                                <button type="button" class="btn btn-success btn-sm" onclick="startCampaign()">
                                    <i class="fas fa-play"></i> Start Campaign
                                </button>
                            {% elif campaign.status == 'active' %}
                                <button type="button" class="btn btn-warning btn-sm" onclick="pauseCampaign()">
                                    <i class="fas fa-pause"></i> Pause Campaign
                                </button>
                            {% elif campaign.status == 'paused' %}
                                <button type="button" class="btn btn-success btn-sm" onclick="resumeCampaign()">
                                    <i class="fas fa-play"></i> Resume Campaign
                                </button>
                            {% endif %}
                            
                            <button type="button" class="btn btn-primary btn-sm" onclick="addLeads()">
                                <i class="fas fa-user-plus"></i> Add Leads
                            </button>
                            
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit"></i> Edit Campaign
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Sequences -->
            <div class="card shadow mt-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Email Sequences</h6>
                    <a href="#" class="btn btn-primary btn-sm">Add Sequence</a>
                </div>
                <div class="card-body">
                    {% if campaign.sequences.all %}
                        {% for sequence in campaign.sequences.all %}
                        <div class="border rounded p-3 mb-3">
                            <h6 class="font-weight-bold">{{ sequence.name }}</h6>
                            <p class="text-muted mb-2">{{ sequence.description }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-sm">{{ sequence.messages.count }} message(s)</span>
                                <div>
                                    <a href="#" class="btn btn-outline-primary btn-sm">Edit</a>
                                    <a href="#" class="btn btn-outline-secondary btn-sm">View Messages</a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted">No email sequences created yet.</p>
                            <a href="#" class="btn btn-primary">Create First Sequence</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Lead Progress -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lead Progress</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Pending</span>
                            <span class="text-secondary font-weight-bold">{{ stats.pending_leads }}</span>
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-secondary" style="width: {% widthratio stats.pending_leads stats.total_leads 100 %}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>In Progress</span>
                            <span class="text-warning font-weight-bold">{{ stats.in_progress_leads }}</span>
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-warning" style="width: {% widthratio stats.in_progress_leads stats.total_leads 100 %}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Completed</span>
                            <span class="text-success font-weight-bold">{{ stats.completed_leads }}</span>
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-success" style="width: {% widthratio stats.completed_leads stats.total_leads 100 %}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <!-- Add recent campaign activity here -->
                        <div class="text-center text-muted">
                            <small>Activity tracking coming soon...</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function startCampaign() {
    if (confirm('Are you sure you want to start this campaign?')) {
        // Make API call to start campaign
        fetch(`/emailer/api/campaigns/{{ campaign.id }}/start/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert(data.message);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while starting the campaign.');
        });
    }
}

function pauseCampaign() {
    if (confirm('Are you sure you want to pause this campaign?')) {
        // Make API call to pause campaign
        fetch(`/emailer/api/campaigns/{{ campaign.id }}/pause/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert(data.message);
                location.reload();
            }
        });
    }
}

function resumeCampaign() {
    if (confirm('Are you sure you want to resume this campaign?')) {
        // Make API call to resume campaign
        fetch(`/emailer/api/campaigns/{{ campaign.id }}/resume/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert(data.message);
                location.reload();
            }
        });
    }
}

function addLeads() {
    if (confirm('Add all available leads from the target category to this campaign?')) {
        // Make API call to add leads
        fetch(`/emailer/api/campaigns/{{ campaign.id }}/add_leads/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert(data.message);
                location.reload();
            }
        });
    }
}
</script>
{% endblock %}
