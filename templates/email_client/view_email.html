{% extends "layouts/base.html" %}

{% block title %} Email Client - View Email {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}

<div class="card card-body py-3">
  <div class="row align-items-center">
    <div class="col-12">
      <div class="d-sm-flex align-items-center justify-space-between">
        <h4 class="mb-4 mb-sm-0 card-title">Email Client</h4>
        <nav aria-label="breadcrumb" class="ms-auto">
          <ol class="breadcrumb">
            <li class="breadcrumb-item d-flex align-items-center">
              <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
              </a>
            </li>
            <li class="breadcrumb-item" aria-current="page">
              <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                Email Client
              </span>
            </li>
          </ol>
        </nav>
      </div>
    </div>
  </div>
</div>

<div class="card overflow-hidden chat-application">
  <div class="d-flex align-items-center justify-content-between gap-6 m-3 d-lg-none">
    <button class="btn btn-primary d-flex" type="button" data-bs-toggle="offcanvas" data-bs-target="#chat-sidebar" aria-controls="chat-sidebar">
      <i class="ti ti-menu-2 fs-5"></i>
    </button>
    <form class="position-relative w-100">
      <input type="text" class="form-control search-chat py-2 ps-5" id="text-srh" placeholder="Search Email" />
      <i class="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
    </form>
  </div>
  <div class="d-flex w-100">
    <!-- Left sidebar -->
    <div class="left-part border-end w-20 flex-shrink-0 d-none d-lg-block">
      <div class="px-9 pt-4 pb-3">
        <a href="{% url 'compose_email' %}?account_id={{ selected_account.id }}" class="btn btn-primary fw-semibold py-8 w-100">
          Compose
        </a>
      </div>
      <ul class="list-group h-n150" data-simplebar>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'inbox' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='inbox' account_id=selected_account.id %}">
            <i class="ti ti-inbox fs-5"></i>Inbox
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'starred' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='starred' account_id=selected_account.id %}">
            <i class="ti ti-star fs-5 text-warning"></i>Starred
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'sent' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='sent' account_id=selected_account.id %}">
            <i class="ti ti-send fs-5"></i>Sent
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'drafts' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='drafts' account_id=selected_account.id %}">
            <i class="ti ti-file fs-5"></i>Drafts
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'important' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='important' account_id=selected_account.id %}">
            <i class="ti ti-alert-circle fs-5 text-info"></i>Important
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'trash' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='trash' account_id=selected_account.id %}">
            <i class="ti ti-trash fs-5"></i>Trash
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <div class="d-flex align-items-center justify-content-between mb-2 mt-4">
            <h6 class="fw-semibold mb-0 text-muted">Labels</h6>
            <a href="javascript:void(0)" class="text-dark hover-primary">
              <i class="ti ti-plus fs-4"></i>
            </a>
          </div>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='work' account_id=selected_account.id %}">
            <i class="ti ti-briefcase fs-5 text-success"></i>Work
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='personal' account_id=selected_account.id %}">
            <i class="ti ti-user fs-5 text-primary"></i>Personal
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='health' account_id=selected_account.id %}">
            <i class="ti ti-heart-rate-monitor fs-5 text-success"></i>Health
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='positive' account_id=selected_account.id %}">
            <i class="ti ti-thumb-up fs-5 text-success"></i>Positive
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='negative' account_id=selected_account.id %}">
            <i class="ti ti-bookmark fs-5 text-danger"></i>Negative
          </a>
        </li>
      </ul>
    </div>

    <div class="d-flex w-100">
      <!-- Email list -->
      <div class="min-width-340">
        <div class="border-end user-chat-box h-100">
          <div class="px-4 pt-9 pb-6 d-none d-lg-block">
            <form class="position-relative">
              <input type="text" class="form-control search-chat py-2 ps-5" id="text-srh" placeholder="Search" />
              <i class="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
            </form>
          </div>
          <div class="app-chat">
            <ul class="chat-users mh-n100" data-simplebar>
              {% for email_item in emails %}
              <li>
                <a href="{% url 'view_email' email_id=email_item.id %}" class="px-4 py-3 bg-hover-light-black d-flex align-items-start chat-user {% if email_item.id == email.id %}bg-light-subtle{% endif %}" id="chat_user_{{ email_item.id }}" data-user-id="{{ email_item.id }}">
                  <div class="form-check mb-0">
                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault_{{ email_item.id }}" />
                  </div>
                  <div class="position-relative w-100 ms-2">
                    <div class="d-flex align-items-center justify-content-between mb-2">
                      <h6 class="mb-0">{{ email_item.sender }}</h6>
                      <span class="badge text-bg-primary">{{ email_item.subject|truncatechars:10 }}</span>
                    </div>
                    <h6 class="fw-semibold text-dark">
                      {{ email_item.subject|truncatechars:40 }}
                    </h6>
                    <div class="d-flex align-items-center justify-content-between">
                      <div class="d-flex align-items-center">
                        <span>
                          <i class="ti ti-star fs-4 me-2 {% if email_item.is_starred %}text-warning{% else %}text-muted{% endif %}"></i>
                        </span>
                        <span class="d-block">
                          <i class="ti ti-alert-circle {% if email_item.is_important %}text-info{% else %}text-muted{% endif %}"></i>
                        </span>
                      </div>
                      <p class="mb-0 fs-2 text-muted">{{ email_item.received_date|date:"h:i A" }}</p>
                    </div>
                  </div>
                </a>
              </li>
              {% empty %}
              <li class="text-center py-5">
                <p class="text-muted">No emails found in this folder.</p>
              </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>

      <!-- Email content area -->
      <div class="w-100">
        <div class="chat-container h-100 w-100">
          <div class="chat-box-inner-part h-100">
            <div class="chatting-box app-email-chatting-box">
              <div class="p-9 py-3 border-bottom chat-meta-user">
                <ul class="list-unstyled mb-0 d-flex align-items-center">
                  <li class="d-lg-none d-block">
                    <a class="text-dark back-btn px-2 fs-5 bg-hover-primary nav-icon-hover position-relative z-index-5" href="javascript:void(0)">
                      <i class="ti ti-arrow-left"></i>
                    </a>
                  </li>
                  <li class="ms-auto">
                    <div class="d-flex align-items-center">
                      <a href="{% url 'reply_email' email_id=email.id %}" class="text-dark px-2 fs-5 bg-hover-primary nav-icon-hover">
                        <i class="ti ti-arrow-back-up"></i>
                      </a>
                      <a href="{% url 'forward_email' email_id=email.id %}" class="text-dark px-2 fs-5 bg-hover-primary nav-icon-hover">
                        <i class="ti ti-arrow-forward-up"></i>
                      </a>
                      <a href="javascript:void(0)" class="text-dark px-2 fs-5 bg-hover-primary nav-icon-hover star-email" data-email-id="{{ email.id }}">
                        <i class="ti ti-star {% if email.is_starred %}text-warning{% endif %}"></i>
                      </a>
                      <a href="javascript:void(0)" class="text-dark px-2 fs-5 bg-hover-primary nav-icon-hover mark-important" data-email-id="{{ email.id }}">
                        <i class="ti ti-alert-circle {% if email.is_important %}text-info{% endif %}"></i>
                      </a>
                      <a href="{% url 'delete_email' email_id=email.id %}" class="text-dark px-2 fs-5 bg-hover-primary nav-icon-hover" onclick="return confirm('Are you sure you want to delete this email?');">
                        <i class="ti ti-trash"></i>
                      </a>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="position-relative overflow-hidden">
                <div class="position-relative">
                  <div class="chat-box email-box mh-n100 p-9" data-simplebar="init">
                    <div class="chat-list chat active-chat" data-user-id="{{ email.id }}">
                      <div class="hstack align-items-start mb-7 pb-1 align-items-center justify-content-between flex-wrap gap-6">
                        <div class="d-flex align-items-center gap-2">
                          <img src="/static/assets/images/profile/user-1.jpg" alt="user" width="48" height="48" class="rounded-circle" />
                          <div>
                            <h6 class="fw-semibold mb-0">
                              {{ email.sender }}
                            </h6>
                            <p class="mb-0">{{ email.sender_email }}</p>
                          </div>
                        </div>
                        <span class="badge text-bg-primary">{{ folder|title }}</span>
                      </div>
                      <div class="border-bottom pb-7 mb-7">
                        <h4 class="fw-semibold text-dark mb-3">
                          {{ email.subject }}
                        </h4>
                        <p class="mb-3 text-dark">To: {{ email.recipients }}</p>
                        {% if email.cc %}
                        <p class="mb-3 text-dark">CC: {{ email.cc }}</p>
                        {% endif %}
                        <p class="mb-3 text-dark">Date: {{ email.received_date }}</p>
                        <div class="email-content mt-5">
                          {% if email.body_html %}
                          <div class="email-html-content">
                            {{ email.body_html|safe }}
                          </div>
                          {% else %}
                          <div class="email-text-content">
                            <p>{{ email.body_text|linebreaks }}</p>
                          </div>
                          {% endif %}
                        </div>
                      </div>

                      {% if email.attachments %}
                      <div class="mb-3">
                        <h6 class="fw-semibold mb-0 text-dark mb-3">
                          Attachments
                        </h6>
                        <div class="d-block d-sm-flex align-items-center gap-4">
                          {% for attachment in email.attachments %}
                          <a href="javascript:void(0)" class="hstack gap-3 mb-2 mb-sm-0">
                            <div class="d-flex align-items-center gap-3">
                              <div class="rounded-1 text-bg-light p-6">
                                <img src="/static/assets/images/chat/icon-adobe.svg" alt="attachment-icon" width="24" height="24" />
                              </div>
                              <div>
                                <h6 class="fw-semibold">
                                  {{ attachment.filename }}
                                </h6>
                                <div class="d-flex align-items-center gap-3 fs-2 text-muted">
                                  <span>{{ attachment.size }}</span>
                                  <span>{{ attachment.date }}</span>
                                </div>
                              </div>
                            </div>
                          </a>
                          {% endfor %}
                        </div>
                      </div>
                      {% endif %}

                      <div class="d-flex align-items-center gap-2 mt-5">
                        <a href="{% url 'reply_email' email_id=email.id %}" class="btn btn-primary">Reply</a>
                        <a href="{% url 'forward_email' email_id=email.id %}" class="btn btn-outline-primary">Forward</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile sidebar -->
  <div class="offcanvas offcanvas-start user-chat-box" tabindex="-1" id="chat-sidebar" aria-labelledby="offcanvasExampleLabel">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasExampleLabel">
        Email
      </h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="px-9 pt-4 pb-3">
      <a href="{% url 'compose_email' %}?account_id={{ selected_account.id }}" class="btn btn-primary fw-semibold py-8 w-100">
        Compose
      </a>
    </div>
    <ul class="list-group h-n150" data-simplebar>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'inbox' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='inbox' account_id=selected_account.id %}">
          <i class="ti ti-inbox fs-5"></i>Inbox
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'starred' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='starred' account_id=selected_account.id %}">
          <i class="ti ti-star fs-5 text-warning"></i>Starred
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'sent' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='sent' account_id=selected_account.id %}">
          <i class="ti ti-send fs-5"></i>Sent
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'drafts' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='drafts' account_id=selected_account.id %}">
          <i class="ti ti-file fs-5"></i>Drafts
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'important' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='important' account_id=selected_account.id %}">
          <i class="ti ti-alert-circle fs-5 text-info"></i>Important
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'trash' %}bg-light-subtle{% endif %}" href="{% url 'view_folder_account' folder_name='trash' account_id=selected_account.id %}">
          <i class="ti ti-trash fs-5"></i>Trash
        </a>
      </li>
    </ul>
  </div>
</div>

{% endblock content %}

<!-- Specific JS goes HERE -->
{% block javascripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Handle star icon clicks
    document.querySelectorAll('.star-email').forEach(function(star) {
      star.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const emailId = this.dataset.emailId;
        const starIcon = this.querySelector('i');

        // Toggle star class
        starIcon.classList.toggle('text-warning');

        // Send AJAX request to star/unstar email
        fetch(`/email_client/api/email/${emailId}/star/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.status !== 'success') {
            console.error('Error starring email:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
      });
    });

    // Handle important icon clicks
    document.querySelectorAll('.mark-important').forEach(function(important) {
      important.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const emailId = this.dataset.emailId;
        const importantIcon = this.querySelector('i');

        // Toggle important class
        importantIcon.classList.toggle('text-info');

        // Send AJAX request to mark email as important/not important
        fetch(`/email_client/api/email/${emailId}/important/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.status !== 'success') {
            console.error('Error marking email as important:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
      });
    });

    // Helper function to get CSRF token
    function getCookie(name) {
      let cookieValue = null;
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.substring(0, name.length + 1) === (name + '=')) {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
            break;
          }
        }
      }
      return cookieValue;
    }
  });
</script>
{% endblock javascripts %}
