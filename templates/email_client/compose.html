{% extends "layouts/base.html" %}

{% block title %} Email Client - Compose {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
{% endblock stylesheets %}

{% block content %}

<div class="card card-body py-3">
  <div class="row align-items-center">
    <div class="col-12">
      <div class="d-sm-flex align-items-center justify-space-between">
        <h4 class="mb-4 mb-sm-0 card-title">Email Client - {% if action == 'reply' %}Reply{% elif action == 'forward' %}Forward{% else %}Compose{% endif %}</h4>
        <nav aria-label="breadcrumb" class="ms-auto">
          <ol class="breadcrumb">
            <li class="breadcrumb-item d-flex align-items-center">
              <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
              </a>
            </li>
            <li class="breadcrumb-item" aria-current="page">
              <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                Email Client
              </span>
            </li>
          </ol>
        </nav>
      </div>
    </div>
  </div>
</div>

<div class="card overflow-hidden">
  <div class="d-flex w-100">
    <!-- Left sidebar -->
    <div class="left-part border-end w-20 flex-shrink-0 d-none d-lg-block">
      <div class="px-9 pt-4 pb-3">
        <a href="{% url 'compose_email' %}?account_id={{ selected_account.id }}" class="btn btn-primary fw-semibold py-8 w-100">
          Compose
        </a>
      </div>
      <ul class="list-group h-n150" data-simplebar>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='inbox' account_id=selected_account.id %}">
            <i class="ti ti-inbox fs-5"></i>Inbox
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='starred' account_id=selected_account.id %}">
            <i class="ti ti-star fs-5 text-warning"></i>Starred
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='sent' account_id=selected_account.id %}">
            <i class="ti ti-send fs-5"></i>Sent
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='drafts' account_id=selected_account.id %}">
            <i class="ti ti-file fs-5"></i>Drafts
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='important' account_id=selected_account.id %}">
            <i class="ti ti-alert-circle fs-5 text-info"></i>Important
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='trash' account_id=selected_account.id %}">
            <i class="ti ti-trash fs-5"></i>Trash
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <div class="d-flex align-items-center justify-content-between mb-2 mt-4">
            <h6 class="fw-semibold mb-0 text-muted">Labels</h6>
            <a href="javascript:void(0)" class="text-dark hover-primary">
              <i class="ti ti-plus fs-4"></i>
            </a>
          </div>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='work' account_id=selected_account.id %}">
            <i class="ti ti-briefcase fs-5 text-success"></i>Work
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='personal' account_id=selected_account.id %}">
            <i class="ti ti-user fs-5 text-primary"></i>Personal
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='health' account_id=selected_account.id %}">
            <i class="ti ti-heart-rate-monitor fs-5 text-success"></i>Health
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='positive' account_id=selected_account.id %}">
            <i class="ti ti-thumb-up fs-5 text-success"></i>Positive
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='negative' account_id=selected_account.id %}">
            <i class="ti ti-bookmark fs-5 text-danger"></i>Negative
          </a>
        </li>
      </ul>
    </div>

    <!-- Compose area -->
    <div class="w-100 p-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title mb-4">{% if action == 'reply' %}Reply to Email{% elif action == 'forward' %}Forward Email{% else %}Compose New Email{% endif %}</h5>

          <form method="post" action="{% if action == 'reply' %}{% url 'reply_email' email_id=original_email.id %}{% elif action == 'forward' %}{% url 'forward_email' email_id=original_email.id %}{% else %}{% url 'compose_email' %}{% endif %}">
            {% csrf_token %}

            <div class="mb-3">
              <label for="recipients" class="form-label">To</label>
              <input type="text" class="form-control" id="recipients" name="recipients" value="{{ recipients }}" required>
            </div>

            <div class="mb-3">
              <label for="cc" class="form-label">CC</label>
              <input type="text" class="form-control" id="cc" name="cc" value="{{ cc|default:'' }}">
            </div>

            <div class="mb-3">
              <label for="bcc" class="form-label">BCC</label>
              <input type="text" class="form-control" id="bcc" name="bcc" value="{{ bcc|default:'' }}">
            </div>

            <div class="mb-3">
              <label for="subject" class="form-label">Subject</label>
              <input type="text" class="form-control" id="subject" name="subject" value="{{ reply_subject|default:'' }}" required>
            </div>

            <div class="mb-3">
              <label for="editor" class="form-label">Message</label>
              <div id="editor" style="height: 300px;">{{ reply_body|default:''}}</div>
              <textarea name="body" id="body" style="display: none;"></textarea>
            </div>

            <div class="mb-3">
              <label for="attachments" class="form-label">Attachments</label>
              <input type="file" class="form-control" id="attachments" name="attachments" multiple>
              <small class="form-text text-muted">You can select multiple files.</small>
            </div>

            <div class="d-flex justify-content-between">
              <a href="{% url 'email_client_account' account_id=selected_account.id %}" class="btn btn-secondary">Cancel</a>
              <div>
                <button type="submit" name="draft" class="btn btn-outline-primary me-2">Save as Draft</button>
                <button type="submit" name="send" class="btn btn-primary">Send</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile sidebar -->
  <div class="offcanvas offcanvas-start user-chat-box" tabindex="-1" id="chat-sidebar" aria-labelledby="offcanvasExampleLabel">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasExampleLabel">
        Email
      </h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="px-9 pt-4 pb-3">
      <a href="{% url 'compose_email' %}?account_id={{ selected_account.id }}" class="btn btn-primary fw-semibold py-8 w-100">
        Compose
      </a>
    </div>
    <ul class="list-group h-n150" data-simplebar>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='inbox' account_id=selected_account.id %}">
          <i class="ti ti-inbox fs-5"></i>Inbox
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='starred' account_id=selected_account.id %}">
          <i class="ti ti-star fs-5 text-warning"></i>Starred
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='sent' account_id=selected_account.id %}">
          <i class="ti ti-send fs-5"></i>Sent
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='drafts' account_id=selected_account.id %}">
          <i class="ti ti-file fs-5"></i>Drafts
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='important' account_id=selected_account.id %}">
          <i class="ti ti-alert-circle fs-5 text-info"></i>Important
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1" href="{% url 'view_folder_account' folder_name='trash' account_id=selected_account.id %}">
          <i class="ti ti-trash fs-5"></i>Trash
        </a>
      </li>
    </ul>
  </div>
</div>

{% endblock content %}

<!-- Specific JS goes HERE -->
{% block javascripts %}
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize Quill editor
    var quill = new Quill('#editor', {
      theme: 'snow',
      modules: {
        toolbar: [
          [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
          ['bold', 'italic', 'underline', 'strike'],
          [{ 'color': [] }, { 'background': [] }],
          [{ 'list': 'ordered'}, { 'list': 'bullet' }],
          [{ 'indent': '-1'}, { 'indent': '+1' }],
          ['link', 'image'],
          ['clean']
        ]
      }
    });

    // Update hidden form field before submit
    document.querySelector('form').addEventListener('submit', function() {
      document.getElementById('body').value = quill.root.innerHTML;
    });
  });
</script>
{% endblock javascripts %}
