{% for email in emails %}

<li>
  <a href="javascript:void(0)" class="px-4 py-3 bg-hover-light-black d-flex align-items-start chat-user {% if email.id == selected_email.id %}bg-light-subtle{% endif %}" id="chat_user_{{ email.id }}" data-user-id="{{ email.id }}" onclick="loadEmailContent({{ email.id }})">
    <div class="form-check mb-0">
      <input class="form-check-input email-checkbox" type="checkbox" value="{{ email.id }}" id="flexCheckDefault_{{ email.id }}" />
    </div>
    <div class="position-relative w-100 ms-2">
      <div class="d-flex align-items-center justify-content-between mb-2">
        <h6 class="mb-0">{% if email.sender %}{{ email.sender }}{% else %}{{ email.sender_email }}{% endif %}</h6>
        <span class="badge text-bg-primary">{{ email.subject|truncatechars:10 }}</span>
      </div>
      <h6 class="fw-semibold text-dark">
        {{ email.subject|truncatechars:40 }}
      </h6>
      <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
          <span>
            <i class="ti ti-star fs-4 me-2 {% if email.is_starred %}text-warning{% else %}text-muted{% endif %} star-icon" data-email-id="{{ email.id }}"></i>
          </span>
          <span class="d-block">
            <i class="ti ti-alert-circle {% if email.is_important %}text-info{% else %}text-muted{% endif %} important-icon" data-email-id="{{ email.id }}"></i>
          </span>
        </div>
        <p class="mb-0 fs-2 text-muted">{{ email.received_date|date:"h:i A" }}</p>
      </div>
    </div>
  </a>
</li>
{% empty %}
<li class="text-center py-5">
  <p class="text-muted">No emails found in this folder.</p>
</li>
{% endfor %}
