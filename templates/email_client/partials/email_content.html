<div class="chat-list chat active-chat" data-user-id="{{ email.id }}">
  <div class="hstack align-items-start mb-7 pb-1 align-items-center justify-content-between flex-wrap gap-6">
    <div class="d-flex align-items-center gap-2">
      <img src="/static/assets/images/profile/user-1.jpg" alt="user" width="48" height="48" class="rounded-circle" />
      <div>
        <h6 class="fw-semibold mb-0">
          {{ email.sender }}
        </h6>
        <p class="mb-0">{{ email.sender_email }}</p>
      </div>
    </div>
    <span class="badge text-bg-primary">{{ folder|title }}</span>
  </div>
  <div class="border-bottom pb-7 mb-7">
    <h4 class="fw-semibold text-dark mb-3">
      {{ email.subject }}
    </h4>
    <p class="mb-3 text-dark">To: {{ email.recipients }}</p>
    {% if email.cc %}
    <p class="mb-3 text-dark">CC: {{ email.cc }}</p>
    {% endif %}
    <p class="mb-3 text-dark">Date: {{ email.received_date }}</p>
    <div class="email-content mt-5">
      {% if email.body_html %}
      <div class="email-html-content">
        {{ email.body_html|safe }}
      </div>
      {% else %}
      <div class="email-text-content">
        <p>{{ email.body_text|linebreaks }}</p>
      </div>
      {% endif %}
    </div>
  </div>
  
  {% if email.attachments %}
  <div class="mb-3">
    <h6 class="fw-semibold mb-0 text-dark mb-3">
      Attachments
    </h6>
    <div class="d-block d-sm-flex align-items-center gap-4">
      {% for attachment in email.attachments %}
      <a href="javascript:void(0)" class="hstack gap-3 mb-2 mb-sm-0">
        <div class="d-flex align-items-center gap-3">
          <div class="rounded-1 text-bg-light p-6">
            <img src="/static/assets/images/chat/icon-adobe.svg" alt="attachment-icon" width="24" height="24" />
          </div>
          <div>
            <h6 class="fw-semibold">
              {{ attachment.filename }}
            </h6>
            <div class="d-flex align-items-center gap-3 fs-2 text-muted">
              <span>{{ attachment.size }}</span>
              <span>{{ attachment.date }}</span>
            </div>
          </div>
        </div>
      </a>
      {% endfor %}
    </div>
  </div>
  {% endif %}
  
  <div class="d-flex align-items-center gap-2 mt-5">
    <a href="javascript:void(0)" class="btn btn-primary" onclick="replyEmail({{ email.id }})">Reply</a>
    <a href="javascript:void(0)" class="btn btn-outline-primary" onclick="forwardEmail({{ email.id }})">Forward</a>
    <a href="javascript:void(0)" class="btn btn-outline-danger" onclick="deleteEmail({{ email.id }})">Delete</a>
  </div>
</div>
