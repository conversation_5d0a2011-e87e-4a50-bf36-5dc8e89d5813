{% extends "layouts/base.html" %}

{% block title %} Email Client {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}

<div class="card card-body py-3">
  <div class="row align-items-center">
    <div class="col-12">
      <div class="d-sm-flex align-items-center justify-space-between">
        <h4 class="mb-4 mb-sm-0 card-title">Email Client</h4>
        <nav aria-label="breadcrumb" class="ms-auto">
          <ol class="breadcrumb">
            <li class="breadcrumb-item d-flex align-items-center">
              <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
              </a>
            </li>
            <li class="breadcrumb-item" aria-current="page">
              <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                Email Client
              </span>
            </li>
          </ol>
        </nav>
      </div>
    </div>
  </div>
</div>

<div class="card overflow-hidden chat-application">
  <div class="d-flex align-items-center justify-content-between gap-6 m-3 d-lg-none">
    <button class="btn btn-primary d-flex" type="button" data-bs-toggle="offcanvas" data-bs-target="#chat-sidebar" aria-controls="chat-sidebar">
      <i class="ti ti-menu-2 fs-5"></i>
    </button>
    <form class="position-relative w-100">
      <input type="text" class="form-control search-chat py-2 ps-5" id="text-srh" placeholder="Search Email" />
      <i class="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
    </form>
  </div>
  <div class="d-flex w-100">
    <!-- Left sidebar -->
    <div class="left-part border-end w-20 flex-shrink-0 d-none d-lg-block">
      <div class="px-9 pt-4 pb-3">
        <a href="{% url 'compose_email' %}?account_id={{ selected_account.id }}" class="btn btn-primary fw-semibold py-8 w-100">
          Compose
        </a>
      </div>
      <ul class="list-group h-n150" data-simplebar>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'inbox' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="inbox" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-inbox fs-5"></i>Inbox
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'starred' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="starred" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-star fs-5 text-warning"></i>Starred
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'sent' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="sent" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-send fs-5"></i>Sent
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'drafts' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="drafts" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-file fs-5"></i>Drafts
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'important' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="important" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-alert-circle fs-5 text-info"></i>Important
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'trash' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="trash" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-trash fs-5"></i>Trash
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <div class="d-flex align-items-center justify-content-between mb-2 mt-4">
            <h6 class="fw-semibold mb-0 text-muted">Labels</h6>
            <a href="javascript:void(0)" class="text-dark hover-primary">
              <i class="ti ti-plus fs-4"></i>
            </a>
          </div>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'work' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="work" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-briefcase fs-5 text-success"></i>Work
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'personal' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="personal" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-user fs-5 text-primary"></i>Personal
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'health' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="health" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-heart-rate-monitor fs-5 text-success"></i>Health
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'positive' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="positive" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-thumb-up fs-5 text-success"></i>Positive
          </a>
        </li>
        <li class="list-group-item border-0 p-0 mx-9">
          <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'negative' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="negative" data-account-id="{{ selected_account.id }}">
            <i class="ti ti-bookmark fs-5 text-danger"></i>Negative
          </a>
        </li>
      </ul>
    </div>

    <div class="d-flex w-100">
      <!-- Email list -->
      <div class="min-width-340">
        <div class="border-end user-chat-box h-100">
          <div class="px-4 pt-9 pb-6 d-none d-lg-block">
            <form class="position-relative">
              <input type="text" class="form-control search-chat py-2 ps-5" id="text-srh" placeholder="Search" />
              <i class="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
            </form>
          </div>
          <div class="app-chat">
            <ul class="chat-users mh-n100" data-simplebar>
              <!-- Email list will be loaded here via AJAX -->
              <li class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Email content area (will be replaced when viewing an email) -->
      <div class="w-100">
        <div class="chat-container h-100 w-100">
          <div class="chat-box-inner-part h-100">
            <div class="chatting-box app-email-chatting-box">
              <div class="p-9 py-3 border-bottom chat-meta-user">
                <ul class="list-unstyled mb-0 d-flex align-items-center">
                  <li class="d-lg-none d-block">
                    <a class="text-dark back-btn px-2 fs-5 bg-hover-primary nav-icon-hover position-relative z-index-5" href="javascript:void(0)">
                      <i class="ti ti-arrow-left"></i>
                    </a>
                  </li>
                  <li class="ms-auto">
                    <div class="d-flex align-items-center">
                      <a href="javascript:void(0)" class="text-dark px-2 fs-5 bg-hover-primary nav-icon-hover">
                        <i class="ti ti-archive"></i>
                      </a>
                      <a href="javascript:void(0)" class="text-dark px-2 fs-5 bg-hover-primary nav-icon-hover">
                        <i class="ti ti-alert-circle"></i>
                      </a>
                      <a href="javascript:void(0)" class="text-dark px-2 fs-5 bg-hover-primary nav-icon-hover">
                        <i class="ti ti-trash"></i>
                      </a>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="position-relative overflow-hidden">
                <div class="position-relative">
                  <div class="chat-box email-box mh-n100 p-9" data-simplebar="init">
                    <!-- Email content will be loaded here via AJAX -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile sidebar -->
  <div class="offcanvas offcanvas-start user-chat-box" tabindex="-1" id="chat-sidebar" aria-labelledby="offcanvasExampleLabel">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasExampleLabel">
        Email
      </h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="px-9 pt-4 pb-3">
      <a href="{% url 'compose_email' %}?account_id={{ selected_account.id }}" class="btn btn-primary fw-semibold py-8 w-100">
        Compose
      </a>
    </div>
    <ul class="list-group h-n150" data-simplebar>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'inbox' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="inbox" data-account-id="{{ selected_account.id }}">
          <i class="ti ti-inbox fs-5"></i>Inbox
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'starred' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="starred" data-account-id="{{ selected_account.id }}">
          <i class="ti ti-star fs-5 text-warning"></i>Starred
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'sent' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="sent" data-account-id="{{ selected_account.id }}">
          <i class="ti ti-send fs-5"></i>Sent
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'drafts' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="drafts" data-account-id="{{ selected_account.id }}">
          <i class="ti ti-file fs-5"></i>Drafts
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'important' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="important" data-account-id="{{ selected_account.id }}">
          <i class="ti ti-alert-circle fs-5 text-info"></i>Important
        </a>
      </li>
      <li class="list-group-item border-0 p-0 mx-9">
        <a class="folder-link d-flex align-items-center gap-6 list-group-item-action text-dark px-3 py-8 mb-1 rounded-1 {% if folder == 'trash' %}bg-light-subtle{% endif %}" href="javascript:void(0)" data-folder="trash" data-account-id="{{ selected_account.id }}">
          <i class="ti ti-trash fs-5"></i>Trash
        </a>
      </li>
    </ul>
  </div>
</div>

{% endblock content %}

<!-- Specific JS goes HERE -->
{% block javascripts %}
<script>
  // Global variables
  let currentFolder = '{{ folder }}';
  let currentAccountId = {{ selected_account.id }};
  let currentEmailId = null;

  document.addEventListener('DOMContentLoaded', function() {
    // Initial load of emails
    loadFolderEmails(currentFolder, currentAccountId);

    // Add event listeners to folder links
    document.querySelectorAll('.folder-link').forEach(function(link) {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const folderName = this.dataset.folder;
        const accountId = this.dataset.accountId;

        // Update active folder in UI
        document.querySelectorAll('.folder-link').forEach(link => link.classList.remove('bg-light-subtle'));
        this.classList.add('bg-light-subtle');

        // Update current folder and account
        currentFolder = folderName;
        currentAccountId = accountId;

        // Load emails for the selected folder
        loadFolderEmails(folderName, accountId);
      });
    });

    // Add event delegation for star icon clicks
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('star-icon') || e.target.closest('.star-icon')) {
        e.preventDefault();
        e.stopPropagation();

        const starIcon = e.target.classList.contains('star-icon') ? e.target : e.target.closest('.star-icon');
        const emailId = starIcon.dataset.emailId;

        // Toggle star class
        starIcon.classList.toggle('text-warning');
        starIcon.classList.toggle('text-muted');

        // Send AJAX request to star/unstar email
        fetch(`/email_client/api/email/${emailId}/star/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.status !== 'success') {
            console.error('Error starring email:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
      }
    });

    // Add event delegation for important icon clicks
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('important-icon') || e.target.closest('.important-icon')) {
        e.preventDefault();
        e.stopPropagation();

        const importantIcon = e.target.classList.contains('important-icon') ? e.target : e.target.closest('.important-icon');
        const emailId = importantIcon.dataset.emailId;

        // Toggle important class
        importantIcon.classList.toggle('text-info');
        importantIcon.classList.toggle('text-muted');

        // Send AJAX request to mark email as important/not important
        fetch(`/email_client/api/email/${emailId}/important/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.status !== 'success') {
            console.error('Error marking email as important:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
      }
    });
  });

  // Function to load emails for a folder
  function loadFolderEmails(folderName, accountId, page = 1) {
    const emailListContainer = document.querySelector('.chat-users');
    emailListContainer.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

    fetch(`/email_client/api/folder/${folderName}/emails/?account_id=${accountId}&page=${page}`)
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          emailListContainer.innerHTML = data.html;

          // If no email is selected, show welcome message
          if (!currentEmailId) {
            showWelcomeMessage();
          }
        } else {
          emailListContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">${data.message}</p></div>`;
        }
      })
      .catch(error => {
        console.error('Error loading emails:', error);
        emailListContainer.innerHTML = '<div class="text-center py-5"><p class="text-danger">Error loading emails. Please try again.</p></div>';
      });
  }

  // Function to load email content
  function loadEmailContent(emailId) {
    currentEmailId = emailId;

    const emailContentContainer = document.querySelector('.email-box');
    emailContentContainer.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

    // Highlight the selected email in the list
    document.querySelectorAll('.chat-user').forEach(item => item.classList.remove('bg-light-subtle'));
    document.getElementById(`chat_user_${emailId}`).classList.add('bg-light-subtle');

    fetch(`/email_client/api/email/${emailId}/content/`)
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          emailContentContainer.innerHTML = data.html;
        } else {
          emailContentContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">${data.message}</p></div>`;
        }
      })
      .catch(error => {
        console.error('Error loading email content:', error);
        emailContentContainer.innerHTML = '<div class="text-center py-5"><p class="text-danger">Error loading email content. Please try again.</p></div>';
      });
  }

  // Function to show welcome message
  function showWelcomeMessage() {
    const emailContentContainer = document.querySelector('.email-box');
    emailContentContainer.innerHTML = `
      <div class="welcome-message text-center py-5">
        <div class="mb-4">
          <img src="/static/assets/images/logos/logo.png" alt="welcome" class="img-fluid" style="max-width: 200px;">
        </div>
        <h4 class="fw-semibold mb-3">Welcome to your Email Client</h4>
        <p class="mb-4">Select an email from the list to view its contents.</p>
        <a href="{% url 'compose_email' %}?account_id={{ selected_account.id }}" class="btn btn-primary">Compose New Email</a>
      </div>
    `;
  }

  // Function to reply to an email
  function replyEmail(emailId) {
    window.location.href = `/email_client/email/${emailId}/reply/`;
  }

  // Function to forward an email
  function forwardEmail(emailId) {
    window.location.href = `/email_client/email/${emailId}/forward/`;
  }

  // Function to delete an email
  function deleteEmail(emailId) {
    if (confirm('Are you sure you want to delete this email?')) {
      fetch(`/email_client/api/email/${emailId}/delete/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': getCookie('csrftoken'),
          'Content-Type': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          // Remove the email from the list
          document.getElementById(`chat_user_${emailId}`).remove();

          // Show welcome message if no email is selected
          showWelcomeMessage();

          // Show success message
          showNotification('Email deleted successfully', 'success');
        } else {
          showNotification(data.message, 'error');
        }
      })
      .catch(error => {
        console.error('Error deleting email:', error);
        showNotification('Error deleting email. Please try again.', 'error');
      });
    }
  }

  // Function to show notification
  function showNotification(message, type = 'info') {
    const notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) {
      const container = document.createElement('div');
      container.id = 'notification-container';
      container.style.position = 'fixed';
      container.style.top = '20px';
      container.style.right = '20px';
      container.style.zIndex = '9999';
      document.body.appendChild(container);
    }

    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
    notification.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.getElementById('notification-container').appendChild(notification);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => notification.remove(), 300);
    }, 5000);
  }

  // Helper function to get CSRF token
  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }
</script>
{% endblock javascripts %}
