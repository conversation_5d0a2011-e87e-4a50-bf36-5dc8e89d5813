        /*
         * The help info & tour.
         */
        const steps = [
            {% for support_tour_item in support_tour_items %}
            {
                content: "{{ support_tour_item.description|safe }}",
                title: "{{ support_tour_item.title }}",
                target: "{{ support_tour_item.target_tag|safe }}",
                order: "{{ support_tour_item.display_order }}",
                group: ""
            },
            {% endfor %}
        ];




        /*
         *
         */
        const actions = [
            {% for support_tour_item in support_tour_items %}
            {
                perform: {{ support_tour_item.perform_action|lower }},
                tag: "{{ support_tour_item.action_target_tag }}",
                type: {{ support_tour_item.action_type }}
            },
            {% endfor %}
        ];



        // let tourParams = {steps: steps, dialogWidth: 400, dialogMaxWidth: 400};
        let tourParams = {steps: steps};
        if (typeof tourHideBack !== 'undefined') {
            if (tourHideBack) {
                tourParams['hidePrev'] = true;
            }
        }
        const tourGuide = new tourguide.TourGuideClient(tourParams);
        tourGuide.onBeforeExit(() => {
            return new Promise((resolve, reject) => {
                if (confirm('Are you sure you want to close the tour?')) {
                    return resolve(true);
                } else {
                    return reject();
                }
            });
        });

        tourGuide.onAfterExit(()=>{
            console.info("The tour has closed");
            if (typeof tourEndClick !== 'undefined') {
                $(tourEndClick)[0].click();
            }
        })

        tourGuide.onBeforeStepChange(()=>{
            return new Promise((resolve, reject) => {
                // console.log('On Before');
                // console.log(tourGuide.activeStep);

                const step = tourGuide.activeStep + 1;
                if (actions[step]['perform']) {
                    console.log('Yes perform action');
                    console.log(actions[step]);
                    console.log(actions[step]['type']);
                    console.log(actions[step]['tag']);

                    if (actions[step]['type'] === 1) {
                        // $(actions[step]['tag']).trigger('click');
                        // $(actions[step]['tag']).click();
                        $(actions[step]['tag'])[0].click();
                        console.log($(actions[step]['tag']).html());
                    }

                    if (actions[step]['type'] === 2) {
                        $(actions[step]['tag']).tab('show');
                        console.log($(actions[step]['tag']).html());
                    }

                    if (actions[step]['type'] === 3) {
                        $(actions[step]['tag']).smartWizard("next");
                        console.log($(actions[step]['tag']).html());
                    }
                }

                return resolve(true);
            });
        });

        {# Showing the tutorial every time would be alloying, so we only show once. #}
        {% if start_tour %}
        tourGuide.start();
        {% endif %}

        /*
         * Event handler for help/info button
         */
        $('#run-tutorial').on('click', function(e) {
            tourGuide.start();
        });

        /*
         * Event handler for help/info button
         */
        $('.run-tutorial').on('click', function(e) {
            tourGuide.start();
        });