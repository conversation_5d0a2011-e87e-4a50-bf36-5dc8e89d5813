


    <script>

        var popButtons = document.querySelectorAll(".pop-loader")
        popButtons.forEach(function(popButton) {
            console.log('register...')
            window.addEventListener('beforeunload', function (e) {
                console.log('Boom2...');
                return true;
            });
        });

        function readCookie(name) {
            var nameEQ = name + "=";
            var ca = document.cookie.split(';');
            for(var i=0;i < ca.length;i++) {
                var c = ca[i];
                while (c.charAt(0)==' ') c = c.substring(1,c.length);
                if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
            }
            return null;
        }

        function popWorkingOverlay(shouldPop) {
            if(shouldPop) {
                $('#working-overlay').show();
            } else {
                $('#working-overlay').hide();
            }
        }

        /*
         * SET BUTTONS TO WORKING/NOT WORKING
         * We set a button to a spinning or 'working' effect and disable the button to
         * show the user that work is being done. Also to make sure they don't try to click again.
         * Or we set the button back to normal once the event is complete.feather icon-refresh-ccw
         */
        function setUpdatingButton(updating, buttonID, iconID, icon, keepDisabled=false) {
            const spinnerClass = "fa fa-spinner fa-pulse fa-fw";
            const buttonObj = $('#'+buttonID);
            const iconObj = $('#'+iconID);

            if (updating) {
                iconObj.removeClass(icon);
                iconObj.addClass(spinnerClass);
            } else {
                iconObj.removeClass(spinnerClass);
                iconObj.addClass(icon);
            }
            if(keepDisabled){
                buttonObj.prop("disabled", true);
            } else {
                buttonObj.prop("disabled", updating);
            }
        }

    </script>

