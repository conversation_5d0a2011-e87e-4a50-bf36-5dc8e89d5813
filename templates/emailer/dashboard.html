{% extends "base.html" %}
{% load static %}

{% block title %}Email Campaign Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">Email Campaign Dashboard</h1>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Campaigns
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_campaigns }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Campaigns
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active_campaigns }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Leads
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_leads }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Emails Sent Today
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.emails_sent_today }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-paper-plane fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Campaigns -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Campaigns</h6>
                    <a href="#" class="btn btn-primary btn-sm">Create New Campaign</a>
                </div>
                <div class="card-body">
                    {% if recent_campaigns %}
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Status</th>
                                        <th>Target Category</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for campaign in recent_campaigns %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'emailer:campaign_detail' campaign.id %}">
                                                {{ campaign.name }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-{% if campaign.status == 'active' %}success{% elif campaign.status == 'paused' %}warning{% elif campaign.status == 'draft' %}secondary{% else %}danger{% endif %}">
                                                {{ campaign.get_status_display }}
                                            </span>
                                        </td>
                                        <td>{{ campaign.target_lead_category.name }}</td>
                                        <td>{{ campaign.date_created|date:"M d, Y" }}</td>
                                        <td>
                                            <a href="{% url 'emailer:campaign_detail' campaign.id %}" class="btn btn-sm btn-outline-primary">
                                                View
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted">No campaigns created yet.</p>
                            <a href="#" class="btn btn-primary">Create Your First Campaign</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Email Account Status -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Email Account Status</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Available Accounts</span>
                            <span class="text-success font-weight-bold">{{ available_accounts }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Accounts at Limit</span>
                            <span class="text-danger font-weight-bold">{{ accounts_at_limit }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Emails Sent</span>
                            <span class="font-weight-bold">{{ stats.total_emails_sent }}</span>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-outline-primary btn-sm">View All Accounts</a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="#" class="btn btn-primary btn-sm">Create Campaign</a>
                        <a href="#" class="btn btn-outline-primary btn-sm">Manage Templates</a>
                        <a href="#" class="btn btn-outline-secondary btn-sm">View Reports</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any dashboard-specific JavaScript here
$(document).ready(function() {
    // Initialize any dashboard widgets
});
</script>
{% endblock %}
