{% extends "layouts/base.html" %}

{% block title %} Update Your Chat Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Your Prospecting Bot Sequences</h4>
                    <a href="{% url 'chat_create_bot_sequence' %}" class="ms-3">
                        <span class="badge rounded-pill  bg-secondary-subtle text-secondary">
                            Create New Bot Sequence <i class="fas fa-plus ms-2"></i>
                        </span>
                    </a>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Bots
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Your Prospecting Bot Sequences
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">

        {% if chat_bot_data %}
            
            
            
        {% else %}
            
            <div class="row">
            
                <div class="col-lg-5">
                    
                    <div class="card text-bg-primary">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-7">
                                    <div class="d-flex flex-column h-100">
                                        <div class="hstack gap-3">
                                            <a href="">
                                                <span class="d-flex align-items-center justify-content-center round-48 bg-white rounded flex-shrink-0">
                                                    <iconify-icon icon="solar:add-circle-broken" class="fs-7 text-muted"></iconify-icon>
                                                </span>
                                            </a>
                                        </div>
                                        <div class="mt-4 mt-sm-auto">
                                            <div class="row">
                                                <div class="col-12">
                                                    <h5 class="text-white fs-6 mb-0 text-nowrap">
                                                        Create Your First<br />
                                                        Prospecting Bot Sequence
                                                    </h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-5 text-center text-md-end">
                                    <img src="/static/assets/images/backgrounds/welcome-bg.png" 
                                         alt="Create Your First Prospecting Bot Sequence" 
                                         class="img-fluid mb-n7 mt-2" width="180">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            
            </div>
            
            
            
            
        {% endif %}



    </div>













    <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Delete This Chat Bot?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure that you want to delete this single Chat Bot?  This cannot be un-done.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="id" id="delete-id" value="">
                        <input type="hidden" name="action" value="delete">
                        <button type="submit" class="btn btn-danger me-2">
                            Yes, Delete This Chat Bot <i class="fas fa-times-circle mr-0 ml-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>






    <div id="addToSiteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Add Your Chat Bot To Your Site
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    
                    <div class="row">
                        
                        <div class="col-6">
                            <div class="card bg-c-blue bitcoin-wallet mb-0">
                                <div class="card-block">
                                    <h2 class="text-white mb-2 f-w-300">
                                        Wordpress
                                    </h2>
                                    <a href="button" class="btn btn-glow-light btn-light" data-bs-toggle="modal" data-bs-target="#wordpressModal">
                                        Click for Plug-in
                                    </a>
                                    <i class="fab fa-wordpress-simple f-70 text-white" style="top: 50%;"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <div class="card theme-bg bitcoin-wallet mb-0">
                                <div class="card-block">
                                    <h2 class="text-white mb-2 f-w-300">
                                        Other Types
                                    </h2>
                                    <button type="button" class="btn btn-glow-light btn-light" data-bs-toggle="modal" data-bs-target="#codeModal">
                                        Click for Code 
                                    </button>
                                    <i class="feather icon-globe f-70 text-white" style="top: 50%;"></i>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    
                </div>
            </div>
        </div>
    </div>




















    <div id="wordpressModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Your Chat Bot WordPress Plugin
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Please download and install the WordPress plug-in below.
                    </p>
                    <h2 class="text-black mb-2 f-w-300">
                        Your Chat Bot #<span id="chat-bot-id"></span>
                    </h2>



                    
                </div>
                <div class="modal-footer">
                    <!-- data-clipboard="true" data-clipboard-action="cut" data-clipboard-target="#bot-code" -->
                    <a href="/static/assets/downloads/neo-agent-ai-chat-bots_wordpress-plugin.zip" class="btn btn-primary" >
                        Download WordPress Plugin
                        <i class="feather icon-download-cloud mr-0 ml-1"></i>
                    </a>

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>





















    <div id="codeModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Your Chat Bot Code
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Please copy and paste the following code on every page where you would like your chat bot to appear.
                        Place the code just above the &lt;/body&gt; tag.
                        If unsure, please ask your tech for assistance.
                    </p>
                    
                    <textarea class="form-control" id="bot-code" rows="8"></textarea>
                </div>
                <div class="modal-footer">
                    <!-- data-clipboard="true" data-clipboard-action="cut" data-clipboard-target="#bot-code" -->
                    <button type="button" class="btn btn-primary" data-clipboard="true" data-clipboard-action="copy" data-clipboard-target="#bot-code">
                        Copy to Clipboard
                    </button>

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>













{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script src="/static/assets/vendor/apexchart/apexchart.js"></script>
<script>
</script>
{% endblock javascripts %}