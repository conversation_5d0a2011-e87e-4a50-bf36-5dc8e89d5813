{% extends "layouts/base.html" %}

{% block title %} Update Your Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
<!--<link rel="stylesheet" href="/static/assets/vendor/select2/css/select2.min.css">-->
<link rel="stylesheet" href="/static/assets/libs/select2/dist/css/select2.min.css">
<link rel="stylesheet" href="/static/assets/plugins/animate.min.css">
<link rel="stylesheet" href="/static/assets/css/perfect-scrollbar.css">
<!--<link rel="stylesheet" href="/static/assets/vendor/dropzone/dist/dropzone.css">-->
<link rel="stylesheet" href="/static/assets/libs/dropzone/dist/min/dropzone.min.css">
    <!--
<style>
    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        position: relative !important;
        top: 100% !important;
    }
    .switch input[type=checkbox] + .cr {
        height: 35px;
        width: 65px;
        top: 0;
    }
    .switch input[type=checkbox] + .cr:after {
        height: 30px;
        width: 30px;
        border-radius: 60px;
        margin-top: 2px;
    }
    .switch input[type=checkbox] + .cr:before {
        height: 35px;
        width: 65px;
        border-radius: 30px;
    }
    .switch input[type=checkbox]:checked + .cr:after {
        left: 35px;
    }
    .bitcoin-wallet i {
        top: 50%;
    }
    .form-control-color {
        height: 65px;
    }
    .vapi-btn {
        xposition: static; /* or relative, depending on your layout needs */
        xdisplay: inline-block;
        xmargin: auto;
        z-index: 999;
        xposition: relative !important;
    }
    .vapi-btn-pill.vapi-btn-is-idle > #vapi-icon-container {
        background: rgb(99, 91, 255) !important;
    }
    .select2-selection  {
        background: #fff !important;
        color: #5b5e81 !important;
        line-height: 1.5 !important;
        font-size: 1rem !important;
        border-radius: 0.625rem !important;
        padding: 0.3rem 1rem !important;
        border: 1px solid #D5DFE7 !important;
    }
    .chat-icon-send {
        color: #fff !important;
        font-size: 1rem !important;
    }
</style>-->
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">
                        Update Bot - {{ chat_bot.name }}
                    </h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Bots
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Update a Prospecting Bot
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-xl-12">
        
            <div class="row">

                <div class="col-12">
                    <!-- Basic Inputs -->
                    <div class="card">
                        <div class="card-body">
                        
                            <div class="default-tab">
    
                                <ul class="nav nav-tabs" id="myTab" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active text-uppercase" id="about-tab" data-bs-toggle="tab" href="#about" role="tab" aria-controls="about" aria-selected="true">
                                            <i class="ti ti-info-circle me-1"></i>
                                            About
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link text-uppercase" id="detail-tab" data-bs-toggle="tab" href="#detail" role="tab" aria-controls="detail" aria-selected="false">
                                            <i class="ti ti-list-details me-1"></i>
                                            Details
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link text-uppercase" id="doc-tab" data-bs-toggle="tab" href="#doc" role="tab" aria-controls="doc" aria-selected="false">
                                            <i class="ti ti-files me-1"></i>
                                            Documents
                                        </a>
                                    </li>
                                    <!--
                                    <li class="nav-item">
                                        <a class="nav-link text-uppercase" id="look-tab" data-bs-toggle="tab" href="#look" role="tab" aria-controls="look" aria-selected="false">
                                            <i class="las la-marker me-2"></i>
                                            Look & Feel
                                        </a>
                                    </li>
                                    -->
                                    <li class="nav-item">
                                        <a class="nav-link text-uppercase" id="test-tab" data-bs-toggle="tab" href="#test" role="tab" aria-controls="test" aria-selected="true">
                                            <i class="ti ti-message-chatbot me-1"></i>
                                            Test SMS
                                        </a>
                                    </li>
                                    <!--
                                    <li class="nav-item">
                                        <a class="nav-link text-uppercase" id="qa-tab" data-bs-toggle="tab" href="#qa" role="tab" aria-controls="qa" aria-selected="false">
                                            <i class="las la-question-circle me-2"></i>
                                            Q & A
                                        </a>
                                    </li>
                                    -->
                                    <li class="nav-item">
                                        <a class="nav-link text-uppercase" id="phone-tab" data-bs-toggle="tab" href="#phone" role="tab" aria-controls="phone" aria-selected="false">
                                            <i class="ti ti-phone me-1"></i>
                                            Phone #
                                        </a>
                                    </li>
                                </ul>
                                
                                <div class="tab-content" id="myTabContent">
                                
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    {#      ABOUT TAB                                            #}
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    <div class="tab-pane fade show active" id="about" role="tabpanel" aria-labelledby="about-tab">
    
                                        <div class="pt-4">
                                            <h5>About Your Bot</h5>
                                            <hr>
                                            <form method="post" class="needs-validation" novalidate>
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="update_about">
                                                <div class="row">
                                                    <div class="col-6 mb-4">
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">Bot Name:</label>
                                                            <input type="text" name="name" class="form-control" placeholder="Chatbot Name"
                                                                   value="{{ chat_bot.name }}" required>
                                                            <small>Name your bot, for your eyes only.</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 mb-4">
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">Bot Description (optional):</label>
                                                            <input type="text" name="description" class="form-control" placeholder="Chatbot Description"
                                                                   value="{% if chat_bot.description %}{{ chat_bot.description }}{% endif %}">
                                                            <small>Describe your bot, this is for your benefit only.</small>
                                                        </div>
                                                    </div>
                                                
                                                
                                                    
                                                    <!--
                                                    <div class="col-6 mb-4">
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">Primary Goal:</label>
                                                            <select class="default-select form-control form-control-lg wide" name="goal_type">
                                                                {% for goal in goals %}
                                                                <option value="{{ goal.0 }}"{% if goal.0 == chat_bot.goal_type %} selected{% endif %}>{{ goal.1 }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 mb-4">
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">Secondary Goal:</label>
                                                            <select class="default-select form-control form-control-lg wide" name="goal_type_secondary">
                                                                {% for goal in goals %}
                                                                <option value="{{ goal.0 }}"{% if goal.0 == chat_bot.goal_type_secondary %} selected{% endif %}>{{ goal.1 }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                    </div>
                                                    -->
                                                
                                                    <div class="form-repeater">
                                                        <div data-repeater-list="first_messages">
                                                            {% for message in chat_bot.first_messages.all %}
                                                            <div data-repeater-item class="row mt-2">
                                                                
                                                                <div class="col-10 mb-0">
                                                                    <div class="form-group">
                                                                        <label class="form-label mb-1">Bot Greeting:</label>
                                                                        <!--<input type="text" name="first_message" class="form-control" placeholder="Bot Greeting"
                                                                               value="{{ message.value }}" required>-->
                                                                        <textarea class="form-control" rows="4" placeholder="Bot Greeting" name="value" required>{{ message.value }}</textarea>
                                                                        
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2 mt-3 mt-md-0 d-flex flex-column justify-content-end align-bottom align-text-bottom">
                                                                    <button data-repeater-delete="" class="btn btn-lg btn-danger form-control-lg" type="button">
                                                                        <i class="ti ti-circle-x fs-5"></i>
                                                                    </button>
                                                                </div>
                                                                <small class="mt-0">This is the message spoken to someone who first calls this bot.</small>
                                                                
                                                            </div>
                                                            {% endfor %}
                                                        </div>
                                                        <button type="button" data-repeater-create="" class="btn btn-success hstack gap-6 mb-5 mt-2">
                                                            Add Bot Greeting
                                                            <i class="ti ti-circle-plus ms-1 fs-5"></i>
                                                        </button>
                                                    </div>
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                
                                                    <div class="col-6 mb-4">
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">Bot Greeting SMS:</label>
                                                            <input type="text" name="first_message_sms" class="form-control" placeholder="Bot Greeting SMS"
                                                                   value="{{ chat_bot.first_message_sms }}" required>
                                                            <small>This is the message text to someone from this bot.</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 mb-4">
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">End Call Message (optional):</label>
                                                            <input type="text" name="end_call_message" class="form-control" placeholder="End Call Message"
                                                                value="{{ chat_bot.end_call_message }}">
                                                            <small>This is the message spoken at the end of the call.</small>
                                                        </div>
                                                    </div>
                                                
                                                    <div class="col-5 mb-4">
                                                        <!--
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">Voicemail Message (optional):</label>
                                                            <input type="text" name="voicemail_message" class="form-control" placeholder="Voicemail Message"
                                                                value="{{ chat_bot.voicemail_message }}">
                                                            <small>This is the message spoken if we run into a voicemail system.</small>
                                                        </div>
                                                        -->
                                                        <label class="form-label mb-1">Voicemail Message (optional):</label>
                                                        <div class="input-group">
                                                            <div class="input-group-text">
                                                                <div class="form-check custom-checkbox">
                                                                    <input type="checkbox" class="form-check-input" name="leave_voicemail_message"{% if chat_bot.leave_voicemail_message %} checked{% endif %}>
                                                                </div>
                                                            </div>
                                                            <input type="text" class="form-control" placeholder="Voicemail Message" 
                                                                   name="voicemail_message" value="{{ chat_bot.voicemail_message }}">
                                                        </div>
                                                        <small>This is the message spoken if we run into a voicemail system. Uncheck to hang-up instead.</small>
                                                    </div>
                                                
                                                
                                                
                                                
                                                    
                                                    <div class="col-3 mb-4">
                                                        <label class="form-label mb-1">Is Active:</label>
                                                        <div class="input-group">
                                                            <div class="input-group-text">
                                                                <div class="form-check custom-checkbox">
                                                                    <input type="checkbox" class="form-check-input" name="is_active"{% if chat_bot.is_active %} checked{% endif %}>
                                                                </div>
                                                            </div>
                                                            <input type="number" step="0.01" class="form-control" placeholder="Max Daily Spend" 
                                                                   name="max_daily_spend" value="{% if chat_bot.max_daily_spend %}{{ chat_bot.max_daily_spend }}{% endif %}">
                                                        </div>
                                                        <small>Should this bot call out?  Should the bot have a daily spending limit?</small>
                                                        {% if form.max_daily_spend.errors %}
                                                        <small class="text-danger">{{ form.max_daily_spend.errors }}</small>
                                                        {% endif %}
                                                    </div>
                                                
                                                    
                                                    <div class="col-4 mb-4">
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">Forward Phone Number:</label>
                                                            <input type="text" name="forward_phone_number" class="form-control" placeholder="Forward Phone Number"
                                                                value="{% if chat_bot.forward_phone_number %}{{ chat_bot.forward_phone_number }}{% endif %}">
                                                            <small>This is the message spoken at the end of the call.</small>
                                                            {% if form.forward_phone_number.errors %}
                                                            <small class="text-danger">{{ form.forward_phone_number.errors }}</small>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                
                                                
                                                
                                                
                                                
                                                    <div class="col-12 mb-4">
                                                        <label class="form-label mb-1">When can this bot contact leads:</label>
                                                        <div class="form-group">
                                                            <div class="form-check form-check-inline">
                                                                <label class="form-check-label">
                                                                    <input type="checkbox" name="can_contact_monday" class="form-check-input"{% if chat_bot.can_contact_monday %} checked{% endif %}>
                                                                    Monday
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <label class="form-check-label">
                                                                    <input type="checkbox" name="can_contact_tuesday" class="form-check-input"{% if chat_bot.can_contact_tuesday %} checked{% endif %}>
                                                                    Tuesday
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <label class="form-check-label">
                                                                    <input type="checkbox" name="can_contact_wednesday" class="form-check-input"{% if chat_bot.can_contact_wednesday %} checked{% endif %}>
                                                                    Wednesday
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <label class="form-check-label">
                                                                    <input type="checkbox" name="can_contact_thursday" class="form-check-input"{% if chat_bot.can_contact_thursday %} checked{% endif %}>
                                                                    Thursday
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <label class="form-check-label">
                                                                    <input type="checkbox" name="can_contact_friday" class="form-check-input"{% if chat_bot.can_contact_friday %} checked{% endif %}>
                                                                    Friday
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <label class="form-check-label">
                                                                    <input type="checkbox" name="can_contact_saturday" class="form-check-input"{% if chat_bot.can_contact_saturday %} checked{% endif %}>
                                                                    Saturday
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <label class="form-check-label">
                                                                    <input type="checkbox" name="can_contact_sunday" class="form-check-input"{% if chat_bot.can_contact_sunday %} checked{% endif %}>
                                                                    Sunday
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                
                                                
                                                
                                                
                                                    <div class="col-3 mb-4">
                                                        <label class="form-label mb-1">Contact start time:</label>
                                                        <div class="input-group">
                                                            <select class="default-select form-control form-select wide" name="can_contact_start_time" required>
                                                                <option value="0">Select Start Time</option>
                                                                <option value="1"{% if chat_bot.can_contact_start_time == 1 %} selected{% endif %}>1:00 AM</option>
                                                                <option value="2"{% if chat_bot.can_contact_start_time == 2 %} selected{% endif %}>2:00 AM</option>
                                                                <option value="3"{% if chat_bot.can_contact_start_time == 3 %} selected{% endif %}>3:00 AM</option>
                                                                <option value="4"{% if chat_bot.can_contact_start_time == 4 %} selected{% endif %}>4:00 AM</option>
                                                                <option value="5"{% if chat_bot.can_contact_start_time == 5 %} selected{% endif %}>5:00 AM</option>
                                                                <option value="6"{% if chat_bot.can_contact_start_time == 6 %} selected{% endif %}>6:00 AM</option>
                                                                <option value="7"{% if chat_bot.can_contact_start_time == 7 %} selected{% endif %}>7:00 AM</option>
                                                                <option value="8"{% if chat_bot.can_contact_start_time == 8 %} selected{% endif %}>8:00 AM</option>
                                                                <option value="9"{% if chat_bot.can_contact_start_time == 9 %} selected{% endif %}>9:00 AM</option>
                                                                <option value="10"{% if chat_bot.can_contact_start_time == 10 %} selected{% endif %}>10:00 AM</option>
                                                                <option value="11"{% if chat_bot.can_contact_start_time == 11 %} selected{% endif %}>11:00 AM</option>
                                                                <option value="12"{% if chat_bot.can_contact_start_time == 12 %} selected{% endif %}>12:00 AM</option>
                                                                <option value="13"{% if chat_bot.can_contact_start_time == 13 %} selected{% endif %}>1:00 PM</option>
                                                                <option value="14"{% if chat_bot.can_contact_start_time == 14 %} selected{% endif %}>2:00 PM</option>
                                                                <option value="15"{% if chat_bot.can_contact_start_time == 15 %} selected{% endif %}>3:00 PM</option>
                                                                <option value="16"{% if chat_bot.can_contact_start_time == 16 %} selected{% endif %}>4:00 PM</option>
                                                                <option value="17"{% if chat_bot.can_contact_start_time == 17 %} selected{% endif %}>5:00 PM</option>
                                                                <option value="18"{% if chat_bot.can_contact_start_time == 18 %} selected{% endif %}>6:00 PM</option>
                                                                <option value="19"{% if chat_bot.can_contact_start_time == 19 %} selected{% endif %}>7:00 PM</option>
                                                                <option value="20"{% if chat_bot.can_contact_start_time == 20 %} selected{% endif %}>8:00 PM</option>
                                                                <option value="21"{% if chat_bot.can_contact_start_time == 21 %} selected{% endif %}>9:00 PM</option>
                                                                <option value="22"{% if chat_bot.can_contact_start_time == 22 %} selected{% endif %}>10:00 PM</option>
                                                                <option value="23"{% if chat_bot.can_contact_start_time == 23 %} selected{% endif %}>11:00 PM</option>
                                                                <option value="24"{% if chat_bot.can_contact_start_time == 24 %} selected{% endif %}>12:00 PM</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                
                                                
                                                
                                                    <div class="col-3 mb-4">
                                                        <label class="form-label mb-1">Contact end time:</label>
                                                        <div class="input-group">
                                                            <select class="default-select form-control form-select wide" name="can_contact_end_time" required>
                                                                <option value="0">Select End Time</option>
                                                                <option value="1"{% if chat_bot.can_contact_end_time == 1 %} selected{% endif %}>1:00 AM</option>
                                                                <option value="2"{% if chat_bot.can_contact_end_time == 2 %} selected{% endif %}>2:00 AM</option>
                                                                <option value="3"{% if chat_bot.can_contact_end_time == 3 %} selected{% endif %}>3:00 AM</option>
                                                                <option value="4"{% if chat_bot.can_contact_end_time == 4 %} selected{% endif %}>4:00 AM</option>
                                                                <option value="5"{% if chat_bot.can_contact_end_time == 5 %} selected{% endif %}>5:00 AM</option>
                                                                <option value="6"{% if chat_bot.can_contact_end_time == 6 %} selected{% endif %}>6:00 AM</option>
                                                                <option value="7"{% if chat_bot.can_contact_end_time == 7 %} selected{% endif %}>7:00 AM</option>
                                                                <option value="8"{% if chat_bot.can_contact_end_time == 8 %} selected{% endif %}>8:00 AM</option>
                                                                <option value="9"{% if chat_bot.can_contact_end_time == 9 %} selected{% endif %}>9:00 AM</option>
                                                                <option value="10"{% if chat_bot.can_contact_end_time == 10 %} selected{% endif %}>10:00 AM</option>
                                                                <option value="11"{% if chat_bot.can_contact_end_time == 11 %} selected{% endif %}>11:00 AM</option>
                                                                <option value="12"{% if chat_bot.can_contact_end_time == 12 %} selected{% endif %}>12:00 AM</option>
                                                                <option value="13"{% if chat_bot.can_contact_end_time == 13 %} selected{% endif %}>1:00 PM</option>
                                                                <option value="14"{% if chat_bot.can_contact_end_time == 14 %} selected{% endif %}>2:00 PM</option>
                                                                <option value="15"{% if chat_bot.can_contact_end_time == 15 %} selected{% endif %}>3:00 PM</option>
                                                                <option value="16"{% if chat_bot.can_contact_end_time == 16 %} selected{% endif %}>4:00 PM</option>
                                                                <option value="17"{% if chat_bot.can_contact_end_time == 17 %} selected{% endif %}>5:00 PM</option>
                                                                <option value="18"{% if chat_bot.can_contact_end_time == 18 %} selected{% endif %}>6:00 PM</option>
                                                                <option value="19"{% if chat_bot.can_contact_end_time == 19 %} selected{% endif %}>7:00 PM</option>
                                                                <option value="20"{% if chat_bot.can_contact_end_time == 20 %} selected{% endif %}>8:00 PM</option>
                                                                <option value="21"{% if chat_bot.can_contact_end_time == 21 %} selected{% endif %}>9:00 PM</option>
                                                                <option value="22"{% if chat_bot.can_contact_end_time == 22 %} selected{% endif %}>10:00 PM</option>
                                                                <option value="23"{% if chat_bot.can_contact_end_time == 23 %} selected{% endif %}>11:00 PM</option>
                                                                <option value="24"{% if chat_bot.can_contact_end_time == 24 %} selected{% endif %}>12:00 PM</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    
                                                
                                                
                                                
                                                    <div class="col-6 mb-4">
                                                        <div class="form-group">
                                                            <label class="form-label mb-1">Lead Categories (optional):</label>
                                                            <select class="multi-select-categories form-control wide" multiple="multiple" name="lead_categories">
                                                                {% for category in lead_categories %}
                                                                <option value="{{ category.pk }}"{% for lead_category in chat_bot.lead_categories.all %}{% if category.pk == lead_category.pk %} selected{% endif %}{% endfor %}>{{ category.name }}</option>
                                                                {% endfor %}
                                                            </select>
                                                            <small>Choose all categories this bots should contact.</small>
                                                        </div>
                                                    </div>
                                                
                                                
                                                
                                                </div>
                                                
                                                <div class="row mt-4">
                                                    <div class="col-12 text-right">
                                                        <button type="submit" class="btn btn-primary me-2"{% if chat_bot.openai_should_tune %} xdisabled{% endif %}>
                                                            Update Bot <i class="ti ti-chevron-right me-0 ms-2"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        
                                    </div>
    
    
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    {#      DETAIL TAB                                           #}
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    <div class="tab-pane fade" id="detail" role="tabpanel" aria-labelledby="detail-tab">
    
                                        <div class="pt-4">
    
                                            <h5>Chat Bot Details</h5>
                                            <hr />
    
                                            <div class="alert alert-success alert-dismissible fade show">
                                                <i class="fas fa-info-circle"></i>
                                                <strong>Important!</strong> The next input is important and will be used to train your AI bot.
                                                Please be as descriptive as possible.  Every sentence you write here will teach the AI how to sell and convert your leads.
                                                If you'd prefer to upload information files then please feel free to leave this blank.
                                            </div>
    
                                            <form method="post" class="needs-validation" novalidate>
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="update_detail">
    
                                                <div class="row mb-4">
                                                    <div class="col-6">
                                                        <div class="form-group mb-0">
                                                            <label class="form-label mb-1" for="phone_system_prompt">Phone Bot Instructions (optional)</label>
                                                            <textarea class="form-control" name="phone_system_prompt" id="phone_system_prompt" rows="10" 
                                                                      required>{{ chat_bot.phone_system_prompt }}</textarea>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group mb-0">
                                                            <label class="form-label mb-1" for="sms_system_prompt">SMS Bot Instructions (optional)</label>
                                                            <textarea class="form-control" name="sms_system_prompt" id="sms_system_prompt" rows="10" 
                                                                      required>{{ chat_bot.sms_system_prompt }}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
    
                                                <div class="alert alert-success alert-dismissible fade show">
                                                    <i class="fas fa-info-circle"></i>
                                                    The next input is important and will be used to train your AI bot.
                                                    Please be as descriptive as possible.  Every sentence you write here will teach the AI how to sell and convert your leads.
                                                    If you'd prefer to upload information files then please feel free to leave this blank.
                                                </div>
    
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group mb-0">
                                                            <label class="form-label mb-1" for="exampleTextarea">Information (optional)</label>
                                                            <textarea class="form-control" name="entity_info" id="entity_info" rows="10">{{ chat_bot.entity_info }}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            
                                                <hr />
    
                                                <div class="row mt-4">
                                                    <div class="col-12 text-right">
                                                        <button type="submit" class="btn btn-primary me-2"{% if chat_bot.openai_should_tune %} xdisabled{% endif %}>
                                                            Update Bot <i class="ti ti-chevron-right me-0 ms-2"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
    
                                        </div>
                                        
                                    </div>
    
    
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    {#      DOCUMENTS TAB                                        #}
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    <div class="tab-pane fade" id="doc" role="tabpanel" aria-labelledby="doc-tab">
                                    
                                        <div class="pt-4">
                                    
                                            <h5>Chat Bot Documents</h5>
                                            <hr />
                                        
                                            <div class="alert alert-success alert-dismissible fade show">
                                                <i class="fas fa-info-circle"></i>
                                                <strong>Optional!</strong> Upload documents that will help the AI train itself.
                                                You are limited to 5 files. Maximum filesize is 5 MB.
                                            </div>
    
                                            
                                            <div class="row">
                                                {% for file in chat_bot.training_files.all %}
                                                <!--
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="card theme-bg bitcoin-wallet">
                                                        <div class="card-block">
                                                            <button type="button" class="btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="1"></button>
                                                            <h5 class="text-white mb-2">Your File</h5>
                                                            <h2 class="text-white mb-2 f-w-300 f-30">{{ file.training_file }}</h2>
                                                            <i class="feather icon-file-text f-80 text-white"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                -->
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="widget-stat card bg-danger ">
                                                        <div class="card-body p-4">
                                                            <button type="button" class="btn-close btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="{{ file.pk }}"></button>
                                                            <div class="media">
                                                                <span class="me-3"><i class="las la-file-alt"></i></span>
                                                                <div class="media-body text-white">
                                                                    <h3 class="text-white">Your File</h3>
                                                                    <p class="mb-1 text-white mb-2">{{ file.training_file }}</p>
                                                                    <div class="progress mb-2 bg-secondary">
                                                                        <div class="progress-bar progress-animated bg-white" style="width: 100%"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                    
                                                {% endfor %}
                                                {% if chat_bot.training_file_2 %}
                                                <!--
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="card theme-bg bitcoin-wallet">
                                                        <div class="card-block">
                                                            <button type="button" class="btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="2"></button>
                                                            <h5 class="text-white mb-2">Your File</h5>
                                                            <h2 class="text-white mb-2 f-w-300 f-30">{{ chat_bot.get_filename_2 }}</h2>
                                                            <i class="feather icon-file-text f-80 text-white"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                -->
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="widget-stat card bg-danger ">
                                                        <div class="card-body p-4">
                                                            <button type="button" class="btn-close btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="2"></button>
                                                            <div class="media">
                                                                <span class="me-3"><i class="las la-file-alt"></i></span>
                                                                <div class="media-body text-white">
                                                                    <h3 class="text-white">Your File</h3>
                                                                    <p class="mb-1 text-white mb-2">{{ chat_bot.get_filename_2 }}</p>
                                                                    <div class="progress mb-2 bg-secondary">
                                                                        <div class="progress-bar progress-animated bg-white" style="width: 100%"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% if chat_bot.training_file_3 %}
                                                <!--
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="card theme-bg bitcoin-wallet">
                                                        <div class="card-block">
                                                            <button type="button" class="btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="3"></button>
                                                            <h5 class="text-white mb-2">Your File</h5>
                                                            <h2 class="text-white mb-2 f-w-300 f-30">{{ chat_bot.get_filename_3 }}</h2>
                                                            <i class="feather icon-file-text f-80 text-white"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                -->
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="widget-stat card bg-danger ">
                                                        <div class="card-body p-4">
                                                            <button type="button" class="btn-close btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="3"></button>
                                                            <div class="media">
                                                                <span class="me-3"><i class="las la-file-alt"></i></span>
                                                                <div class="media-body text-white">
                                                                    <h3 class="text-white">Your File</h3>
                                                                    <p class="mb-1 text-white mb-2">{{ chat_bot.get_filename_3 }}</p>
                                                                    <div class="progress mb-2 bg-secondary">
                                                                        <div class="progress-bar progress-animated bg-white" style="width: 100%"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% if chat_bot.training_file_4 %}
                                                <!--
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="card theme-bg bitcoin-wallet">
                                                        <div class="card-block">
                                                            <button type="button" class="btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="4"></button>
                                                            <h5 class="text-white mb-2">Your File</h5>
                                                            <h2 class="text-white mb-2 f-w-300 f-30">{{ chat_bot.get_filename_4 }}</h2>
                                                            <i class="feather icon-file-text f-80 text-white"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                -->
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="widget-stat card bg-danger ">
                                                        <div class="card-body p-4">
                                                            <button type="button" class="btn-close btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="4"></button>
                                                            <div class="media">
                                                                <span class="me-3"><i class="las la-file-alt"></i></span>
                                                                <div class="media-body text-white">
                                                                    <h3 class="text-white">Your File</h3>
                                                                    <p class="mb-1 text-white mb-2">{{ chat_bot.get_filename_4 }}</p>
                                                                    <div class="progress mb-2 bg-secondary">
                                                                        <div class="progress-bar progress-animated bg-white" style="width: 100%"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% if chat_bot.training_file_5 %}
                                                <!--
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="card theme-bg bitcoin-wallet">
                                                        <div class="card-block">
                                                            <button type="button" class="btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="5"></button>
                                                            <h5 class="text-white mb-2">Your File</h5>
                                                            <h2 class="text-white mb-2 f-w-300 f-30">{{ chat_bot.get_filename_5 }}</h2>
                                                            <i class="feather icon-file-text f-80 text-white"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                -->
                                                <div class="col-md-6 col-xl-4">
                                                    <div class="widget-stat card bg-danger ">
                                                        <div class="card-body p-4">
                                                            <button type="button" class="btn-close btn-close-white btn-delete" data-bs-toggle="modal" data-bs-target="#deleteFileModal" data-index="5"></button>
                                                            <div class="media">
                                                                <span class="me-3"><i class="las la-file-alt"></i></span>
                                                                <div class="media-body text-white">
                                                                    <h3 class="text-white">Your File</h3>
                                                                    <p class="mb-1 text-white mb-2">{{ chat_bot.get_filename_5 }}</p>
                                                                    <div class="progress mb-2 bg-secondary">
                                                                        <div class="progress-bar progress-animated bg-white" style="width: 100%"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endif %}
                                            </div>
    
                                            <div class="dropzone" id="documents-dropzone">
                                            <!--<form id="documents-dropzone" class="dropzone" action="/ajax_file_upload_handler/" enctype="multipart/form-data" method="post"
                                                acceptedFiles="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/html,text/plain">-->
                                                <div class="fallback">
                                                    <input name="file" type="file" multiple />
                                                </div>
												<svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
													<path d="M27.1666 26.6667L20.4999 20L13.8333 26.6667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
													<path d="M20.5 20V35" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
													<path d="M34.4833 30.6501C36.1088 29.7638 37.393 28.3615 38.1331 26.6644C38.8731 24.9673 39.027 23.0721 38.5703 21.2779C38.1136 19.4836 37.0724 17.8926 35.6111 16.7558C34.1497 15.619 32.3514 15.0013 30.4999 15.0001H28.3999C27.8955 13.0488 26.9552 11.2373 25.6498 9.70171C24.3445 8.16614 22.708 6.94647 20.8634 6.1344C19.0189 5.32233 17.0142 4.93899 15.0001 5.01319C12.9861 5.0874 11.015 5.61722 9.23523 6.56283C7.45541 7.50844 5.91312 8.84523 4.7243 10.4727C3.53549 12.1002 2.73108 13.9759 2.37157 15.959C2.01205 17.9421 2.10678 19.9809 2.64862 21.9222C3.19047 23.8634 4.16534 25.6565 5.49994 27.1667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
													<path d="M27.1666 26.6667L20.4999 20L13.8333 26.6667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
												</svg>
                                            <!--</form>-->
                                            </div>
                                        
                                        </div>
                                    
                                    </div>
    
    
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    {#      LOOK & FEEL TAB                                      #}
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    <div class="tab-pane fade" id="look" role="tabpanel" aria-labelledby="look-tab">
    
                                        <div class="pt-4">
                                        
                                            <form method="post">
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="update_look">
    
                                                <div class="row">
    
                                                    <div class="col-6 mb-4">
    
                                                        <h6 class="card-title mb-0">Open Button Color:</h6>
                                                        <p class="card-text">The background color of the bottom right button.</p>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" name="open_button_color"
                                                                id="open_button_color" value="#{{ chat_bot.open_button_color }}" title="Choose your color">
                                                        </div>
    
                                                    </div>
                                                    <div class="col-6 mb-4">
    
                                                        <h6 class="card-title mb-0">Open Button Icon Color:</h6>
                                                        <p class="card-text">The icon color of the bottom right button.</p>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" name="open_button_icon_color"
                                                                id="open_button_icon_color" value="#{{ chat_bot.open_button_icon_color }}" title="Choose your color">
                                                        </div>
    
                                                    </div>
                                                    <div class="col-6 mb-4">
    
                                                        <h6 class="card-title mb-0">Buttons Color:</h6>
                                                        <p class="card-text">The background color of all the form buttons in the chat window.</p>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" name="button_color"
                                                                id="button_color" value="#{{ chat_bot.button_color }}" title="Choose your color">
                                                        </div>
    
                                                    </div>
                                                    <div class="col-6 mb-4">
    
                                                        <h6 class="card-title mb-0">Buttons Text Color:</h6>
                                                        <p class="card-text">The text color of all the form buttons in the chat window.</p>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" name="button_text_color"
                                                                id="button_text_color" value="#{{ chat_bot.button_text_color }}" title="Choose your color">
                                                        </div>
    
                                                    </div>
                                                    <div class="col-6 mb-4">
    
                                                        <h6 class="card-title mb-0">Chat Background Color:</h6>
                                                        <p class="card-text">Background color of the main chat window.</p>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" name="background_color"
                                                                id="background_color" value="#{{ chat_bot.background_color }}" title="Choose your color">
                                                        </div>
    
                                                    </div>
    
                                                </div>
    
                                                <div class="row mt-4">
                                                    <div class="col-12 text-right">
                                                        <button type="submit" class="btn btn-primary me-2">
                                                            Update Bot <i class="fas fa-sync-alt mr-0 ml-2"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        
                                        
                                        </div>
                                        
                                    </div>
    
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    {#      TEST CHAT BOT TAB                                    #}
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    <div class="tab-pane fade" id="test" role="tabpanel" aria-labelledby="test-tab">
                                        
                                        
                                        
                                        

          <div class="card overflow-hidden chat-application shadow-lg mt-4">
              
              
            <div class="d-flex">
              
              <div class="w-100 w-xs-100 chat-container">
                <div class="chat-box-inner-part h-100">
                    
                  <div class="chatting-box d-block">
                      
                    <div class="d-flex parent-chat-box app-chat-right">
                      <div class="chat-box w-xs-100">
                        <div class="chat-box-inner p-9" data-simplebar style="height: 500px;">
                          <div class="chat-list chat active-chat" data-user-id="1" id="chat-scroll">
                              
                            <div class="hstack gap-3 align-items-start mb-7 justify-content-start">
                              <img src="/static/assets/images/favicon.png" alt="AI" width="40" height="40" class="rounded-circle" />
                              <div>
                                <h6 class="fs-2 text-muted">
                                  Sixth Sense AI Assistant
                                </h6>
                                <div class="p-2 text-bg-light bg-secondary-subtle rounded-1 d-inline-block text-dark fs-3">
                                  {{ chat_bot.first_message_sms }}
                                </div>
                              </div>
                            </div>
                              
                              
                          </div>
                        </div>
                        <div class="px-9 py-6 border-top chat-send-message-footer">
                          <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center gap-2 w-85">
                              <input type="text" class="form-control message-type-box text-muted border-0 rounded-0 p-0 ms-2" placeholder="Type a Message" id="chat_form" />
                            </div>
                            <ul class="list-unstyledn mb-0 align-items-center">
                              <li>
                                <a class="text-dark px-2 fs-6 bg-hover-primary nav-icon-hover z-index-5" href="javascript:void(0)" id="message-submit">
                                    Send
                                  <i class="fa fa-paper-plane xfs-7 ms-0"></i>
                                </a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                
                </div>
              </div>
            
            </div>
          </div>

          
          
          
          
          
          
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
    <!--
                                        <div class="pt-4">
                                        
                                            <div id="test-button">
                                            </div>
                                        
                                            <form method="post" id="chat-form">
                                                {% csrf_token %}
                                                <div class="chart-content">
							                        <div class="chat-box-area dlab-scroll" id="chat-scroll">
                                                        
                                                        <div class="media my-4">
                                                            <div class="dz-media">
                                                                <img src="/static/assets/images/Bot.png" alt="">
                                                            </div>
                                                            <div class="message-received w-auto">
                                                                <h5>AI</h5>
                                                                <div>
                                                                    <p class="mb-2 bg-light">
                                                                    {{ chat_bot.first_message_sms }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        
                                                    </div>
                                                    <div class="type-massage">
                                                        <div class="input-group">
                                                            <textarea class="form-control" id="chat_form" placeholder="Write your message..."></textarea>
                                                            <div class="input-group-append">
                                                                <button type="button" id="message-submit" class="btn btn-primary rounded text-white">
                                                                    Send
                                                                    <i class="fas fa-paper-plane ms-2 me-0 chat-icon-send" style="color: #fff !important; font-size: 1rem !important;"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                        
                                                            
                                                </div>
                                            </form>
    
                                        </div>
                                    -->
                                    
                                    </div>
    
    
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    {#      QUESTION & ANSWERS TAB                               #}
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    <div class="tab-pane fade" id="qa" role="tabpanel" aria-labelledby="qa-tab">
    
                                        <div class="pt-4">
                                    
                                            <div class="alert alert-primary">
                                                <div class="media align-items-center">
                                                    <i class="feather icon-alert-circle h2 mb-0"></i>
                                                    <div class="media-body ms-3 ml-2">
                                                        <b>OPTIONAL:</b>
                                                        Including frequently asked questions and their answers, as provided by your customers and users, is highly beneficial for training your chatbot.
                                                    </div>
                                                </div>
                                            </div>
    
                                            <div class="row">
                                                <div class="col-lg-4">
    
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h5>Add a Q &amp; A</h5>
                                                        </div>
                                                        <div class="card-body">
    
                                                            <form method="post">
                                                                {% csrf_token %}
                                                                <input type="hidden" name="action" value="add_qa">
                                                                <div class="form-group">
                                                                    <label class="form-label" for="question">Question:</label>
                                                                    <input type="text" name="question" class="form-control" placeholder="Question" required="">
                                                                </div>
    
                                                                <div class="form-group mb-0">
                                                                    <label class="form-label" for="answer">Answer</label>
                                                                    <textarea class="form-control" id="answer" name="answer" rows="3" required=""></textarea>
                                                                </div>
    
                                                                <button class="btn btn-primary me-2 mt-4" type="submit">
                                                                    Add Q &amp; A <i class="fas fa-arrow-alt-circle-right mr-0 ml-2"></i>
                                                                </button>
                                                            </form>
    
                                                        </div>
                                                    </div>
    
                                                </div>
                                                <div class="col-lg-8">
    
    
    
    
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h5>Your Questions &amp; Answers</h5>
                                                        </div>
                                                        <div class="card-body">
    
                                                            {% if questions %}
                                                                {% for question in questions %}
                                                            <div class="card widget-content">
                                                                <div class="card-block">
                                                                    <button type="button" class="btn-close btn-delete" data-bs-toggle="modal"
                                                                            data-bs-target="#deleteModal" data-id="{{ question.pk }}"></button>
                                                                    <div class="row">
                                                                        <div class="col-sm-12 m-b-20">
                                                                            <div class="widget-lorem">
                                                                                <div class="media align-items-center justify-content-center receive-bar">
                                                                                    <div class="me-3 photo-table">
                                                                                        <h5 class="theme-bg text-white d-flex align-items-center justify-content-center h-30px w-30px">
                                                                                            Q
                                                                                        </h5>
                                                                                    </div>
                                                                                    <div class="media-body ml-2">
                                                                                        <h4>{{ question.question }}</h4>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-sm-12 m-b-0">
                                                                            <div class="widget-lorem">
                                                                                <div class="media send-bar">
                                                                                    <div class="me-3 photo-table">
                                                                                        <h5 class="text-white d-flex theme-bg2 align-items-center justify-content-center h-30px w-30px">
                                                                                            A
                                                                                        </h5>
                                                                                    </div>
                                                                                    <div class="media-body ml-2">
                                                                                        <p>{{ question.answer }}</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                                {% endfor %}
                                                            {% else %}
                                                                    <h3>You don't have any questions & answers anymore!</h3>
                                                            {% endif %}
                                                        </div>
                                                    </div>
    
    
    
    
                                                </div>
                                            </div>
                                        
                                        </div>
                                    
                                    </div>
    
    
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    {#      PHONE NUMBER TAB                                     #}
                                    {# ######################################################### #}
                                    {# ######################################################### #}
                                    <div class="tab-pane fade" id="phone" role="tabpanel" aria-labelledby="phone-tab">
                                    
                                        <div class="pt-4">
    
                                            {% if chat_bot.phone_number and chat_bot.use_phone_number %}
                                                
                                            <h5 class="mt-0">Your Chat Bot Phone Number</h5>
                                            <hr>
                                                
                                            <div class="row">
                                                <div class="col-md-12">
                                            
                                                    <div class="widget-stat card bg-danger ">
                                                        <div class="card-body p-4">
                                                            <div class="media">
                                                                <span class="me-3">
                                                                    <i class="las la-phone-volume"></i>
                                                                </span>
                                                                <div class="media-body text-white">
                                                                    <p class="mb-1 text-white">Your Bot Phone Number</p>
                                                                    <h3 class="text-white mb-2">
                                                                        ({{ chat_bot.phone_number|slice:"2:5" }})
                                                                        {{ chat_bot.phone_number|slice:"5:8" }}-{{ chat_bot.phone_number|slice:"8:12" }}
                                                                    </h3>
                                                                    <div class="progress mb-2 bg-secondary">
                                                                        <div class="progress-bar progress-animated bg-white" style="width: 100%"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                </div>
                                            
                                            </div>
                                                
                                                
                                            {% else %}
                                                
                                            <h5 class="mt-0">Add a Phone Number to Your Chat Bot</h5>
                                            <hr>
    
                                            <form class="row">
                                                <div class="col-md-4">
                                                    <div class="card box-2 h-100 p-0">
                                                        <div class="card-header border-0 ps-1">
                                                            <h5>Search Numbers</h5>
                                                        </div>
                                                        <div class="card-body ps-0 pe-0 pt-0">
                                                            <div class="flow">
                                                                <div class="form-check mb-4">
                                                                    <input type="checkbox" class="form-check-input" id="phone-type" name="phone-type">
                                                                    <label class="form-check-label" for="phone-type">Search Toll Free Numbers</label>
                                                                </div>
        
                                                                <div class="form-group mb-4">
                                                                    <label class="form-label mb-1" for="area_code">Local Area Code <small>(optional)</small></label>
                                                                    <input class="form-control" type="number" value="" id="area-code" min="100" max="999" pattern="\d{1,3}">
                                                                </div>
                                                                
                                                                <div class="form-group">
                                                                    <button type="button" class="btn btn-primary mt-3" id="phone-search-button">
                                                                        Search Phone Numbers
                                                                        <i class="ti ti-chevron-right me-0 ms-2"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
    
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-8">
                                                    <div class="card h-100">
                                                        <div class="card-header border-0 ps-1">
                                                            <h5>Search Results</h5>
                                                        </div>
                                                        <div class="card-body ps-0 pe-0 pt-0">
                                                            <div class="row" id="phone-search-results">
    
                                                                
                                                                
                                                            </div>
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                                
                                            {% endif %}
                                            
                                        </div>
                                    
                                    </div>
                                
                                
                                    
                                </div>
                            
                            </div>
                        
                        </div>
                    </div>
                </div>
                    
                    
            </div>
       
        
        </div>
    </div>










    <div id="launchModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Re-launch Chat Bot?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Are you ready to re-launch and test your chat bot?
                        You only need to re-launch if you've added new Q&As.
                        Don't worry, you can make changes anytime.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <a href="/chat/launch_bot/{{ chat_bot.pk }}/" class="btn btn-success me-2">
                        Yes, Re-launch My Chat Bot <i class="fas fa-rocket mr-0 ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>


    <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Delete This Q & A?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure that you want to delete this single Q & A?  This cannot be un-done, however you can always re-add it.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="id" id="delete-id" value="">
                        <input type="hidden" name="action" value="delete_qa">
                        <button type="submit" class="btn btn-danger me-2">
                            Yes, Delete This Q & A <i class="fas fa-times-circle me-0 ms-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    

    <div id="deleteFileModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Delete This File?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure that you want to delete this file?  This cannot be un-done, however you can always re-upload it.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="index" id="delete-index" value="">
                        <input type="hidden" name="action" value="delete_file">
                        <button type="submit" class="btn btn-danger me-2">
                            Yes, Delete This File <i class="fas fa-times-circle me-0 ms-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    

    <div id="buyNumberModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Attach This Phone Number?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Are you sure that you want to attach the phone number <b id="phone-numberp"></b> to 
                        this bot? This can only be changed by technical support.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="phone" id="phone-number" value="">
                        <input type="hidden" name="action" value="attach_phone">
                        <button type="submit" class="btn btn-primary me-2">
                            Yes, Attach This Phone Number <i class="fas fa-arrow-alt-circle-right ms-2 me-0"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>








{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>   
    <!--
    <script src="/static/assets/vendor/select2/js/select2.full.min.js"></script>
    -->
    <script src="/static/assets/libs/select2/dist/js/select2.full.min.js"></script>
    <script src="/static/assets/libs/select2/dist/js/select2.min.js"></script>
        
    <!--<script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>-->
    <script src="/static/assets/libs/dropzone/dist/min/dropzone.min.js"></script>
        
        
    
    <script src="/static/assets/libs/jquery.repeater/jquery.repeater.min.js"></script>
    <script src="/static/assets/libs/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="/static/assets/js/forms/repeater-init.js"></script> 
        
    <script>
    
        Dropzone.autoDiscover = false;
    
        /*
        var ps = new PerfectScrollbar('#chat-scroll', {
            wheelSpeed: .5,
            swipeEasing: 0,
            suppressScrollX: !0,
            wheelPropagation: 1,
            minScrollbarLength: 40,
        });
        */
        
        
        // multi-select-categories
        $(".multi-select-categories").select2({
            placeholder: "Select lead categories",
            closeOnSelect: false,
            minimumInputLength: 0
        });
        
        /*
        $('form').on('submit', function() {
            $(':submit').prop('disabled', true);
            $(':submit i').removeClass('fa-sync-alt').removeClass('fa-times-circle').removeClass('fa-arrow-alt-circle-right').addClass('fa-spin fa-spinner');
        });
         */
    
        $('#deleteFileModal').on('show.bs.modal', function(e) {
          const index = e.relatedTarget.dataset.index;
          $('#delete-index').val(index);
        });
    
        $('#deleteModal').on('show.bs.modal', function(e) {
          const id = e.relatedTarget.dataset.id;
          $('#delete-id').val(id);
        });
    
        $('#buyNumberModal').on('show.bs.modal', function(e) {
          const number = e.relatedTarget.dataset.number;
          $('#phone-number').val(number);
          const numberp = e.relatedTarget.dataset.numberp;
          $('#phone-numberp').html(numberp);
        });
    
        let messages = $('#chat-scroll'),
        d, h, m,
        i = 0;
    
    
        $(function() {
            
            var vapiInstance = null;
            const assistant = "{{ chat_bot.vapi_assistant_id }}"; // Substitute with your assistant ID
            const apiKey = "ecde2c85-7c3a-4528-b444-09d32376f743"; // Substitute with your Public key from Vapi Dashboard.
            const buttonConfig = {
                customClass: "vapi-btn",
                position: "bottom-left", // "bottom" | "top" | "left" | "right" | "top-right" | "top-left" | "bottom-left" | "bottom-right"
                offset: "40px", // decide how far the button should be from the edge
                width: "50px", // min-width of the button
                height: "50px", // height of the button
                idle: { // button state when the call is not active.
                    color: `rgb(93, 254, 202)`, 
                    type: "pill", // or "round"
                    title: "Want to test your bot?", // only required in case of Pill
                    subtitle: "Talk with your new AI assistant", // only required in case of pill
                    icon: `https://unpkg.com/lucide-static@0.321.0/icons/phone.svg`,
                },
                loading: { // button state when the call is connecting
                    color: `rgb(93, 124, 202)`,
                    type: "pill", // or "round"
                    title: "Connecting...", // only required in case of Pill
                    subtitle: "Please wait", // only required in case of pill
                    icon: `https://unpkg.com/lucide-static@0.321.0/icons/loader-2.svg`,
                },
                active: { // button state when the call is in progress or active.
                    color: `rgb(255, 0, 0)`,
                    type: "pill", // or "round"
                    title: "Call is in progress...", // only required in case of Pill
                    subtitle: "End the call.", // only required in case of pill
                    icon: `https://unpkg.com/lucide-static@0.321.0/icons/phone-off.svg`,
                },
            };
            
            (function (d, t) {
                var g = document.createElement(t),
                  s = d.getElementsByTagName(t)[0];
                g.src =
                  "https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js";
                g.defer = true;
                g.async = true;
                s.parentNode.insertBefore(g, s);
            
                g.onload = function () {
                    vapiInstance = window.vapiSDK.run({
                        apiKey: apiKey, // mandatory
                        assistant: assistant, // mandatory
                        config: buttonConfig, // optional
                    });
                  
                    // Function calls and transcripts will be sent via messages
                    vapiInstance.on('message', (message) => {
                        console.log(message);
                    });
                
                    vapiInstance.on('error', (e) => {
                        console.error(e)
                    });
                  
                };
            })(document, "script");
            
    
            
            
            
            
            
            
            let replaceUrlsWithLinks = (text) => {
                let urlPattern = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
            
                return text.replace(urlPattern, function(url) {
                    return '<a href="' + url + '" target="_blank">' + url + '</a>';
                });
            }
            
            
            /*
             * The AI puts tokens in text where HTML should be.  Fix.  
             * We also like to make it more readable by putting headings and bolding.
             */
            let convertToHTML = (text) => {
                let isCodeTagOpen = false;
                text = text.replace(/```/g, () => {
                    isCodeTagOpen = !isCodeTagOpen;
                    return isCodeTagOpen ? '<p class="code">' : '</p>';
                });
                
                text = replaceUrlsWithLinks(text);
              
                // Match lines ending with two <br /> tags and not ending with punctuation or : or !
                const regex = /([^.:!?])<br \/><br \/>/g;
                text = text.replace(regex, '<h6>$1</h6><br /><br />');
            
                // Match lines that start with text (without punctuation) followed by a colon
                const regexStartsWithColon = /^([a-zA-Z0-9\s]+):/gm;
                text = text.replace(regexStartsWithColon, '<b>$1</b>:');
                
                const regexBoldWords = /\*\*(.*?)\*\*/g;
                text = text.replace(regexBoldWords, '<b>$1</b>');
                
                const regexItalicsWords = /\*(.*?)\*/g;
                text = text.replace(regexItalicsWords, '<i>$1</i>');
            
                // Remove all newlines
                return text.replace(/\n/g, '');
            };
            
            
            
            /*
             * Connect to AI server, and download the streamed AI response.
             */
            async function fetchData() {
                // We want to let the user know that they need to wait until this communication is over.
                $("#message-submit").prop('disabled', true);
                // Changes the send icon with a 'waiting' spinner.
                $('#message-submit i').removeClass('fa-paper-plane').addClass('fa-spin fa-spinner');
                
                // To add to each communication HTML
                let currentdate = new Date(); 
                let datetime = (currentdate.getMonth()+1) + "/" + currentdate.getDate()  + "/" + currentdate.getFullYear() +   
                        " - " + currentdate.getHours() + ":" + currentdate.getMinutes() + ":" + currentdate.getSeconds();
                
                // We don't want the user to have to delete their message after they submit.
                const userMessage = $('#chat_form').val();
                $('#chat_form').val('');
                
                
                // Create a new user chat message HTML
                insertUserMessage(userMessage);
                // let userMessageDiv = $('<div class="hstack gap-3 align-items-start mb-7 justify-content-end"> <div class="text-end"><h6 class="fs-2 text-muted">' + datetime + '</h6><div class="p-2 bg-light-info text-dark rounded-1 d-inline-block fs-3 chat-bubble">' + userMessage + '</div></div></div>');
                
                // Create a new AI chat message HTML
                // let aiMessageDiv = $('<div class="hstack gap-3 align-items-start mb-7 justify-content-start"><img src="/static/assets-portal/images/chat/Bot2.webp" alt="user8" width="40" height="40" class="rounded-circle" /> <div><h6 class="fs-2 text-muted">' + datetime + '</h6><div class="p-2 bg-light rounded-1 d-inline-block text-dark fs-3 main-chat-bubble chat-bubble"><i class="fs-5 ms-0 me-1 ti spinner-border"></i></div></div></div>');
                // let aiMessageDiv = $('<div class="row m-b-20 received-chat align-items-end" id="chats-loading"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg main-chat-bubble"><h6 class="m-b-0"><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div></h6></div></div></div>');
                // let aiMessageDiv = $('<div class="media my-4"><div class="dz-media"><img src="/static/assets/images/Bot.png" alt=""></div><div class="message-received w-auto"><h5>AI</h5><div><p class="mb-2 bg-light main-chat-bubble"><h6 id="delete-me"><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div></h6></p></div></div></div>');
                let aiMessageDiv = $('<div class="hstack gap-3 align-items-start mb-7 justify-content-start"><img src="/static/assets/images/favicon.png" alt="AI" width="40" height="40" class="rounded-circle" /><div><h6 class="fs-2 text-muted">Sixth Sense AI Assistant</h6><div class="p-2 text-bg-light bg-secondary-subtle rounded-1 d-inline-block text-dark fs-3 main-chat-bubble"><h6 id="delete-me"><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div></h6></div></div></div>');
                let mainChatBubble = aiMessageDiv.find('.main-chat-bubble');
                
                // Append the new div to the #chatwindow
                // $("#chat-scroll").append(userMessageDiv);
                $("#chat-scroll").append(aiMessageDiv);
                updateScrollbar();
                
                // Form to send to the AI server
                const formData = new FormData();
                formData.append('prompt', userMessage);
                formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
                       
                // Connect to AI server
                const url = '{% url 'chat_api_ai_chat' chat_bot.pk %}'
                const response = await fetch(url, {
                    method: 'POST', 
                    body: formData
                })
        
                // Wait and read data as it comes in, streaming the AI data so the user doesn't wait a long time.        
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullResponse = '';
                while (true) {
                    const { value, done } = await reader.read();
                    if (done) {
                        // We're done so re-enable button
                        console.log('Connection Closed');
                        $('#message-submit').prop('disabled', false);
                        $('#message-submit i').removeClass('fa-spin fa-spinner').addClass('fa-paper-plane');
                        let element = document.getElementById('delete-me');
    
                        // Check if the element exists
                        if(element) {
                            // Remove the element
                            element.remove();
                        }
                        /*let mainChatBubblePost = document.querySelector('.main-chat-bubble');
                        if(mainChatBubble) {
                        
                            // Find the child 'h6' element and remove it
                            let childH6 = mainChatBubblePost.querySelector('h6');
                            if(childH6) childH6.remove();
                            // Remove the class 'main-chat-bubble'
                            mainChatBubblePost.classList.remove('main-chat-bubble');
                        }*/
                        updateScrollbar();
                        break;
                    }
                    // Remove <i> since the HTML starts with a spinner icon to not confuse the user with a blank box.
                    //mainChatBubble.find('h6').remove();
                    /*let mainChatBubblePost = document.querySelector('.main-chat-bubble');
                    if(mainChatBubble) {
                        // Remove the class 'main-chat-bubble'
                        mainChatBubblePost.classList.remove('main-chat-bubble');
                    
                        // Find the child 'h6' element and remove it
                        let childH6 = mainChatBubblePost.querySelector('h6');
                        if(childH6) childH6.remove();
                    }*/
                    let outputText = decoder.decode(value).replace(/\n/g, '<br>');
                    console.log('outputText');
                    console.log(outputText);
                    
                    fullResponse += outputText;  // Save for later.
                    mainChatBubble.append(outputText);
                    updateScrollbar();
                }
                
                // Adding HTML like <code>, <h6>, etc. causes issues because browsers 'fix' non ended tags.  
                // So we fix the HTML after stream is done.
                fullResponse = convertToHTML(fullResponse);
                mainChatBubble.html(fullResponse);
                updateScrollbar();
            }
        
        
            
            
            async function xxxfetchData() {
              
                const url = '{% url 'chat_api_test' %}'
                const response = await fetch(url, {
                    method: 'GET'
                })
        
                // Wait and read data as it comes in, streaming the AI data so the user doesn't wait a long time.        
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullResponse = '';
                while (true) {
                    const { value, done } = await reader.read();
                    if (done) {
                        break;
                    }
                    
                    let outputText = decoder.decode(value).replace(/\n/g, '<br>');
                    console.log(outputText);
                    
                }
            }
            // fetchData();
            
            
            
            
            
            
            
            
            
            
            
            
            
    
            const csrf_token_header = {"X-CSRFToken": "{{ csrf_token }}"};
    
            $('#documents-dropzone').dropzone({
                url: "{% url 'chat_api_upload_document' chat_bot.pk %}",
                headers: csrf_token_header,
                paramName: "file", // The name that will be used to transfer the file
                maxFilesize: 200, // MB
                method: "post",
                acceptedFiles: "application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/html,text/plain",
                success: function (file, response) {
                    console.log('success');
                    console.log(response.file);
                    //$('#' + drops[i] + '-preview').attr('src', drops_pre[i] + response.file);
                    //$('#' + drops[i] + '-preview').removeClass('hide');
                },
            });
    
    
    
    
            //chat_api_phone_search
    
            $("#phone-search-button").click(function() {
                
                $("#phone-search-button").prop('disabled', true);
                $("#phone-search-button i").removeClass("ti-chevron-right").addClass("spinner-border spinner-border-sm");
    
                // Get the input values
                const areaCode = $("#area-code").val();
                const phoneType = $('#phone-type').prop('checked');
    
                $.ajax({
                    url: '{% url 'chat_api_phone_search' %}', // Replace this with your API URL
                    type: 'POST',
                    dataType: 'json',
                    data: {
                      'area_code': areaCode,
                      'phone_type': phoneType,
                      'csrfmiddlewaretoken': '{{ csrf_token }}'
                    },
                    success: function(data) {
                        // Clear the phone-search-results div
                        $("#phone-search-results").empty();
                
                        // Check the data length
                        if (data.length === 0) {
                            // Append a no results message
                            $("#phone-search-results").append('<h2>No results found</h2>');
                        } else {
                            // iterate through each item in the data array
                            $.each(data, function(i, item) {
                                // create the div string, replacing [item0] and [item1] with the array items
                                var divStr = `<div class="col-md-6 d-flex align-items-stretch">
                                  <a href="javascript:void(0)" class="card text-bg-primary text-white w-100 card-hover" data-bs-toggle="modal" data-bs-target="#buyNumberModal" 
                                    data-number="${item[0]}" data-numberp="${item[1]}">
                                    <div class="card-body">
                                      <div class="d-flex align-items-center">
                                        <i class="ti ti-phone display-6"></i>
                                        <div class="ms-auto">
                                          <i class="ti ti-circle-plus fs-8"></i>
                                        </div>
                                      </div>
                                      <div class="mt-4">
                                        <h4 class="card-title mb-1 text-white">
                                          ${item[1]}
                                        </h4>
                                        <p class="card-text fw-normal text-white opacity-75">
                                          Click to attach this number to the sequence.
                                        </p>
                                      </div>
                                    </div>
                                  </a>
                                </div>`;
                    
                                // append the new div to the phone-search-results div
                                $("#phone-search-results").append(divStr);
                            });
                        }
                    },
                    error: function() {
                        // Clear the phone-search-results div
                        $("#phone-search-results").empty();
                
                        // Append a no results message in case of error
                        $("#phone-search-results").append('<h2>No results found</h2>');
                    },
                    complete: function() {
                        $("#phone-search-button").prop('disabled', false);
                        $("#phone-search-button i").removeClass("spinner-border spinner-border-sm").addClass("ti-chevron-right");
                    }
                });
            });
    
    
    
    
    
    
    
    
    
    
    
    
    
            $("#chat-form").submit(function(e){
                e.preventDefault();
            });
    
            $('#message-submit').click(function() {
                console.log('Submit...');
                // insertMessage();
                fetchData();
            });
    
            $(window).on('keydown', function(e) {
                console.log('Key...');
                if (e.which == 13) {
                    console.log('down...');
                    // insertMessage();
                    fetchData();
                    return false;
                }
            });
        });
    
    
        function updateScrollbar() {
            $("#chat-scroll").animate({ scrollTop: $('#chat-scroll').prop("scrollHeight")}, 10);
            /*
            if ($(window).width() > 992) {
                messages.mCustomScrollbar("update").mCustomScrollbar('scrollTo', 'bottom', {
                    scrollInertia: 10,
                    timeout: 0
                });
            } else {
                $("#chat-scroll").animate({ scrollTop: $('#chat-scroll').prop("scrollHeight")}, 10);
            }
             */
        }
    
        function setDate(){
            d = new Date()
            if (m != d.getMinutes()) {
                m = d.getMinutes();
                $('<div class="timestamp">' + d.getHours() + ':' + m + '</div>').appendTo($('.message:last'));
                $('<div class="checkmark-sent-delivered">&check;</div>').appendTo($('.message:last'));
                $('<div class="checkmark-read">&check;</div>').appendTo($('.message:last'));
            }
        }
    
        function insertUserMessage(msg) {
            //$('#chat-scroll').append('<div class="row m-b-20 send-chat align-items-end"><div class="col text-end"><div class="msg"><h6 class="m-b-0 text-white">' + msg + '</h6></div></div><div class="col-auto p-l-0"><h5 class="text-white d-flex align-items-center theme-bg justify-content-center">U</h5></div></div>');
            //$('#chat-scroll').append('<div class="media mb-4 justify-content-end align-items-start"><div class="message-sent w-auto"><h5>User</h5><div><p class="mb-2">' + msg + '</p></div></div><div class="dz-media ms-2"><img src="/static/assets/images/profile/default.png" alt=""></div></div>');
            $('#chat-scroll').append('<div class="hstack gap-3 align-items-start mb-7 justify-content-end"><div class="text-end"><h6 class="fs-2 text-muted">User</h6><div class="p-2 bg-info-subtle text-dark rounded-1 d-inline-block fs-3">' + msg + '</div></div></div>');
            // setDate();
            updateScrollbar();
        }
    
        function startAdvocateMessage() {
            $('<div class="row m-b-20 received-chat align-items-end" id="chats-loading"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center  theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0"><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div></h6></div></div></div>').appendTo($('#chat-scroll'));
    
            updateScrollbar();
        }
    
        function insertAdminMessage(msg) {
            $('#chats-loading').remove();
            $('#chat-scroll').append('<div class="row m-b-20 received-chat align-items-end"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">S</h5></div><div class="col"><div class="msg"><h6 class="m-b-0">' + msg + '</h6></div></div></div>');
            updateScrollbar();
        }
    
        function insertAdvocateMessage(msg, pause, msg2) {
            if (pause) {
                setTimeout(function () {
                    $('#chats-loading').remove();
                    $('#chat-scroll').append('<div class="row m-b-20 received-chat align-items-end"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0">' + msg + '</h6></div></div></div>');
    
                    if (msg2 != 'null') {
                        $('#chat-scroll').append('<div class="row m-b-20 received-chat align-items-end"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0">' + msg2 + '</h6></div></div></div>');
                    }
    
                    updateScrollbar();
                }, 1000 + (Math.random() * 20) * 100);
            }
            else
            {
                $('#chats-loading').remove();
                $('#chat-scroll').append('<div class="row m-b-20 received-chat align-items-end"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0">' + msg + '</h6></div></div></div>');
                updateScrollbar();
            }
            i++;
        }
    
    
        function insertMessage() {
            msg = $('#chat_form').val();
            if ($.trim(msg) == '') {
                return false;
            }
    
            var formData = {message:msg, chat_bot_id:'{{ chat_bot.pk }}'};
            insertUserMessage(msg);
            $('#chat_form').val(null);
            startAdvocateMessage();
    
            $.ajax({
                url : "/chat/message/",
                type: "POST",
                data : formData,
                success: function(data, textStatus, jqXHR)
                {
                    console.log(data)
                    insertAdvocateMessage(data.message, true, data.message2);
                },
                error: function (jqXHR, textStatus, errorThrown)
                {
                    insertAdminMessage('There was a problem sending your message to your advocate. Please try again in a moment.');
                },
                complete: function(data)
                {
                }
            });
        }
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    </script>
{% endblock javascripts %}
