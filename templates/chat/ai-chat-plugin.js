/*!
 * self_learningai-ai-chat-plugin
 * Copyright 2023 - Intellic Labs
 * https://intelliclabs.com/
 * https://selflearningai.chat/
 */
'use strict';

window.addEventListener("load", function(){
    // =======================================================
    // =========    Self Learning AI Chat Plugin code    ============
    // =======================================================
    let wrapperDiv = document.createElement("div");
    wrapperDiv.id = "saic-chat-wrapper";
    document.body.appendChild(wrapperDiv);

    let link = document.createElement('link');
    link.setAttribute('rel', 'stylesheet');
    //link.setAttribute('href', 'https://selflearningai.chat/chat/bot_css/' + saicChatID + '/');
    link.setAttribute('href', '/chat/bot_css/' + saicChatID + '/');
    document.head.appendChild(link);

    // setTimeout(function(){
    console.log('Test 456')
    let request = new XMLHttpRequest();
    //request.open('GET', 'https://selflearningai.chat/chat/start_chat_session/' + saicChatID + '/')
    request.open('GET', '/chat/start_chat_session/' + saicChatID + '/')
    request.send();
    request.onload = async () => {
        console.log(request.response);
        document.getElementById("saic-chat-wrapper").innerHTML = request.response;

        document.getElementById("saic-chat-btn").onclick = function() {
            let element = document.getElementById("saic-chat");
            let icon = document.getElementById("saic-chat-btn-icon");
            console.log('element.style.visibility');
            console.log(element.style.visibility);
            if (element.style.visibility == "hidden") {
                element.style.visibility = "visible";
                icon.className = "fas fa-times";

                let saicChat = document.getElementById("saic-chat-messages");
                saicChat.scrollTop = saicChat.scrollHeight - saicChat.clientHeight;
            } else {
                element.style.visibility = "hidden";
                icon.className = "fas fa-comment";
            }
        };

        document.getElementById("saic-chat-start-btn").onclick = function() {
            console.log("Blamo");
            let email_value = document.getElementById("saic-chat-input-email").value;
            if (email_value.length < 5) {
                alert("Please enter in your email address.");
                return;
            }
            let post_data = {"email": email_value};
            console.log('post_data');
            console.log(post_data);

            let name_input = document.getElementById("saic-chat-input-name");
            if(typeof(name_input) != 'undefined' && name_input != null) {
                let name_value = name_input.value;
                if (name_input.hasAttribute('required')) {
                    if (name_value.length < 3) {
                        alert("Please enter in your name.");
                        return;
                    }
                    post_data["name"] = name_value;
                }
            }
            console.log('post_data 2');
            console.log(post_data);

            let phone_input = document.getElementById("saic-chat-input-phone");
            if(typeof(phone_input) != 'undefined' && phone_input != null) {
                let phone_value = phone_input.value;
                if (phone_input.hasAttribute('required')) {
                    if (phone_value.length < 8) {
                        alert("Please enter in your phone number.");
                        return;
                    }
                    post_data["phone"] = phone_value;
                }
            }
            console.log('post_data 3');
            console.log(post_data);
            request.open('POST', 'https://selflearningai.chat/chat/api/add_lead/' + saicChatID + '/')
            request.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
            request.send(JSON.stringify(post_data));
            request.onload = () => {
                console.log('Add lead done');
                console.log(request.response);
                let div = document.getElementById("saic-chat-form");
                div.parentNode.removeChild(div);

                document.getElementById("saic-chat-input-box").style.visibility='';

                let chat_div = document.getElementById('saic-chat-messages');
                chat_div.innerHTML += '<div class="message stark">Hello, how may I help you?</div>';
            };
        };

        document.getElementById("saic-chat-send-btn").onclick = function() {
            console.log('Clickity clack...');
            sendChat();
        };

        document.getElementById("saic-chat-send-input").addEventListener("keydown", function (e) {
            if (e.code === "Enter") {  //checks whether the pressed key is "Enter"
                console.log('Enter...');
                sendChat();
            }
        });

        async function sendChat() {
            let message = document.getElementById("saic-chat-send-input").value;
            document.getElementById("saic-chat-send-input").value = "";
            if (message.length < 1) {
                return;
            }

            $("#saic-chat-send-btn").prop('disabled', true);
            // Changes the send icon with a 'waiting' spinner.
            $('#saic-chat-send-btn i').removeClass('fa-paper-plane').addClass('fa-spin fa-spinner');

            addChatToWindow(message);
            addWaitingToWindow();

            // Form to send to the AI server
            const formData = new FormData();
            formData.append('prompt', message);

            // Connect to AI server
            const url = 'api/ai_chat/' + saicChatID + '/';
            const response = await fetch(url, {
                method: 'POST',
                body: formData
            })

            // Wait and read data as it comes in, streaming the AI data so the user doesn't wait a long time.
            let mainChatBubble = $('#saic-chat-messages').find('.main-chat-bubble');
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullResponse = '';
            while (true) {
                const { value, done } = await reader.read();
                if (done) {
                    // We're done so re-enable button
                    console.log('Connection Closed');
                    $('#saic-chat-send-btn').prop('disabled', false);
                    $('#saic-chat-send-btn i').removeClass('fa-spin fa-spinner').addClass('fa-paper-plane');
                    mainChatBubble.removeClass('main-chat-bubble');
                    break;
                }
                // Remove <i> since the HTML starts with a spinner icon to not confuse the user with a blank box.
                mainChatBubble.find('typing').remove();
                let outputText = decoder.decode(value).replace(/\n/g, '<br>');

                fullResponse += outputText;  // Save for later.
                mainChatBubble.append(outputText);
                updateScrollbar();
            }




            /*
            let post_data = {"message": message, "chat_bot_id": saicChatID};
            let request = new XMLHttpRequest();
            request.open('POST', 'https://selflearningai.chat/chat/api/message/');
            request.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
            request.send(JSON.stringify(post_data));
            request.onload = () => {
                if (request.readyState === 4) {
                    if (request.status === 200) {
                        console.log(request.responseText);
                        var response = JSON.parse(request.responseText);
                        console.log(response.message);
                        addResponseToWindow(response.message, response.message2);
                        console.log(response.message2);
                    } else {
                        addErrorToWindow('There was an error contacting the server. Please try again later.');
                        console.error(request.statusText);
                    }
                }
            };
            */
        }

        function addWaitingToWindow() {
            let chat_div = document.getElementById('saic-chat-messages');
            chat_div.innerHTML += '<div id="saic-chat-working" class="message stark main-chat-bubble"><div class="typing typing-1"></div><div class="typing typing-2"></div><div class="typing typing-3"></div></div>';
            scrollToTop();
        }
        function addResponseToWindow(message, message2) {
            setTimeout(function(){
                document.getElementById("saic-chat-working").remove();
                let chat_div = document.getElementById('saic-chat-messages');
                chat_div.innerHTML += '<div class="message stark">' + message + '</div>';

                if (message2 != 'null') {
                    chat_div.innerHTML += '<div class="message stark">' + message2 + '</div>';
                }

                scrollToTop();
            }, 1000);
        }
        function addChatToWindow(message) {
            let chat_div = document.getElementById('saic-chat-messages');
            chat_div.innerHTML += '<div class="message parker">' + message + '</div>';
            scrollToTop();
        }
        function addErrorToWindow(message) {
            document.getElementById("saic-chat-working").remove();
            let chat_div = document.getElementById('saic-chat-messages');
            chat_div.innerHTML += '<div class="message stark error">Error: ' + message + '</div>';
            scrollToTop();
        }
        function scrollToTop() {
            var saic_chat = document.getElementById("saic-chat-messages");
            saic_chat.scrollTop = saic_chat.scrollHeight - saic_chat.clientHeight;
        }
    }
    // ===========================================================
    // =========    End Self Learning AI Chat Plugin code    ============
    // ===========================================================
});
