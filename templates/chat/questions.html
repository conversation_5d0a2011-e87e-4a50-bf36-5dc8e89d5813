{% extends "layouts/base.html" %}

{% block title %} Bot Questions {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}








    <div class="row">
        <div class="col-xl-12">
            
            
            <div class="row">
                <div class="col-xl-12">
                    <div class="page-titles mb-3">
                        <div class="d-flex align-items-center">
                            <h2 class="heading">Bot Questions & Answers</h2>
                        </div>
                    </div>
                </div>
            </div>





            <div class="row">
                <div class="col-12">
                                
                                
                    <div class="alert alert-success alert-dismissible fade show">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="btn-close"></button>
                        <i class="fas fa-info-circle"></i>
                        Use this form to add as many questions and answers about your company, brand business, or entity.
                        The more questions with answers you add, the more accurate your bot will be.
                    </div>
                                
                </div>
            </div>


            {% if form.errors %}

                <div class="row">
                    <div class="col-12">
                                    
                        <div class="alert alert-danger alert-dismissible fade show">
                            <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="me-2"><polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>
                            <strong>Error!</strong> Please fix the errors below.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="btn-close"></button>
                        </div>
                                    
                    </div>
                </div>

            {% endif %}


            <div class="row">





                <div class="col-sm-4">
                    <!-- Basic Inputs -->
                    <div class="card">
                                     
                        <div class="card-header border-0 pb-0">
                            <h4 class="card-title">Add Question & Answer</h4>
                        </div>
                                    
                        <div class="card-body">


                            <form method="post">
                                {% csrf_token %}
                                <div class="form-group input-group-lg mb-4">
                                    <label class="form-label mb-1" for="question">Question:</label>
                                    <input type="text" name="question" class="form-control form-control-lg" placeholder="Question" required>
                                </div>
                                
                                <div class="form-group mb-0">
                                    <label class="form-label mb-1" for="answer">Answer</label>
                                    <textarea class="form-control" id="answer" name="answer" rows="4" placeholder="Answer" required></textarea>
                                </div>

                                <button class="btn btn-primary me-2 mt-4" type="submit">
                                    Add Q & A 
                                    <i class="fas fa-arrow-alt-circle-right me-0 ms-2"></i>
                                </button>
                            </form>





                        </div>
                        <div class="card-footer">
                            <button class="btn btn-warning me-2 mt-2" data-bs-toggle="modal" data-bs-target="#launchModal">
                                Launch Chat Bot <i class="fas fa-rocket me-0 ms-2"></i>
                            </button>
                            <!--
                            <button type="reset" class="btn btn-light">Reset</button>
                            -->
                        </div>
                    </div>
                </div>



                <!-- [ form-element ] start -->
                <div class="col-sm-8">
                    <!-- Basic Inputs -->
                    <div class="card">
                                
                                   
                        <div class="card-header border-0 pb-0">
                            <h4 class="card-title">Bot Questions & Answers</h4>
                        </div>
                                
                        <div class="card-body">








                            {% if questions %}
                                <div class="row">
                                {% for question in questions %}
                                    
                                    
                                    
                                <div class="col-12">    
                                    <div class="card">
                                        <div class="">
                                            <div class="prot-blog">
                                                <button type="button" class="btn-close btn-delete" data-bs-toggle="modal"
                                                        data-bs-target="#deleteModal" data-id="{{ question.pk }}"></button>
                                                
                                                <p class="d-flex fs-20 mb-4 {% if question.question|length > 50 %}align-items-start{% else %}align-items-center{% endif %}">
                                                    <i class="fs-30 fa-solid fas fa-question-circle me-2"></i>
                                                    {{ question.question }}
                                                </p>
                                                
                                                <p class="d-flex mb-0 {% if question.answer|length > 100 %}align-items-start{% else %}align-items-center{% endif %}">
                                                    <i class="fs-30 fa-solid fas fa-info-circle me-2"></i>
                                                    {{ question.answer }}
                                                </p>
                                                
                                                <div class="shape">
                                                    <svg width="488" height="353" viewBox="0 0 488 353" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <mask id="mask0_51_1209" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="438" height="283">
                                                        <rect width="438" height="283" fill="url(#paint0_linear_51_1209)"></rect>
                                                        </mask>
                                                        <g mask="url(#mask0_51_1209)">
                                                        <path d="M165 410.5H15L465.5 88H487.5L165 410.5Z" fill="#ccecff"></path>
                                                        <path d="M264 369.5H114L564.5 47H586.5L264 369.5Z" fill="#ccecff"></path>
                                                        </g>
                                                        <defs>
                                                        <linearGradient id="paint0_linear_51_1209" x1="308.075" y1="-143.042" x2="316.634" y2="468.334" gradientUnits="userSpaceOnUse">
                                                        <stop offset="0" stop-color="#363B64"></stop>
                                                        <stop offset="1" stop-color="#4CBC9A"></stop>
                                                        </linearGradient>
                                                        </defs>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                    
                                    
                                    
                                    
                                {% endfor %}
                                </div>
                            {% else %}
                                <h3>You don't have any questions & answers yet!</h3>
                            {% endif %}

                        </div>

                    </div>
                </div>
                <!-- [ form-element ] end -->
                        
            </div>
            <!-- [ Main Content ] end -->
        </div>
    </div>









    <div id="launchModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        Launch Bot?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you ready to launch and test your bot?  Don't worry, you can make changes anytime.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <a href="{% url 'chat_update_bot' chat_bot.pk %}" class="btn btn-success me-2" id="launch-button">
                        Yes, Launch My Bot <i class="fas fa-rocket me-0 ms-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>


    <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Delete This Q & A?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure that you want to delete this single Q & A?  This cannot be un-done, however you can always re-add it.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="id" id="delete-id" value="">
                        <input type="hidden" name="action" value="delete">
                        <button type="submit" class="btn btn-danger me-2">
                            Yes, Delete This Q & A 
                            <i class="fas fa-times-circle me-0 ms-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>











{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script>
    $(document).ready(function() {
        
        $('form').on('submit', function() {
            $(':submit').prop('disabled', true);
            $(':submit i').removeClass('fa-times-circle').removeClass('fa-arrow-alt-circle-right').addClass('fa-spin fa-spinner');
        });

        $('#launch-button').on('click', function(e) {
            $(this).addClass('disabled');
            $('i', this).removeClass('fa-launch').addClass('fa-spin fa-spinner');
        });
    
        $('#deleteModal').on('show.bs.modal', function(e) {
          let id = e.relatedTarget.dataset.id;
          $('#delete-id').val(id);
        });
        
        
        
    });
</script>
{% endblock javascripts %}
