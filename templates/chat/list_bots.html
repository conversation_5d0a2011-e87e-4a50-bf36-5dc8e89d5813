{% extends "layouts/base.html" %}

{% block title %} Update Your Chat Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">Your Prospecting Bots</h4>
                    <a href="{% url 'chat_create_bot' %}" class="ms-3">
                        <span class="badge rounded-pill  bg-secondary-subtle text-secondary">
                            Create New Bot <i class="fas fa-plus ms-2"></i>
                        </span>
                    </a>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Bots
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Your Prospecting Bots
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- [ Main Content ] start -->
    <div class="row">

        {% if chat_bot_data %}
            {% for chat_bot in chat_bot_data %}
                <div class="col-xl-6 col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">
                                {{ chat_bot.chat_bot_name }} Bot
                            </h4>
                            
                        </div>
                        <div class="card-body pb-0">
                            <div class="row mb-1">
                                <div class="col-4 text-center">
                                    <div class="d-grid">
                                        <a href="{% url 'chat_update_bot' chat_bot.chat_bot_id %}"
                                           class="btn btn-primary text-uppercase w-100">
                                            Edit Bot <i class="fas fa-edit me-0 ms-2"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="col-4 text-center">
                                    <div class="d-grid">
                                        <a href="/chat/view_leads/{{ chat_bot.chat_bot_id }}/"
                                           class="btn text-uppercase w-100 border btn-warning">
                                            Leads <i class="fas fa-users me-0 ms-2"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="col-4 text-center">
                                    <div class="d-grid">
                                        <a href="{% url 'chat_update_bot' chat_bot.chat_bot_id %}"
                                           class="btn btn-dark text-uppercase w-100">
                                            Test Bot <i class="fas fa-comment-dots me-0 ms-2"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div id="bar-chart{{ chat_bot.chat_bot_id }}"
                                        class="bar-chart{{ chat_bot.chat_bot_id }}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer mt-2">
                            <div class="row pb-0">
                                <div class="col-md-4 col-4 text-center m-b-15">
                                    <h4 class="f-w-300">Total Chats</h4>
                                    <span>{{ chat_bot.total_chats }}</span>
                                </div>
                                <div class="col-md-4 col-4 text-center m-b-15">
                                    <h4 class="f-w-300">Forwarded</h4>
                                    <span>{{ chat_bot.total_forwarded }}</span>
                                </div>
                                <div class="col-md-4 col-4 text-center m-b-15">
                                    <h4 class="f-w-300">Appointments</h4>
                                    <span>{{ chat_bot.total_scheduled }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
            
        {% else %}
            
            <div class="row">
            
                <div class="col-lg-5">
                    
                    <div class="card text-bg-primary">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-7">
                                    <div class="d-flex flex-column h-100">
                                        <div class="hstack gap-3">
                                            <a href="{% url 'chat_create_bot' %}">
                                                <span class="d-flex align-items-center justify-content-center round-48 bg-white rounded flex-shrink-0">
                                                    <iconify-icon icon="solar:add-circle-broken" class="fs-7 text-muted"></iconify-icon>
                                                </span>
                                            </a>
                                        </div>
                                        <div class="mt-4 mt-sm-auto">
                                            <div class="row">
                                                <div class="col-12">
                                                    <h5 class="text-white fs-6 mb-0 text-nowrap">
                                                        Create Your First<br />
                                                        Prospecting Bot
                                                    </h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-5 text-center text-md-end">
                                    <img src="/static/assets/images/backgrounds/welcome-bg.png" 
                                         alt="Create Your First Prospecting Bot Sequence" 
                                         class="img-fluid mb-n7 mt-2" width="180">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            
            </div>
        {% endif %}
    
    </div>




    
    <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Delete This Chat Bot?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure that you want to delete this single Chat Bot?  This cannot be un-done.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="id" id="delete-id" value="">
                        <input type="hidden" name="action" value="delete">
                        <button type="submit" class="btn btn-danger me-2">
                            Yes, Delete This Chat Bot <i class="fas fa-times-circle mr-0 ml-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>






    <div id="addToSiteModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Add Your Chat Bot To Your Site
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    
                    <div class="row">
                        
                        <div class="col-6">
                            <div class="card bg-c-blue bitcoin-wallet mb-0">
                                <div class="card-block">
                                    <h2 class="text-white mb-2 f-w-300">
                                        Wordpress
                                    </h2>
                                    <a href="button" class="btn btn-glow-light btn-light" data-bs-toggle="modal" data-bs-target="#wordpressModal">
                                        Click for Plug-in
                                    </a>
                                    <i class="fab fa-wordpress-simple f-70 text-white" style="top: 50%;"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6">
                            <div class="card theme-bg bitcoin-wallet mb-0">
                                <div class="card-block">
                                    <h2 class="text-white mb-2 f-w-300">
                                        Other Types
                                    </h2>
                                    <button type="button" class="btn btn-glow-light btn-light" data-bs-toggle="modal" data-bs-target="#codeModal">
                                        Click for Code 
                                    </button>
                                    <i class="feather icon-globe f-70 text-white" style="top: 50%;"></i>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    
                </div>
            </div>
        </div>
    </div>




















    <div id="wordpressModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Your Chat Bot WordPress Plugin
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Please download and install the WordPress plug-in below.
                    </p>
                    <h2 class="text-black mb-2 f-w-300">
                        Your Chat Bot #<span id="chat-bot-id"></span>
                    </h2>



                    
                </div>
                <div class="modal-footer">
                    <!-- data-clipboard="true" data-clipboard-action="cut" data-clipboard-target="#bot-code" -->
                    <a href="/static/assets/downloads/neo-agent-ai-chat-bots_wordpress-plugin.zip" class="btn btn-primary" >
                        Download WordPress Plugin
                        <i class="feather icon-download-cloud mr-0 ml-1"></i>
                    </a>

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>





















    <div id="codeModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Your Chat Bot Code
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Please copy and paste the following code on every page where you would like your chat bot to appear.
                        Place the code just above the &lt;/body&gt; tag.
                        If unsure, please ask your tech for assistance.
                    </p>
                    
                    <textarea class="form-control" id="bot-code" rows="8"></textarea>
                </div>
                <div class="modal-footer">
                    <!-- data-clipboard="true" data-clipboard-action="cut" data-clipboard-target="#bot-code" -->
                    <button type="button" class="btn btn-primary" data-clipboard="true" data-clipboard-action="copy" data-clipboard-target="#bot-code">
                        Copy to Clipboard
                    </button>

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>













{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script src="/static/assets/vendor/apexchart/apexchart.js"></script>
<script>
let chatBotID = 0;

$('#addToSiteModal').on('show.bs.modal', function(e) {
    chatBotID = e.relatedTarget.dataset.id;
});

$('#codeModal').on('show.bs.modal', function(e) {
    var id = e.relatedTarget.dataset.id;
    $('#modal-id-number').text(chatBotID);

    let botCode = `<!-- NEO Agent Code -->\n<script>\nconst saicChatID = "` + chatBotID + `";\n&lt;/script>\n&lt;script src=\"https://neo-agent.tech/static/assets/js/self-learning-ai-chat-plugin.js\">&lt;/script>\n<!-- End NEO Agent Code -->`;
    botCode = botCode.replace(/&lt;/g, "<");
    
    $('#bot-code').val(botCode);

});
    
$('#wordpressModal').on('show.bs.modal', function(e) {
    var id = e.relatedTarget.dataset.id;
    $('#chat-bot-id').text(chatBotID);
});



$('#deleteModal').on('show.bs.modal', function(e) {
    var id = e.relatedTarget.dataset.id;
    $('#delete-id').val(id);
});
document.addEventListener("DOMContentLoaded", function () {
    setTimeout(function () {
        floatchart()
    }, 700);
});

function floatchart() {
    (function () {
        {% for chat_bot in chat_bot_data %}
        var options{{ chat_bot.chat_bot_id }} = {
            chart: {
                type: 'bar',
                height: 255,
                toolbar: {
                    show: false,
                }
            },
            series: [{
                name: '# of Chats',
                data: {{ chat_bot.past_week_chat_count|safe }}
            }, {
                name: '# of Actions',
                data: {{ chat_bot.past_week_action_count|safe }}
            }],
            colors: ['#1de9b6', '#a389d4'],
            fill: {
                type: 'gradient',
                opacity: 1,
                gradient: {
                    shade: 'dark',
                    type: 'vertical',
                    gradientToColors: ['#1dc4e9', '#899ed4'],
                    stops: [0, 100]
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '45%',
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: {{ chat_bot.past_week_dates|safe }},
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + " total"
                    }
                }
            }
        };
        var chart{{ chat_bot.chat_bot_id }} = new ApexCharts(document.querySelector("#bar-chart{{ chat_bot.chat_bot_id }}"), options{{ chat_bot.chat_bot_id }});
        chart{{ chat_bot.chat_bot_id }}.render();
        {% endfor %}
    })();
}
</script>
{% endblock javascripts %}