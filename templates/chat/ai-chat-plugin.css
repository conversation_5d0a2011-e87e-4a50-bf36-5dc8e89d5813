/*!
 * self-learningai-ai-chat-css
 * Copyright 2023 - Intellic Labs
 * https://intelliclabs.com/
 * https://selflearningai.chat/
 */
@import url("https://fonts.googleapis.com/css?family=Red+Hat+Display:400,500,900&display=swap");
@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.9.0/css/all.min.css");

.saic-main {
  font-family: Red hat Display, sans-serif;
  font-weight: 400;
  line-height: 1.25em;
  letter-spacing: 0.025em;
  color: #333;
  font-size: 14px;
}

.saic-location {
  position: absolute;
  right: 70px;
  bottom: 70px;
}

.saic-chat-btn {
    position: fixed;
    visibility: visible;
    bottom: 10px;
    right: 10px;
    left: auto;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgb(0 0 0 / 6%), 0 3px 32px rgb(0 0 0 / 16%);
    transition: all 0.4s;
    background-color: #{{ chat_bot.open_button_color }};
	color: #{{ chat_bot.open_button_icon_color }};
    z-index: 999995;
}

.saic-chat-btn i {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
	font-size: 1.8em;
    box-shadow: 0 5px 10px 0 rgb(0 0 0 / 20%);
}

.saic-chat-pic {
  width: 4rem;
  height: 4rem;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
}

.saic-chat-contact {
  position: relative;
  margin-bottom: 1rem;
  padding-left: 5rem;
  height: 4.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.saic-chat-contact .saic-chat-pic {
  position: absolute;
  left: 0;
}
.saic-chat-contact .name {
  font-weight: 500;
  margin-bottom: 0.125rem;
}
.saic-chat-contact .message, .contact .seen {
  font-size: 0.9rem;
  color: #999;
}
.saic-chat-contact .message, .contact .seen a {
    color: #999;
}
.saic-chat-contact .badge {
  box-sizing: border-box;
  position: absolute;
  width: 1.5rem;
  height: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
  padding-top: 0.125rem;
  border-radius: 1rem;
  top: 0;
  left: 2.5rem;
  background: #333;
  color: white;
}

.saic-chat {
  /* visibility: hidden; */
  /* position: relative; */
  position: fixed;
  bottom: 70px;
  right: 20px;
  display: flex;
  flex-direction: column;
  /* justify-content: space-between; */
  /* width: 22rem; */
  /* height: 35rem; */
  /* width: 350px;
  height: 400px; */
  width: 400px;
  height: 550px;
  z-index: 2;
  box-sizing: border-box;
  border-radius: 1rem;
  background: #{{ chat_bot.background_color }};
  box-shadow: 0 0 8rem 0 rgba(0, 0, 0, 0.1), 0rem 2rem 4rem -3rem rgba(0, 0, 0, 0.5);
}

.saic-chat .messages input {
  border: none;
  background-image: none;
  background-color: white;
  padding: 0.5rem 1rem;
  margin-right: 1rem;
  border-radius: 1.125rem;
  flex-grow: 2;
  box-shadow: 0 0 1rem rgba(0, 0, 0, 0.1), 0rem 1rem 1rem -1rem rgba(0, 0, 0, 0.2);
  font-family: Red hat Display, sans-serif;
  font-weight: 400;
  letter-spacing: 0.025em;
}
.saic-chat .input input:placeholder {
  color: #999;
}

.saic-chat .saic-chat-contact.bar {
  flex-basis: 3.5rem;
  flex-shrink: 0;
  margin: 1rem;
  box-sizing: border-box;
}
.saic-chat .messages {
  /* min-height: 28.5em; */
  height: 100%;
  padding: 1rem;
  background: #F7F7F7;
  flex-shrink: 2;
  overflow-y: auto;
  box-shadow: inset 0 2rem 2rem -2rem rgba(0, 0, 0, 0.05), inset 0 -2rem 2rem -2rem rgba(0, 0, 0, 0.05);
}
.saic-chat .messages .time {
  font-size: 0.8rem;
  background: #EEE;
  padding: 0.25rem 1rem;
  border-radius: 2rem;
  color: #999;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0 auto;
}
.saic-chat .messages .message {
  box-sizing: border-box;
  padding: 0.5rem 1rem;
  margin: 1rem;
  background: #FFF;
  border-radius: 1.125rem 1.125rem 1.125rem 0;
  min-height: 2.25rem;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 66%;
  box-shadow: 0 0 2rem rgba(0, 0, 0, 0.075), 0rem 1rem 1rem -1rem rgba(0, 0, 0, 0.1);
}
.saic-chat .messages .message .error {
  color: red;
  font-weight: bolder;
}
.saic-chat .messages .message.parker {
  margin: 1rem 1rem 1rem auto;
  border-radius: 1.125rem 1.125rem 0 1.125rem;
  background: #333;
  color: white;
}
.saic-chat .messages .message .typing {
  display: inline-block;
  width: 0.8rem;
  height: 0.8rem;
  margin-right: 0rem;
  box-sizing: border-box;
  background: #ccc;
  border-radius: 50%;
}
.saic-chat .messages .message .typing.typing-1 {
  -webkit-animation: typing 3s infinite;
          animation: typing 3s infinite;
}
.saic-chat .messages .message .typing.typing-2 {
  -webkit-animation: typing 3s 250ms infinite;
          animation: typing 3s 250ms infinite;
}
.saic-chat .messages .message .typing.typing-3 {
  -webkit-animation: typing 3s 500ms infinite;
          animation: typing 3s 500ms infinite;
}
.saic-chat .input {
  box-sizing: border-box;
  flex-basis: 4rem;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding: 0 0.5rem 0 0.6rem;
}
.saic-chat .input i {
  font-size: 1.5rem;
  /*margin-right: 1rem;*/
  color: #666;
  cursor: pointer;
  transition: color 200ms;
}
.saic-chat .input i:hover {
  color: #333;
}
.saic-chat .input input {
  border: none;
  background-image: none;
  background-color: white;
  padding: 0.5rem 1rem;
  margin-right: 0.5rem;
  border-radius: 1.125rem;
  flex-grow: 2;
  box-shadow: 0 0 1rem rgba(0, 0, 0, 0.1), 0rem 1rem 1rem -1rem rgba(0, 0, 0, 0.2);
  font-family: Red hat Display, sans-serif;
  font-weight: 400;
  letter-spacing: 0.025em;
}
.saic-chat .input input:placeholder {
  color: #999;
}

.saic-chat-form {
  border-radius: 0.25rem!important;
  border: 1px solid #6c757d!important;
  padding: 0 1em 1em 1em;
  margin-bottom: 1em;
}

.saic-chat-form p {
  margin: 1em 0 1em 0;
}

.saic-chat-form button {
  color: #{{ chat_bot.button_text_color }};
  background-color: #{{ chat_bot.button_color }};
  border-color: #{{ chat_bot.button_color }};
  border-radius: 30px;
  padding: 10px 20px;
  font-size: 14px;
  margin-bottom: 5px;
  margin-right: 10px;
  transition: all .3s ease-in-out;
  width: 100%;
}

.saic-chat-form-group {
  margin-bottom: 1em;
}

.saic-chat-form-group input {
  background: #f4f4f4;
  height: 50px;
  width: 100%;
}

@-webkit-keyframes typing {
  0%, 75%, 100% {
    transform: translate(0, 0.25rem) scale(0.9);
    opacity: 0.5;
  }
  25% {
    transform: translate(0, -0.25rem) scale(1);
    opacity: 1;
  }
}

@keyframes typing {
  0%, 75%, 100% {
    transform: translate(0, 0.25rem) scale(0.9);
    opacity: 0.5;
  }
  25% {
    transform: translate(0, -0.25rem) scale(1);
    opacity: 1;
  }
}

.saic-chat-pic.Alexander {
  background-image: url("/static/assets/images/avatars/Alexander.jpg");
  display: block !important;
}

.saic-chat-pic.Carol {
  background-image: url("/static/assets/images/avatars/Carol.jpg");
  display: block !important;
}

.saic-chat-pic.Charles {
  background-image: url("/static/assets/images/avatars/Charles.jpg");
  display: block !important;
}

.saic-chat-pic.Denise {
  background-image: url("/static/assets/images/avatars/Denise.jpg");
  display: block !important;
}

.saic-chat-pic.Heather {
  background-image: url("/static/assets/images/avatars/Heather.jpg");
  display: block !important;
}

.saic-chat-pic.Joseph {
  background-image: url("/static/assets/images/avatars/Joseph.jpg");
  display: block !important;
}

.saic-chat-pic.Melissa {
  background-image: url("/static/assets/images/avatars/Melissa.jpg");
  display: block !important;
}

#saic-chat-send-btn {
  border: none;
  width: 60px;
  border-radius: 50px;
  font-size: 1.5rem;
  /* margin-right: 1rem; */
  color: #666;
  cursor: pointer;
  transition: color 200ms;
}
#saic-chat-send-btn:hover {
  color: #333;
}