{% extends "layouts/base.html" %}

{% block title %} Test Your Chat Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}










		<div class="pcoded-content">
			<div class="pcoded-inner-content">
				<!-- [ breadcrumb ] start -->
				<div class="page-header">
					<div class="page-block">
						<div class="row align-items-center">
							<div class="col-md-12">
								<div class="page-header-title">
									<h5 class="m-b-10">Message</h5>
								</div>
								<ul class="breadcrumb">
									<li class="breadcrumb-item"><a href="/"><i
												class="feather icon-home"></i></a></li>
									<li class="breadcrumb-item"><a href="#!">Social</a></li>
									<li class="breadcrumb-item"><a href="#!">Message</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
				<!-- [ breadcrumb ] end -->
				<div class="main-body">
					<div class="page-wrapper">
						<!-- [ Main Content ] start -->
						<div class="row">
							<!-- [ message ] start -->
							<div class="col-sm-12">
								<div class="card msg-card mb-0">
									<div class="card-body msg-block">
										<div class="row">
											<div class="col-lg-3 col-md-12">
												<div class="message-mobile">
													<div class="task-right-header-status"
														data-bs-target=".taskboard-right-progress"
														data-bs-toggle="collapse">
														<span class="f-w-400">Friend
															List</span>
														<i class="fas fa-times float-end m-t-10"></i>
													</div>
													<div class="taskboard-right-progress">
														<div class="h-list-header">
															<div class="input-group">
																<input type="text" id="msg-friends" class="form-control"
																	placeholder="Search Friend . . .">
																<span class="input-group-text"><i
																		class='feather icon-search'></i></span>
															</div>
														</div>
														<div class="h-list-body">
															<div class="msg-user-list scroll-div">
																<div class="main-friend-list">
																	<div class="media userlist-box " data-id="1"
																		data-status="online"
																		data-username="Josephin Doe">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Alexander.jpg"
																				alt="Generic placeholder image ">
																			<div class="live-status">3</div>
																		</a>
																		<div class="media-body">
																			<h6 class="chat-header">Josephin Doe<small
																					class="d-block text-c-green">Typing
																					. . </small></h6>
																		</div>
																	</div>
																	<div class="media userlist-box active " data-id="2"
																		data-status="online" data-username="Lary Doe">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Heather.jpg"
																				alt="Generic placeholder image">
																			<div class="live-status">1</div>
																		</a>
																		<div class="media-body">
																			<h6 class="chat-header">Lary Doe<small
																					class="d-block text-c-green">online</small>
																			</h6>
																		</div>
																	</div>
																	<div class="media userlist-box " data-id="3"
																		data-status="online" data-username="Alice">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Charles.jpg"
																				alt="Generic placeholder image"></a>
																		<div class="media-body">
																			<h6 class="chat-header">Alice<small
																					class="d-block text-c-green">online</small>
																			</h6>
																		</div>
																	</div>
																	<div class="media userlist-box " data-id="4"
																		data-status="offline" data-username="Alia">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Alexander.jpg"
																				alt="Generic placeholder image">
																			<div class="live-status">1</div>
																		</a>
																		<div class="media-body">
																			<h6 class="chat-header">Alia<small
																					class="d-block text-muted">10 min
																					ago</small></h6>
																		</div>
																	</div>
																	<div class="media userlist-box " data-id="5"
																		data-status="offline" data-username="Suzen">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Heather.jpg"
																				alt="Generic placeholder image"></a>
																		<div class="media-body">
																			<h6 class="chat-header">Suzen<small
																					class="d-block text-muted">15 min
																					ago</small></h6>
																		</div>
																	</div>
																	<div class="media userlist-box " data-id="1"
																		data-status="online"
																		data-username="Josephin Doe">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Charles.jpg"
																				alt="Generic placeholder image "></a>
																		<div class="media-body">
																			<h6 class="chat-header">Josephin Doe<small
																					class="d-block text-muted">10 min
																					ago</small></h6>
																		</div>
																	</div>
																	<div class="media userlist-box " data-id="2"
																		data-status="online" data-username="Lary Doe">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Alexander.jpg"
																				alt="Generic placeholder image"></a>
																		<div class="media-body">
																			<h6 class="chat-header">Lary Doe<small
																					class="d-block text-c-green">online</small>
																			</h6>
																		</div>
																	</div>
																	<div class="media userlist-box " data-id="3"
																		data-status="online" data-username="Alice">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Heather.jpg"
																				alt="Generic placeholder image"></a>
																		<div class="media-body">
																			<h6 class="chat-header">Alice<small
																					class="d-block text-c-green">online</small>
																			</h6>
																		</div>
																	</div>
																	<div class="media userlist-box " data-id="2"
																		data-status="online" data-username="Lary Doe">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Alexander.jpg"
																				alt="Generic placeholder image"></a>
																		<div class="media-body">
																			<h6 class="chat-header">Lary Doe<small
																					class="d-block text-c-green">online</small>
																			</h6>
																		</div>
																	</div>
																	<div class="media userlist-box " data-id="3"
																		data-status="online" data-username="Alice">
																		<a class="media-left" href="#!"><img
																				class="media-object img-radius"
																				src="/static/assets/images/avatars/Heather.jpg"
																				alt="Generic placeholder image"></a>
																		<div class="media-body">
																			<h6 class="chat-header">Alice<small
																					class="d-block text-c-green">online</small>
																			</h6>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-lg-9 col-md-12">
												<div class="ch-block">
													<div class="h-list-body">
														<div class="msg-user-chat scroll-div">
															<div class="main-friend-chat">
																<div class="media chat-messages">
																	<a class="media-left photo-table" href="#!"><img
																			class="media-object img-radius img-radius m-t-5"
																			src="/static/assets/images/avatars/Alexander.jpg"
																			alt="Generic placeholder image"></a>
																	<div class="media-body chat-menu-content">
																		<div class="">
																			<p class="chat-cont">hello Datta! Will you
																				tell me something</p>
																			<p class="chat-cont">about yourself?</p>
																		</div>
																		<p class="chat-time">8:20 a.m.</p>
																	</div>
																</div>
																<div class="media chat-messages">
																	<div class="media-body chat-menu-reply">
																		<div class="">
																			<p class="chat-cont">Ohh! very nice</p>
																		</div>
																		<p class="chat-time">8:22 a.m.</p>
																	</div>
																</div>
																<div class="media chat-messages">
																	<a class="media-left photo-table" href="#!"><img
																			class="media-object img-radius img-radius m-t-5"
																			src="/static/assets/images/avatars/Alexander.jpg"
																			alt="Generic placeholder image"></a>
																	<div class="media-body chat-menu-content">
																		<div class="">
																			<p class="chat-cont">can you help me?</p>
																		</div>
																		<p class="chat-time">8:20 a.m.</p>
																	</div>
																</div>
															</div>
														</div>
													</div>
													<hr>
													<div class="msg-form">
														<div class="input-group mb-0">
															<input type="text" class="form-control msg-send-chat"
																placeholder="Text . . .">
															<input type="file" id="imgupload" style="display:none" />
															<button id="OpenImgUpload"
																class="btn btn-secondary btn-icon" type="button"
																data-bs-toggle="tooltip" title="file attachment"><i
																	class="feather icon-paperclip"></i></button>
															<button class="btn btn-theme btn-icon btn-msg-send"
																type="button"><i class="feather icon-play"></i></button>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<!-- [ message ] end -->
						</div>
						<!-- [ Main Content ] end -->
					</div>
				</div>
			</div>
		</div>

























{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<link rel="stylesheet" href="/static/assets/css/perfect-scrollbar.css">
<script src="/static/assets/js/perfect-scrollbar.min.js"></script>

<script type="text/javascript">
window.addEventListener('load', function () {
    var ps = new PerfectScrollbar('.msg-user-list.scroll-div', {
        wheelSpeed: .5,
        swipeEasing: 0,
        suppressScrollX: !0,
        wheelPropagation: 1,
        minScrollbarLength: 40,
    });
    var ps = new PerfectScrollbar('.msg-user-chat.scroll-div', {
        wheelSpeed: .5,
        swipeEasing: 0,
        suppressScrollX: !0,
        wheelPropagation: 1,
        minScrollbarLength: 40,
    });
    document.querySelector(".task-right-header-status").addEventListener("click", function (e) {
        slideToggle(document.querySelector(".taskboard-right-progress"), 200);
    });
    var tc = document.querySelectorAll(".message-mobile .media");
    for (var t = 0; t < tc.length; t++) {
        var c = tc[t];
        c.addEventListener("click", function (e) {
            console.log('Clicked...');
            var vw = window.innerWidth;
            if (vw < 992) {
                slideUp(document.querySelector(".taskboard-right-progress"), 200);
                document.querySelector(".msg-block").classList.add('dis-chat');
            }
        });
    }
});
</script>

{% endblock javascripts %}
