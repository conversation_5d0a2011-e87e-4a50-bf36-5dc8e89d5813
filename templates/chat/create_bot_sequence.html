{% extends "layouts/base.html" %}

{% block title %} Create Your Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}




    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">
                        Create a Telemarketing Bot Sequence
                    </h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Bots
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Create a Prospecting Bot Sequence
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        <div class="col-xl-12">
        
            <form method="post" class="needs-validation" novalidate>
                {% csrf_token %}
            
                {% if form.errors %}
                    
                    <div class="alert alert-danger alert-dismissible fade show">
                        <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="me-2"><polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>
                        <strong>Error!</strong> Please fix the errors below.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="btn-close">
                        </button>
                    </div>
                    
                {% endif %}

                <div class="row">

                    <!-- [ form-element ] start -->
                    <div class="col-sm-8">
                        <!-- Basic Inputs -->
                        <div class="card">

                            <div class="card-body">
                                    
                                <div class="row">
                                        
                                    <div class="mb-4 col-md-12">
                                        <label class="form-label mb-1" for="name">Bot Sequence Name:</label>
                                        <input type="text" name="name" class="form-control form-control-lg" placeholder="Bot Name" required>
                                        <small>Name your bot, for your eyes only.</small>
                                        {% if form.name.errors %}
                                            <small class="text-danger">{{ form.name.errors }}</small>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mb-3 col-md-12">
                                        <label class="form-label mb-1" for="description">Bot Sequence Description (optional):</label>
                                        <input type="text" name="description" class="form-control form-control-lg" placeholder="Bot Description" maxlength="250">
                                        <small>Describe your bot, this is for your benefit only.</small>
                                    </div>
                                
                                    <div class="mb-4 col-md-6">
                                        <label class="form-label mb-1">Primary Goal (optional):</label>
                                        <select class="default-select form-control form-control-lg wide" name="goal_type">
                                            {% for goal in goals %}
                                            <option value="{{ goal.0 }}"{% if goal.0 == 1 %} selected{% endif %}>{{ goal.1 }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                
                                
                                    <!--
                                    <div class="mb-4 col-md-6">
                                        <label class="form-label mb-1">Secondary Goal:</label>
                                        <select class="default-select form-control form-control-lg wide" name="goal_type_secondary">
                                            {% for goal in goals %}
                                            <option value="{{ goal.0 }}">{{ goal.1 }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    -->
                                
                                    <div class="mb-0 col-md-12">
                                        <hr class="my-1" />
                                    </div>

                                </div>



                            </div>
                            <div class="card-footer pt-0">
                                <button class="btn btn-primary me-2" type="submit">
                                    Create Bot Sequence & Continue to Update Section 
                                    <i class="ti ti-chevron-right me-0 ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- [ form-element ] end -->


                </div>
            </form>
  
        
        
        
        
        
        
        
        </div>
    </div>





















{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/libs/jquery.repeater/jquery.repeater.min.js"></script>
    <script src="/static/assets/libs/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="/static/assets/js/forms/repeater-init.js"></script>
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>
    <script>
        $(document).ready(function() {
            /*
            $('form').on('submit', function() {
                $(':submit').prop('disabled', true);
                $(':submit i').removeClass('fa-arrow-alt-circle-right').addClass('fa-spin fa-spinner');
            });
             */
            
            $("#sample-phone-bot-instruction").click(function() {
                let text = `{{ sample_phone_bot_instruction|safe }}`;
                $("#phone_system_prompt").val(text);
            });
            
            $("#sample-sms-bot-instruction").click(function() {
                let text = `{{ sample_sms_bot_instruction|safe }}`;
                $("#sms_system_prompt").val(text);
            });
            
            
        });
    </script>
{% endblock javascripts %}
