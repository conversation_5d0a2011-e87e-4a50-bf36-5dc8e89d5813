{% extends "layouts/base.html" %}

{% block title %} <PERSON><PERSON> Leads {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css"/>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.0/css/buttons.dataTables.min.css"/>
<style>
.btn-primary-dt {
    border-color: var(--primary) !important;
    background-color: var(--primary) !important;
    color: #FFFFFF !important;
}
.btn-dt {
    padding: 0.688rem 1.5rem !important;
    border-radius: 0.5rem !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
}
.btn-sm-dt {
    font-size: 0.813rem !important;
    padding: 0.579rem 1rem !important;
}
table.dataTable thead .sorting_asc:after {
    visibility: hidden !important;
}
table.dataTable thead .sorting:after {
    content: "" !important;
}
</style>
{% endblock stylesheets %}

{% block content %}
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">
                        Your Bot <strong>'{{ chat_bot.name }}'</strong> Leads
                    </h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Bots
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Your Bot Leads
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
                            
        <div class="col-xl-12">
            <div class="card">
                <div class="card-body xtable-border-style">
                    <div class="table-responsive">
                        
                        <table class="display" id="prospect-leads">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Phone</th>
                                    <th>Success</th>
                                    <th>Forwarded</th>
                                    <th>Appointment</th>
                                    <th>Duration</th>
                                    <th>Cost</th>
                                    <th data-type="date" data-format="MMM DD, YY, hh:mm a">Date Contacted</th>
                                </tr>
                            </thead>
                            <tbody>
                            {% if leads %}
                            {% for lead in leads %}
                                {{ lead }}
                                <tr>
                                    <td>
                                        {% if lead.lead.pk %}
                                        <a href="{% url 'leads_view_lead' lead.lead.pk %}">
                                            {{ lead.lead.first_name }} {{ lead.lead.last_name }}
                                        </a>
                                        {% endif %}
                                    </td>
                                    <td>{{ lead.get_contact_type_display }}</td>
                                    <td>
                                        {% if lead.lead.phone_cell %}
                                        ({{ lead.lead.phone_cell|slice:"0:3" }})
                                        {{ lead.lead.phone_cell|slice:"3:6" }}-{{ lead.lead.phone_cell|slice:"6:10" }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if lead.success_evaluation %}
                                        <span class="badge badge-sm light badge-success">
                                            <i class="fa fa-circle text-success me-1"></i>
                                            Success
                                        </span>
                                        {% else %}
                                        <span class="badge badge-sm light badge-danger">
                                            <i class="fa fa-circle text-danger me-1"></i>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if lead.forwarded_call %}
                                        <span class="badge badge-sm light badge-success">
                                            <i class="fa fa-circle text-success me-1"></i>
                                            Yes
                                        </span>
                                        {% else %}
                                        <span class="badge badge-sm light badge-danger">
                                            <i class="fa fa-circle text-danger me-1"></i>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if lead.scheduled_appointment %}
                                        <span class="badge badge-sm light badge-success">
                                            <i class="fa fa-circle text-success me-1"></i>
                                            Yes
                                        </span>
                                        {% else %}
                                        <span class="badge badge-sm light badge-danger">
                                            <i class="fa fa-circle text-danger me-1"></i>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ lead.duration_seconds }} seconds</td>
                                    <td>${{ lead.cost }}</td>
                                    <td>{{ lead.date_contacted_start|date:"M d, y, g:i a" }}</td>
                                </tr>
                            {% endfor %}
                            {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
    </div>



{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script type="text/javascript" charset="utf8" src="//cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" charset="utf8" src="//cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" charset="utf8" src="//cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min.js"></script>
<script type="text/javascript" charset="utf8" src="//cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.12/pdfmake.min.js" integrity="sha512-axXaF5grZBaYl7qiM6OMHgsgVXdSLxqq0w7F4CQxuFyrcPmn0JfnqsOtYHUun80g6mRRdvJDrTCyL8LQqBOt/Q==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script> 
    $('#prospect-leads').DataTable({
        language: {
            paginate: {
                previous: '<<', 
                next: '>>' 
            }
        },
        dom: 'lBfrtip',
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
        buttons: [{
            extend: 'csvHtml5',
            className: 'btn-dt btn-primary-dt btn-sm-dt ms-5'
        },
        { 
            extend: 'excelHtml5', 
            className: 'btn-dt btn-primary-dt btn-sm-dt' 
        },
        { 
            extend: 'pdfHtml5', 
            className: 'btn-dt btn-primary-dt btn-sm-dt' 
        },
        { 
            extend: 'copyHtml5', 
            className: 'btn-dt btn-primary-dt btn-sm-dt' 
        }],
    });
</script>
{% endblock javascripts %}