{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} Dashboard {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}


          <div class="card overflow-hidden chat-application">
          
            <div class="d-flex">
              
              <div class="w-100 w-xs-100 chat-container">
                <div class="chat-box-inner-part h-100">
                    
                  <div class="chatting-box d-block">
                      
                    <!--
                    <div class="p-9 border-bottom chat-meta-user d-flex align-items-center justify-content-between">
                      <div class="hstack gap-3 current-chat-user-name">
                        <div class="position-relative">
                          <img src="/static/assets/images/favicon.png" alt="user1" width="48" height="48" class="rounded-circle" />
                          <span class="position-absolute bottom-0 end-0 p-1 badge rounded-pill bg-success">
                            <span class="visually-hidden">New alerts</span>
                          </span>
                        </div>
                        <div>
                          <h6 class="mb-1 name fw-semibold">Test 123</h6>
                          <p class="mb-0">Away</p>
                        </div>
                      </div>
                      <ul class="list-unstyled mb-0 d-flex align-items-center">
                        <li>
                          <a class="text-dark px-2 fs-7 bg-hover-primary nav-icon-hover position-relative z-index-5" href="javascript:void(0)">
                            <i class="ti ti-phone"></i>
                          </a>
                        </li>
                        <li>
                          <a class="text-dark px-2 fs-7 bg-hover-primary nav-icon-hover position-relative z-index-5" href="javascript:void(0)">
                            <i class="ti ti-video"></i>
                          </a>
                        </li>
                        <li>
                          <a class="chat-menu text-dark px-2 fs-7 bg-hover-primary nav-icon-hover position-relative z-index-5" href="javascript:void(0)">
                            <i class="ti ti-menu-2"></i>
                          </a>
                        </li>
                      </ul>
                    </div>
                    -->
                      
                    <div class="d-flex parent-chat-box app-chat-right">
                      <div class="chat-box w-xs-100">
                        <div class="chat-box-inner p-9" data-simplebar>
                          <div class="chat-list chat active-chat" data-user-id="1">
                              
                            <div class="hstack gap-3 align-items-start mb-7 justify-content-start">
                              <img src="/static/assets/images/favicon.png" alt="user8" width="40" height="40" class="rounded-circle" />
                              <div>
                                <h6 class="fs-2 text-muted">
                                  Andrew, 2 hours ago
                                </h6>
                                <div class="p-2 text-bg-light bg-secondary-subtle rounded-1 d-inline-block text-dark fs-3">
                                  If I don’t like something, I’ll stay away
                                  from it.
                                </div>
                              </div>
                            </div>
                            <div class="hstack gap-3 align-items-start mb-7 justify-content-end">
                              <div class="text-end">
                                <h6 class="fs-2 text-muted">2 hours ago</h6>
                                <div class="p-2 bg-info-subtle text-dark rounded-1 d-inline-block fs-3">
                                  If I don’t like something, I’ll stay away
                                  from it.
                                </div>
                              </div>
                            </div>
                              
                          </div>
                        </div>
                        <div class="px-9 py-6 border-top chat-send-message-footer">
                          <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center gap-2 w-85">
                              <input type="text" class="form-control message-type-box text-muted border-0 rounded-0 p-0 ms-2" placeholder="Type a Message" fdprocessedid="0p3op" />
                            </div>
                            <ul class="list-unstyledn mb-0 align-items-center">
                              <li>
                                <a class="text-dark px-2 fs-6 bg-hover-primary nav-icon-hover z-index-5" href="javascript:void(0)">
                                    Send
                                  <i class="ti ti-send xfs-7 ms-0"></i>
                                </a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            
            </div>
          </div>

          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/js/apps/chat.js"></script>
{% endblock javascripts %}        