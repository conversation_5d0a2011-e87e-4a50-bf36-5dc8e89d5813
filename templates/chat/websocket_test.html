<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test - Voicebot</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .panel {
            flex: 1;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
        }
        .log-panel {
            height: 400px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin-bottom: 15px;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .sent {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .received {
            background-color: #f6ffed;
            border-left: 3px solid #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border-left: 3px solid #ff4d4f;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #096dd9;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .disconnected {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
        }
        .timestamp {
            color: #888;
            font-size: 0.8em;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>WebSocket Test - Voicebot</h1>
    
    <div class="status disconnected" id="connection-status">
        Disconnected
    </div>
    
    <div class="container">
        <div class="panel">
            <h2>Connection</h2>
            <div class="form-group">
                <label for="ws-url">WebSocket URL:</label>
                <input type="text" id="ws-url" value="wss://silo.intelliclabs.com/ws/voicebot/websocket/" />
            </div>
            <button id="connect-btn">Connect</button>
            <button id="disconnect-btn" disabled>Disconnect</button>
            
            <h2>Send Message</h2>
            <div class="form-group">
                <label for="message-type">Message Type:</label>
                <select id="message-type">
                    <option value="start">Start</option>
                    <option value="speech">Speech</option>
                    <option value="dtmf">DTMF</option>
                    <option value="mark">Mark</option>
                </select>
            </div>
            
            <div id="speech-input" class="form-group" style="display: none;">
                <label for="transcript">Transcript:</label>
                <textarea id="transcript">Hello, how are you today?</textarea>
            </div>
            
            <div id="dtmf-input" class="form-group" style="display: none;">
                <label for="digits">Digits:</label>
                <input type="text" id="digits" value="1" />
            </div>
            
            <div class="form-group">
                <label for="call-sid">Call SID:</label>
                <input type="text" id="call-sid" value="" placeholder="Auto-generated for 'start', required for others" />
            </div>
            
            <button id="send-btn" disabled>Send Message</button>
            
            <h2>Raw JSON</h2>
            <div class="form-group">
                <textarea id="raw-json" placeholder="Edit JSON directly if needed"></textarea>
            </div>
            <button id="send-raw-btn" disabled>Send Raw JSON</button>
        </div>
        
        <div class="panel">
            <h2>Message Log</h2>
            <div class="log-panel" id="log-panel"></div>
            <button id="clear-log-btn">Clear Log</button>
        </div>
    </div>
    
    <script>
        let socket = null;
        let currentCallSid = null;
        
        // DOM Elements
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const sendBtn = document.getElementById('send-btn');
        const sendRawBtn = document.getElementById('send-raw-btn');
        const clearLogBtn = document.getElementById('clear-log-btn');
        const wsUrl = document.getElementById('ws-url');
        const messageType = document.getElementById('message-type');
        const transcript = document.getElementById('transcript');
        const digits = document.getElementById('digits');
        const callSid = document.getElementById('call-sid');
        const rawJson = document.getElementById('raw-json');
        const logPanel = document.getElementById('log-panel');
        const connectionStatus = document.getElementById('connection-status');
        const speechInput = document.getElementById('speech-input');
        const dtmfInput = document.getElementById('dtmf-input');
        
        // Event Listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        sendBtn.addEventListener('click', sendMessage);
        sendRawBtn.addEventListener('click', sendRawJson);
        clearLogBtn.addEventListener('click', clearLog);
        messageType.addEventListener('change', updateFormFields);
        
        // Initialize form fields
        updateFormFields();
        
        // Functions
        function connect() {
            try {
                const url = wsUrl.value.trim();
                if (!url) {
                    logMessage('Please enter a WebSocket URL', 'error');
                    return;
                }
                
                socket = new WebSocket(url);
                
                socket.onopen = function(event) {
                    logMessage('Connected to WebSocket server', 'received');
                    updateConnectionStatus(true);
                };
                
                socket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        logMessage(`Received: ${JSON.stringify(data, null, 2)}`, 'received');
                    } catch (e) {
                        logMessage(`Received (raw): ${event.data}`, 'received');
                    }
                };
                
                socket.onerror = function(error) {
                    logMessage(`WebSocket Error: ${error.message || 'Unknown error'}`, 'error');
                };
                
                socket.onclose = function(event) {
                    const reason = event.reason ? ` Reason: ${event.reason}` : '';
                    logMessage(`Disconnected from WebSocket server. Code: ${event.code}.${reason}`, 'error');
                    updateConnectionStatus(false);
                };
                
            } catch (error) {
                logMessage(`Error: ${error.message}`, 'error');
            }
        }
        
        function disconnect() {
            if (socket) {
                socket.close();
                socket = null;
            }
        }
        
        function updateConnectionStatus(isConnected) {
            connectionStatus.className = isConnected ? 'status connected' : 'status disconnected';
            connectionStatus.textContent = isConnected ? 'Connected' : 'Disconnected';
            
            connectBtn.disabled = isConnected;
            disconnectBtn.disabled = !isConnected;
            sendBtn.disabled = !isConnected;
            sendRawBtn.disabled = !isConnected;
        }
        
        function updateFormFields() {
            const type = messageType.value;
            
            // Hide all input fields first
            speechInput.style.display = 'none';
            dtmfInput.style.display = 'none';
            
            // Show relevant fields based on message type
            if (type === 'speech') {
                speechInput.style.display = 'block';
            } else if (type === 'dtmf') {
                dtmfInput.style.display = 'block';
            }
            
            // Update raw JSON preview
            updateRawJson();
        }
        
        function updateRawJson() {
            const type = messageType.value;
            let message = {
                type: type
            };
            
            // Add call SID if available
            const sidValue = callSid.value.trim();
            if (sidValue) {
                message.callSid = sidValue;
            } else if (currentCallSid && type !== 'start') {
                message.callSid = currentCallSid;
            } else if (type === 'start') {
                message.callSid = `test_call_${new Date().toISOString().replace(/[-:.TZ]/g, '')}`;
            }
            
            // Add type-specific fields
            if (type === 'speech') {
                message.transcript = transcript.value;
            } else if (type === 'dtmf') {
                message.digits = digits.value;
            } else if (type === 'mark') {
                message.name = 'test_mark';
            }
            
            rawJson.value = JSON.stringify(message, null, 2);
        }
        
        function sendMessage() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage('Not connected to WebSocket server', 'error');
                return;
            }
            
            try {
                // Update raw JSON before sending
                updateRawJson();
                const message = JSON.parse(rawJson.value);
                
                // Store call SID if it's a start message
                if (message.type === 'start') {
                    currentCallSid = message.callSid;
                    callSid.value = currentCallSid;
                }
                
                // Send the message
                socket.send(JSON.stringify(message));
                logMessage(`Sent: ${JSON.stringify(message, null, 2)}`, 'sent');
                
            } catch (error) {
                logMessage(`Error: ${error.message}`, 'error');
            }
        }
        
        function sendRawJson() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage('Not connected to WebSocket server', 'error');
                return;
            }
            
            try {
                const json = rawJson.value.trim();
                if (!json) {
                    logMessage('Please enter JSON to send', 'error');
                    return;
                }
                
                const message = JSON.parse(json);
                
                // Store call SID if it's a start message
                if (message.type === 'start') {
                    currentCallSid = message.callSid;
                    callSid.value = currentCallSid;
                }
                
                // Send the message
                socket.send(json);
                logMessage(`Sent: ${JSON.stringify(message, null, 2)}`, 'sent');
                
            } catch (error) {
                logMessage(`Error: ${error.message}`, 'error');
            }
        }
        
        function logMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const timestamp = document.createElement('div');
            timestamp.className = 'timestamp';
            timestamp.textContent = new Date().toLocaleTimeString();
            
            const content = document.createElement('pre');
            content.textContent = message;
            
            messageDiv.appendChild(timestamp);
            messageDiv.appendChild(content);
            
            logPanel.appendChild(messageDiv);
            logPanel.scrollTop = logPanel.scrollHeight;
        }
        
        function clearLog() {
            logPanel.innerHTML = '';
        }
        
        // Update form when inputs change
        callSid.addEventListener('input', updateRawJson);
        transcript.addEventListener('input', updateRawJson);
        digits.addEventListener('input', updateRawJson);
        
        // Initial update
        updateRawJson();
    </script>
</body>
</html>