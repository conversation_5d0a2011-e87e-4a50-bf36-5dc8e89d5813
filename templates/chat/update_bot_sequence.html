{% extends "layouts/base.html" %}

{% block title %} Create Your Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
<link rel="stylesheet" href="/static/assets/libs/select2/dist/css/select2.min.css">
<link rel="stylesheet" href="/static/assets/libs/dragula/dist/dragula.min.css">
{% endblock stylesheets %}

{% block content %}
    
    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">
                        Update Bot Sequence - {{ chat_bot_sequence.name }}
                    </h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Bots
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Update a Prospecting Bot Sequence
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>


    
    
    
    
    <div class="row">
        <div class="col-12">
    
            
            <div class="card">
                <div class="card-body">
    
    
                    <div class="default-tab">
    
                        <ul class="nav nav-tabs" id="myTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active text-uppercase" id="about-tab" data-bs-toggle="tab" href="#about" role="tab" aria-controls="about" aria-selected="true">
                                    <i class="ti ti-info-circle me-1"></i>
                                    About
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link text-uppercase" id="goal-tab" data-bs-toggle="tab" href="#goal" role="tab" aria-controls="detail" aria-selected="false">
                                    <i class="ti ti-target-arrow me-1"></i>
                                    Sequence Goal
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link text-uppercase" id="bots-tab" data-bs-toggle="tab" href="#bots" role="tab" aria-controls="detail" aria-selected="false">
                                    <i class="ti ti-robot me-1"></i>
                                    Bots
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link text-uppercase" id="phone-tab" data-bs-toggle="tab" href="#phone" role="tab" aria-controls="phone" aria-selected="false">
                                    <i class="ti ti-phone me-1"></i>
                                    Phone #
                                </a>
                            </li>
                        </ul>
                                
                        
                        <div class="tab-content" id="myTabContent">
    
                            
                            {# ######################################################### #}
                            {# ######################################################### #}
                            {#      ABOUT TAB                                            #}
                            {# ######################################################### #}
                            {# ######################################################### #}
                            <div class="tab-pane fade show active" id="about" role="tabpanel" aria-labelledby="about-tab">
    
                                <div class="pt-4">
                                    <h5>About Your Bot Sequence</h5>
                                    <hr>
                                    <form method="post" class="needs-validation" novalidate>
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="update_about">
                                        <div class="row">
                                            <div class="col-6 mb-4">
                                                <div class="form-group">
                                                    <label class="form-label mb-1">Bot Sequence Name:</label>
                                                    <input type="text" name="name" class="form-control" placeholder="Chatbot Name"
                                                           value="{{ chat_bot_sequence.name }}" required>
                                                    <small>Name your bot sequence, for your eyes only.</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-4">
                                                <div class="form-group">
                                                    <label class="form-label mb-1">Bot Sequence Description (optional):</label>
                                                    <input type="text" name="description" class="form-control" placeholder="Chatbot Description"
                                                           value="{% if chat_bot_sequence.description %}{{ chat_bot_sequence.description }}{% endif %}">
                                                    <small>Describe your bot sequence, this is for your benefit only.</small>
                                                </div>
                                            </div>
                                        
                                                
                                        
                                                
                                                
                                                
                                            <div class="col-6 mb-4">
                                                <label class="form-label mb-1">Is Active:</label>
                                                <div class="input-group">
                                                    <div class="input-group-text">
                                                        <div class="form-check custom-checkbox">
                                                            <input type="checkbox" class="form-check-input" name="is_active"{% if chat_bot_sequence.is_active %} checked{% endif %}>
                                                        </div>
                                                    </div>
                                                    <input type="number" step="0.01" class="form-control" placeholder="Max Daily Spend" 
                                                           name="max_daily_spend" value="{% if chat_bot_sequence.max_daily_spend %}{{ chat_bot_sequence.max_daily_spend }}{% endif %}">
                                                </div>
                                                <small>Should this bot call out?  Should the bot have a daily spending limit?</small>
                                                {% if form.max_daily_spend.errors %}
                                                <small class="text-danger">{{ form.max_daily_spend.errors }}</small>
                                                {% endif %}
                                            </div>
                                                
                                                    
                                        
                                                
                                                
                                                
                                            <div class="col-6 mb-4">
                                                <label class="form-label mb-1">Forward on Success:</label>
                                                <div class="input-group">
                                                    <div class="input-group-text">
                                                        <div class="form-check custom-checkbox">
                                                            <input type="checkbox" class="form-check-input" name="forward_on_success"{% if chat_bot_sequence.forward_on_success %} checked{% endif %}>
                                                        </div>
                                                    </div>
                                                    <select class="form-select" id="inputGroupSelect01">
                                                        <option selected>Select Chat Bot</option>
                                                        {% for chat_bot in chat_bot_sequences %}
                                                        <option value="{{ chat_bot.pk }}"{% if chat_bot.pk == chat_bot_sequence.forward_chat_bot_sequence.pk %} selected{% endif %}>{{ chat_bot.name }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                                <small>Should we forward successful leads to a new sequence? For example, after they buys a product, then send them to an onboarding sequence.</small>
                                                {% if form.max_daily_spend.errors %}
                                                <small class="text-danger">{{ form.max_daily_spend.errors }}</small>
                                                {% endif %}
                                            </div>
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                                
                                                
                                                
                                                
                                            <div class="col-12 mb-4">
                                                <label class="form-label mb-1">When can this bot sequence contact leads:</label>
                                                <div class="form-group">
                                                    <div class="form-check form-check-inline">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" name="can_contact_monday" class="form-check-input"{% if chat_bot_sequence.can_contact_monday %} checked{% endif %}>
                                                            Monday
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" name="can_contact_tuesday" class="form-check-input"{% if chat_bot_sequence.can_contact_tuesday %} checked{% endif %}>
                                                            Tuesday
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" name="can_contact_wednesday" class="form-check-input"{% if chat_bot_sequence.can_contact_wednesday %} checked{% endif %}>
                                                            Wednesday
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" name="can_contact_thursday" class="form-check-input"{% if chat_bot_sequence.can_contact_thursday %} checked{% endif %}>
                                                            Thursday
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" name="can_contact_friday" class="form-check-input"{% if chat_bot_sequence.can_contact_friday %} checked{% endif %}>
                                                            Friday
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" name="can_contact_saturday" class="form-check-input"{% if chat_bot_sequence.can_contact_saturday %} checked{% endif %}>
                                                            Saturday
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" name="can_contact_sunday" class="form-check-input"{% if chat_bot_sequence.can_contact_sunday %} checked{% endif %}>
                                                            Sunday
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                                
                                                
                                                
                                                
                                            <div class="col-3 mb-4">
                                                <label class="form-label mb-1">Contact start time:</label>
                                                <div class="input-group">
                                                    <select class="default-select form-control form-select wide" name="can_contact_start_time" required>
                                                        <option value="0">Select Start Time</option>
                                                        <option value="1"{% if chat_bot_sequence.can_contact_start_time == 1 %} selected{% endif %}>1:00 AM</option>
                                                        <option value="2"{% if chat_bot_sequence.can_contact_start_time == 2 %} selected{% endif %}>2:00 AM</option>
                                                        <option value="3"{% if chat_bot_sequence.can_contact_start_time == 3 %} selected{% endif %}>3:00 AM</option>
                                                        <option value="4"{% if chat_bot_sequence.can_contact_start_time == 4 %} selected{% endif %}>4:00 AM</option>
                                                        <option value="5"{% if chat_bot_sequence.can_contact_start_time == 5 %} selected{% endif %}>5:00 AM</option>
                                                        <option value="6"{% if chat_bot_sequence.can_contact_start_time == 6 %} selected{% endif %}>6:00 AM</option>
                                                        <option value="7"{% if chat_bot_sequence.can_contact_start_time == 7 %} selected{% endif %}>7:00 AM</option>
                                                        <option value="8"{% if chat_bot_sequence.can_contact_start_time == 8 %} selected{% endif %}>8:00 AM</option>
                                                        <option value="9"{% if chat_bot_sequence.can_contact_start_time == 9 %} selected{% endif %}>9:00 AM</option>
                                                        <option value="10"{% if chat_bot_sequence.can_contact_start_time == 10 %} selected{% endif %}>10:00 AM</option>
                                                        <option value="11"{% if chat_bot_sequence.can_contact_start_time == 11 %} selected{% endif %}>11:00 AM</option>
                                                        <option value="12"{% if chat_bot_sequence.can_contact_start_time == 12 %} selected{% endif %}>12:00 AM</option>
                                                        <option value="13"{% if chat_bot_sequence.can_contact_start_time == 13 %} selected{% endif %}>1:00 PM</option>
                                                        <option value="14"{% if chat_bot_sequence.can_contact_start_time == 14 %} selected{% endif %}>2:00 PM</option>
                                                        <option value="15"{% if chat_bot_sequence.can_contact_start_time == 15 %} selected{% endif %}>3:00 PM</option>
                                                        <option value="16"{% if chat_bot_sequence.can_contact_start_time == 16 %} selected{% endif %}>4:00 PM</option>
                                                        <option value="17"{% if chat_bot_sequence.can_contact_start_time == 17 %} selected{% endif %}>5:00 PM</option>
                                                        <option value="18"{% if chat_bot_sequence.can_contact_start_time == 18 %} selected{% endif %}>6:00 PM</option>
                                                        <option value="19"{% if chat_bot_sequence.can_contact_start_time == 19 %} selected{% endif %}>7:00 PM</option>
                                                        <option value="20"{% if chat_bot_sequence.can_contact_start_time == 20 %} selected{% endif %}>8:00 PM</option>
                                                        <option value="21"{% if chat_bot_sequence.can_contact_start_time == 21 %} selected{% endif %}>9:00 PM</option>
                                                        <option value="22"{% if chat_bot_sequence.can_contact_start_time == 22 %} selected{% endif %}>10:00 PM</option>
                                                        <option value="23"{% if chat_bot_sequence.can_contact_start_time == 23 %} selected{% endif %}>11:00 PM</option>
                                                        <option value="24"{% if chat_bot_sequence.can_contact_start_time == 24 %} selected{% endif %}>12:00 PM</option>
                                                    </select>
                                                </div>
                                            </div>
                                                
                                                
                                                
                                            <div class="col-3 mb-4">
                                                <label class="form-label mb-1">Contact end time:</label>
                                                <div class="input-group">
                                                    <select class="default-select form-control form-select wide" name="can_contact_end_time" required>
                                                        <option value="0">Select End Time</option>
                                                        <option value="1"{% if chat_bot_sequence.can_contact_end_time == 1 %} selected{% endif %}>1:00 AM</option>
                                                        <option value="2"{% if chat_bot_sequence.can_contact_end_time == 2 %} selected{% endif %}>2:00 AM</option>
                                                        <option value="3"{% if chat_bot_sequence.can_contact_end_time == 3 %} selected{% endif %}>3:00 AM</option>
                                                        <option value="4"{% if chat_bot_sequence.can_contact_end_time == 4 %} selected{% endif %}>4:00 AM</option>
                                                        <option value="5"{% if chat_bot_sequence.can_contact_end_time == 5 %} selected{% endif %}>5:00 AM</option>
                                                        <option value="6"{% if chat_bot_sequence.can_contact_end_time == 6 %} selected{% endif %}>6:00 AM</option>
                                                        <option value="7"{% if chat_bot_sequence.can_contact_end_time == 7 %} selected{% endif %}>7:00 AM</option>
                                                        <option value="8"{% if chat_bot_sequence.can_contact_end_time == 8 %} selected{% endif %}>8:00 AM</option>
                                                        <option value="9"{% if chat_bot_sequence.can_contact_end_time == 9 %} selected{% endif %}>9:00 AM</option>
                                                        <option value="10"{% if chat_bot_sequence.can_contact_end_time == 10 %} selected{% endif %}>10:00 AM</option>
                                                        <option value="11"{% if chat_bot_sequence.can_contact_end_time == 11 %} selected{% endif %}>11:00 AM</option>
                                                        <option value="12"{% if chat_bot_sequence.can_contact_end_time == 12 %} selected{% endif %}>12:00 AM</option>
                                                        <option value="13"{% if chat_bot_sequence.can_contact_end_time == 13 %} selected{% endif %}>1:00 PM</option>
                                                        <option value="14"{% if chat_bot_sequence.can_contact_end_time == 14 %} selected{% endif %}>2:00 PM</option>
                                                        <option value="15"{% if chat_bot_sequence.can_contact_end_time == 15 %} selected{% endif %}>3:00 PM</option>
                                                        <option value="16"{% if chat_bot_sequence.can_contact_end_time == 16 %} selected{% endif %}>4:00 PM</option>
                                                        <option value="17"{% if chat_bot_sequence.can_contact_end_time == 17 %} selected{% endif %}>5:00 PM</option>
                                                        <option value="18"{% if chat_bot_sequence.can_contact_end_time == 18 %} selected{% endif %}>6:00 PM</option>
                                                        <option value="19"{% if chat_bot_sequence.can_contact_end_time == 19 %} selected{% endif %}>7:00 PM</option>
                                                        <option value="20"{% if chat_bot_sequence.can_contact_end_time == 20 %} selected{% endif %}>8:00 PM</option>
                                                        <option value="21"{% if chat_bot_sequence.can_contact_end_time == 21 %} selected{% endif %}>9:00 PM</option>
                                                        <option value="22"{% if chat_bot_sequence.can_contact_end_time == 22 %} selected{% endif %}>10:00 PM</option>
                                                        <option value="23"{% if chat_bot_sequence.can_contact_end_time == 23 %} selected{% endif %}>11:00 PM</option>
                                                        <option value="24"{% if chat_bot_sequence.can_contact_end_time == 24 %} selected{% endif %}>12:00 PM</option>
                                                    </select>
                                                </div>
                                            </div>
                                                    
                                                
                                                
                                                
                                            <div class="col-6 mb-4">
                                                <div class="form-group">
                                                    <label class="form-label mb-1">Lead Categories (optional):</label>
                                                    <select class="multi-select-categories form-control wide" multiple="multiple" name="lead_categories">
                                                        {% for category in lead_categories %}
                                                        <option value="{{ category.pk }}"{% for lead_category in chat_bot_sequence.lead_categories.all %}{% if category.pk == lead_category.pk %} selected{% endif %}{% endfor %}>{{ category.name }}</option>
                                                        {% endfor %}
                                                    </select>
                                                    <small>Choose all categories this bots should contact.</small>
                                                </div>
                                            </div>
                                        
                                                
                                                
                                        </div>
                                                
                                        <div class="row mt-4">
                                            <div class="col-12 text-right">
                                                <button type="submit" class="btn btn-primary me-2">
                                                    Update Bot Sequence <i class="ti ti-chevron-right me-0 ms-2"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            
                            </div>
    
    
                            {# ######################################################### #}
                            {# ######################################################### #}
                            {#      Goal TAB                                             #}
                            {# ######################################################### #}
                            {# ######################################################### #}
                            <div class="tab-pane fade" id="goal" role="tabpanel" aria-labelledby="goal-tab">
    
                                <div class="pt-4">
    
                                    <h5>Chat Bot Sequence Goal</h5>
                                    <hr />
    
                                    <div class="alert alert-success alert-dismissible fade show">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Important!</strong> This section is to help this sequence know when it has succeeded converting a lead.
                                        Select the type of goal and then enter in a detailed description of what constitutes success.
                                        This is a prompt that will instruct the AI what to look for when deciding. 
                                    </div>
    
                                    <form method="post" class="needs-validation" novalidate>
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="update_goal">
                                    
                                        <div class="row mb-4">
                                        
                                            <div class="col-12 mb-4">
                                                <div class="form-group">
                                                    <label class="form-label mb-1">Primary Goal (optional):</label>
                                                    <select class="default-select form-control form-select form-control-lg wide" name="goal_type" required>
                                                        <option value="">Select Goal Type</option>
                                                        {% for goal in goals %}
                                                        <option value="{{ goal.0 }}"{% if goal.0 == chat_bot_sequence.goal_type %} selected{% endif %}>{{ goal.1 }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            
                                            <div class="col-12">
                                                <div class="form-group mb-0">
                                                    <label class="form-label mb-1" for="goal_type_prompt">Goal Type Prompt (optional)</label>
                                                    <textarea class="form-control" name="goal_type_prompt" id="goal_type_prompt" 
                                                              rows="10" required>{% if chat_bot_sequence.goal_type_prompt %}{{ chat_bot_sequence.goal_type_prompt }}{% endif %}</textarea>
                                                </div>
                                            </div>
                                        
                                        </div>
    
    
                                        <div class="row mt-4">
                                            <div class="col-12 text-right">
                                                <button type="submit" class="btn btn-primary me-2">
                                                    Update Bot Sequence <i class="ti ti-chevron-right me-0 ms-2"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
    
                                </div>
                            
                            </div>
                        
                        
                        
                        
                            
                            {# ######################################################### #}
                            {# ######################################################### #}
                            {#      Bot TAB                                              #}
                            {# ######################################################### #}
                            {# ######################################################### #}
                            <div class="tab-pane fade" id="bots" role="tabpanel" aria-labelledby="bots-tab">
    
                                <div class="pt-4">
    
                                    <h5>Chat Bot Contact Sequence</h5>
                                    <hr />
                                    <form>
                                        <div class="input-group">
                                            <select class="form-select" id="inputGroupSelect04">
                                                <option selected>Choose Bot to Add</option>
                                                {% for chat_bot in chat_bots %}
                                                <option data-id="{{ chat_bot.pk }}" data-name="{{ chat_bot.name }}" data-description="{{ chat_bot.description }}">{{ chat_bot.name }}</option>
                                                {% endfor %}
                                            </select>
                                            <button class="btn btn-primary" type="button" id="add-chat-button">Add Chat Bot</button>
                                        </div>
                                    </form>
                                
                                    <div class="alert alert-success alert-dismissible fade show mt-4">
                                        <i class="fas fa-info-circle"></i>
                                        Add Bots to this sequence using the dropdown above.  Drag and drop to order the bots in the sequence
                                        the lead should be contacted. The lead will be contacted each day with the next bot in the sequence, 
                                        until a successful outcome or the lead opts out. <br /><br />
                                        If you have assigned this sequence a phone number, it will be attached to the first bot in your sequence.
                                    </div>
                                
                                    <div class="row draggable-cards mt-4" id="draggable-area">
                                        {% for bot in sequence_order %}
                                        <div class="col-md-4 d-flex align-items-stretch" data-id="{{ bot.pk }}">
                                            <div class="card text-bg-primary text-white w-100 card-hover">
                                                <div class="card-body">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-robot display-6"></i>
                                                        <div class="ms-auto">
                                                            <i class="ti ti-list-tree fs-8"></i>
                                                        </div>
                                                    </div>
                                                    <div class="mt-4">
                                                        <h4 class="card-title mb-1 text-white">
                                                            {{ bot.name }}
                                                        </h4>
                                                        <p class="card-text fw-normal text-white opacity-75">
                                                            {{ bot.description }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                
                                    <div class="row mt-0">
                                        <div class="col-12 text-right">
                                            <form method="post" id="sequence-form" class="needs-validation" novalidate>
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="update_sequence">
                                                <button type="submit" class="btn btn-primary me-2">
                                                    Update Bot Sequence <i class="ti ti-chevron-right me-0 ms-2"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                    
                                
                                </div>
                            </div>
    
    
    
    
                            {# ######################################################### #}
                            {# ######################################################### #}
                            {#      PHONE NUMBER TAB                                     #}
                            {# ######################################################### #}
                            {# ######################################################### #}
                            <div class="tab-pane fade" id="phone" role="tabpanel" aria-labelledby="phone-tab">
                                    
                                <div class="pt-4">
    
                                    {% if chat_bot_sequence.phone_number and chat_bot_sequence.use_phone_number %}
                                                
                                        <h5 class="mt-0">Your Chat Bot Phone Number</h5>
                                        <hr>
                                                
                                        <div class="row">
                                            <div class="col-md-12">
                                            
                                                <div class="widget-stat card bg-danger ">
                                                    <div class="card-body p-4">
                                                        <div class="media">
                                                            <span class="me-3">
                                                                <i class="las la-phone-volume"></i>
                                                            </span>
                                                            <div class="media-body text-white">
                                                                <p class="mb-1 text-white">Your Bot Phone Number</p>
                                                                <h3 class="text-white mb-2">
                                                                    ({{ chat_bot_sequence.phone_number|slice:"2:5" }})
                                                                    {{ chat_bot_sequence.phone_number|slice:"5:8" }}-{{ chat_bot_sequence.phone_number|slice:"8:12" }}
                                                                </h3>
                                                                <div class="progress mb-2 bg-secondary">
                                                                    <div class="progress-bar progress-animated bg-white" style="width: 100%"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                            
                                        </div>
                                                
                                                
                                    {% else %}
                                                
                                        <h5 class="mt-0">Add a Phone Number to Your Chat Bot</h5>
                                        <hr>
    
                                        <form class="row">
                                            <div class="col-md-4">
                                                <div class="card box-2 h-100 p-0">
                                                    <div class="card-header border-0 ps-1">
                                                        <h5>Search Numbers</h5>
                                                    </div>
                                                    <div class="card-body ps-0 pe-0 pt-0">
                                                        <div class="flow">
                                                            <div class="form-check mb-4">
                                                                <input type="checkbox" class="form-check-input" id="phone-type" name="phone-type">
                                                                <label class="form-check-label" for="phone-type">Search Toll Free Numbers</label>
                                                            </div>
        
                                                            <div class="form-group mb-4">
                                                                <label class="form-label mb-1" for="area_code">Local Area Code <small>(optional)</small></label>
                                                                <input class="form-control" type="number" value="" id="area-code" min="100" max="999" pattern="\d{1,3}">
                                                            </div>
                                                                
                                                            <div class="form-group">
                                                                <button type="button" class="btn btn-primary mt-3" id="phone-search-button">
                                                                    Search Phone Numbers
                                                                    <i class="ti ti-chevron-right me-0 ms-2"></i>
                                                                </button>
                                                            </div>
                                                        </div>
    
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="card h-100">
                                                    <div class="card-header border-0 ps-1">
                                                        <h5>Search Results</h5>
                                                    </div>
                                                    <div class="card-body ps-0 pe-0 pt-0">
                                                        <div class="row" id="phone-search-results">
    
                                                        </div>
                                                            
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                                
                                    {% endif %}
                                
                                </div>
                                    
                            </div>
                                
                                
                        </div>
                    
                    
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    
    
    
    
    
    
    <div id="buyNumberModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalCenterTitle">
                        Attach This Phone Number?
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>
                        Are you sure that you want to attach the phone number <b id="phone-numberp"></b> to 
                        this bot? This can only be changed by technical support.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        <input type="hidden" name="phone" id="phone-number" value="">
                        <input type="hidden" name="action" value="attach_phone">
                        <button type="submit" class="btn btn-primary me-2">
                            Yes, Attach This Phone Number <i class="ti ti-chevron-right me-0 ms-2"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>    
    
    
    
    
    
    
    
    
    
    
    

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/libs/jquery.repeater/jquery.repeater.min.js"></script>
    <script src="/static/assets/libs/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="/static/assets/js/forms/repeater-init.js"></script>
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>
    <script src="/static/assets/libs/select2/dist/js/select2.full.min.js"></script>
    <script src="/static/assets/libs/select2/dist/js/select2.min.js"></script>
    <script src="/static/assets/libs/dragula/dist/dragula.min.js"></script>
    <script>
        $(document).ready(function() {
            
            /*
             * 
             */
            dragula([document.getElementById("draggable-area")]);
            
            /*
             * 
             */
            $(".multi-select-categories").select2({
                placeholder: "Select lead categories",
                closeOnSelect: false,
                minimumInputLength: 0
            });
            
            
            
            
            /*
             * 
             */
            $("#add-chat-button").click(function () {
                const selectedOption = $('#inputGroupSelect04 option:selected');
                console.log(selectedOption);
        
                var chatBotId = selectedOption.data("id");
                var chatBotName = selectedOption.data("name");
                var chatBotDescription = selectedOption.data("description");
                
                if(chatBotId === undefined) {
                    Swal.fire({
                        title: "Choose Bot",
                        text: "Please select a bot from the list.",
                        icon: "error"
                    });
                    return;
                }
        
                $("#draggable-area").append(`
                    <div class="col-md-4 d-flex align-items-stretch" data-id="${chatBotId}">
                        <div class="card text-bg-primary text-white w-100 card-hover">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i class="ti ti-robot display-6"></i>
                                    <div class="ms-auto">
                                        <i class="ti ti-list-tree fs-8"></i>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <h4 class="card-title mb-1 text-white">
                                        ${chatBotName}
                                    </h4>
                                    <p class="card-text fw-normal text-white opacity-75">
                                        ${chatBotDescription}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                `);
            });
            
            /*
             * 
             */
            $('#sequence-form').on('submit', function (e) {
                e.preventDefault();
        
                var collectedIds = [];
        
                $('.col-md-4.d-flex.align-items-stretch').each(function () {
                    var dataId = $(this).data('id');
                    if (dataId) {
                        collectedIds.push(dataId);
                    }
                });
        
                $(this).append(`<input type="hidden" name="bot_sequence" value="${collectedIds.join(',')}" />`);
        
                this.submit();
            });
            
            
            
            
            
            
            
            
            
            
            /*
             * 
             */
            $('#buyNumberModal').on('show.bs.modal', function(e) {
                const number = e.relatedTarget.dataset.number;
                $('#phone-number').val(number);
                const numberp = e.relatedTarget.dataset.numberp;
                $('#phone-numberp').html(numberp);
            });
            
            /*
             * 
             */
            $("#phone-search-button").click(function() {
                $("#phone-search-button").prop('disabled', true);
                $("#phone-search-button i").removeClass("ti-chevron-right").addClass("spinner-border spinner-border-sm");
    
                // Get the input values
                const areaCode = $("#area-code").val();
                const phoneType = $('#phone-type').prop('checked');
    
                $.ajax({
                    url: '{% url 'chat_api_phone_search' %}', // Replace this with your API URL
                    type: 'POST',
                    dataType: 'json',
                    data: {
                      'area_code': areaCode,
                      'phone_type': phoneType,
                      'csrfmiddlewaretoken': '{{ csrf_token }}'
                    },
                    success: function(data) {
                        // Clear the phone-search-results div
                        $("#phone-search-results").empty();
                
                        // Check the data length
                        if (data.length === 0) {
                            // Append a no results message
                            $("#phone-search-results").append('<h2>No results found</h2>');
                        } else {
                            // iterate through each item in the data array
                            $.each(data, function(i, item) {
                                // create the div string, replacing [item0] and [item1] with the array items
                                var divStr = `<div class="col-md-6 d-flex align-items-stretch">
                                  <a href="javascript:void(0)" class="card text-bg-primary text-white w-100 card-hover" data-bs-toggle="modal" data-bs-target="#buyNumberModal" 
                                    data-number="${item[0]}" data-numberp="${item[1]}">
                                    <div class="card-body">
                                      <div class="d-flex align-items-center">
                                        <i class="ti ti-phone display-6"></i>
                                        <div class="ms-auto">
                                          <i class="ti ti-circle-plus fs-8"></i>
                                        </div>
                                      </div>
                                      <div class="mt-4">
                                        <h4 class="card-title mb-1 text-white">
                                          ${item[1]}
                                        </h4>
                                        <p class="card-text fw-normal text-white opacity-75">
                                          Click to attach this number to the sequence.
                                        </p>
                                      </div>
                                    </div>
                                  </a>
                                </div>`;
                    
                                // append the new div to the phone-search-results div
                                $("#phone-search-results").append(divStr);
                            });
                        }
                    },
                    error: function() {
                        // Clear the phone-search-results div
                        $("#phone-search-results").empty();
                
                        // Append a no results message in case of error
                        $("#phone-search-results").append('<h2>No results found</h2>');
                    },
                    complete: function() {
                        $("#phone-search-button").prop('disabled', false);
                        $("#phone-search-button i").removeClass("spinner-border spinner-border-sm").addClass("ti-chevron-right");
                    }
                });
            });
    
    
    
    
    
    
    
    
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
        });
    </script>
{% endblock javascripts %}
