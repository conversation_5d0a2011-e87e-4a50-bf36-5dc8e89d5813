{% extends "layouts/base.html" %}

{% block title %} Create Your Bot {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}




    <div class="card card-body py-3">
        <div class="row align-items-center">
            <div class="col-12">
                <div class="d-sm-flex align-items-center justify-space-between">
                    <h4 class="mb-4 mb-sm-0 card-title">
                        Create a Telemarketing Bot
                    </h4>
                    <nav aria-label="breadcrumb" class="ms-auto">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item d-flex align-items-center">
                                <a class="text-muted text-decoration-none d-flex" href="{% url 'dashboard' %}">
                                    <iconify-icon icon="solar:home-2-line-duotone" class="fs-6"></iconify-icon>
                                </a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Bots
                                </span>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">
                                <span class="badge fw-medium fs-2 bg-primary-subtle text-primary">
                                    Create a Prospecting Bot
                                </span>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        <div class="col-xl-12">
        
            <form method="post" class="needs-validation" novalidate>
                {% csrf_token %}
            
                {% if form.errors %}
                    
                    <div class="alert alert-danger alert-dismissible fade show">
                        <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="me-2"><polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>
                        <strong>Error!</strong> Please fix the errors below.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="btn-close">
                        </button>
                    </div>
                    
                {% endif %}

                <div class="row">

                    <!-- [ form-element ] start -->
                    <div class="col-sm-8">
                        <!-- Basic Inputs -->
                        <div class="card">
                            <!--
                            <div class="card-header border-0 pb-0">
                                <h4 class="card-title">About Your Bot</h4>
                            </div>
                            -->

                            <div class="card-body">
                                    
                                <div class="row">
                                        
                                    <div class="mb-4 col-md-12">
                                        <label class="form-label mb-1" for="name">Bot Name:</label>
                                        <input type="text" name="name" class="form-control form-control-lg" placeholder="Bot Name" required>
                                        <small>Name your bot, for your eyes only.</small>
                                        {% if form.name.errors %}
                                            <small class="text-danger">{{ form.name.errors }}</small>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mb-3 col-md-12">
                                        <label class="form-label mb-1" for="description">Bot Description (optional):</label>
                                        <input type="text" name="description" class="form-control form-control-lg" placeholder="Bot Description" maxlength="250">
                                        <small>Describe your bot, this is for your benefit only.</small>
                                    </div>
                                
                                
                                    <!--
                                    <div class="mb-4 col-md-6">
                                        <label class="form-label mb-1">Primary Goal:</label>
                                        <select class="default-select form-control form-control-lg wide" name="goal_type">
                                            {% for goal in goals %}
                                            <option value="{{ goal.0 }}"{% if goal.0 == 1 %} selected{% endif %}>{{ goal.1 }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                
                                    <div class="mb-4 col-md-6">
                                        <label class="form-label mb-1">Secondary Goal:</label>
                                        <select class="default-select form-control form-control-lg wide" name="goal_type_secondary">
                                            {% for goal in goals %}
                                            <option value="{{ goal.0 }}">{{ goal.1 }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    -->
                                
                                
                                    <div class="form-repeater">
                                        <div data-repeater-list="first_messages">
                                            <div data-repeater-item class="row mt-2">
                                                <div class="col-md-10">
                                                    <label class="form-label mb-1" for="first_message">Bot Greeting:</label>
                                                    <input type="text" name="value" class="form-control form-control-lg" placeholder="Bot Greeting" required>
                                                    {% if form.first_message.errors %}
                                                    <small class="text-danger">{{ form.first_message.errors }}</small>
                                                    {% endif %}
                                                </div>
                                                <div class="col-md-2 mt-3 mt-md-0 d-flex flex-column justify-content-end">
                                                    <button data-repeater-delete="" class="btn btn-lg btn-danger form-control-lg" type="button">
                                                        <i class="ti ti-circle-x fs-5"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <small class="mt-0">This is the message spoken to someone who calls or is called by this bot.</small>
                                        <button type="button" data-repeater-create="" class="btn btn-success hstack gap-6 mb-5 mt-2">
                                            Add Bot Greeting
                                            <i class="ti ti-circle-plus ms-1 fs-5"></i>
                                        </button>
                                    </div>
                                
                                
                                
                                
                                
                                    <div class="mb-4 col-md-12">
                                        <label class="form-label mb-1" for="first_message_sms">Bot Greeting SMS:</label>
                                        <input type="text" name="first_message_sms" class="form-control form-control-lg" placeholder="First Message SMS" required>
                                        <small>This is the message test to someone from this bot.</small>
                                        {% if form.first_message_sms.errors %}
                                            <small class="text-danger">{{ form.first_message_sms.errors }}</small>
                                        {% endif %}
                                    </div>
                                
                                
                                
                                    <div class="mb-4 col-md-12">
                                        <label class="form-label mb-1" for="end_call_message">End Call Message (optional):</label>
                                        <input type="text" name="end_call_message" class="form-control form-control-lg" placeholder="End Call Message">
                                        <small>This is the message spoken at the end of the call.</small>
                                    </div>
                                
                                    <div class="mb-2 col-md-12">
                                        <label class="form-label mb-1" for="voicemail_message">Voicemail Message (optional):</label>
                                        <input type="text" name="voicemail_message" class="form-control form-control-lg" placeholder="Voicemail Message">
                                        <small>This is the message spoken if we run into a voicemail system.</small>
                                    </div>
                                
                                
                                    <div class="mb-2 col-md-12">
                                        <hr />
                                    </div>

                                    <div class="mb-2 col-md-12">
                                        
                                        <div class="alert alert-success alert-dismissible fade show">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Crucial!</strong> The next input is crucial. It should primarily contain instructions on how the bot should behave and perform.
                                            <br />&nbsp;<br />
                                            Avoid placing large sections of company or product information here—those details belong in the fields below or in uploaded files.
                                            <br />&nbsp;<br />
                                            Focus this input entirely on defining the bot’s behavior to ensure it interacts effectively.
                                            <br />&nbsp;<br />
                                            Click the <i class="fas fa-hands-helping"></i> icon for sample input.
                                        </div>
                                        
                                    </div>
                                
                                
                                
                                    <div class="mb-4 col-md-12">
                                        <label class="form-label mb-1" for="phone_system_prompt">
                                            Phone Bot Instructions (optional)
                                            <i class="fas fa-hands-helping text-info cursor-pointer" id="sample-phone-bot-instruction" data-toggle="tooltip" data-placement="top" title="Create a sample instruction"></i>
                                        </label>
                                        <textarea class="form-control" name="phone_system_prompt" id="phone_system_prompt" rows="10"></textarea>
                                    </div>
                                
                                    <div class="mb-2 col-md-12">
                                        <label class="form-label mb-1" for="sms_system_prompt">
                                            SMS Bot Instructions (optional)
                                            <i class="fas fa-hands-helping text-info cursor-pointer" id="sample-sms-bot-instruction" data-toggle="tooltip" data-placement="top" title="Create a sample instruction"></i>
                                        </label>
                                        <textarea class="form-control" name="sms_system_prompt" id="sms_system_prompt" rows="10"></textarea>
                                    </div>



                                    <div class="mb-2 col-md-12">
                                        <hr />
                                    </div>

                                    <div class="mb-2 col-md-12">
                                        
                                        <div class="alert alert-success alert-dismissible fade show">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Important!</strong> The next input is important and will be used to train your AI bot.
                                            Uploaded files are a better method to contribute this information however this method is available.
                                            <br />&nbsp;<br />
                                            Please be as descriptive as possible.  Every sentence you write here will teach the AI how to sell and convert your leads.
                                            If you'd prefer to upload information files then please feel free to leave this blank.
                                        </div>
                                        
                                    </div>



                                    <div class="mb-5 col-md-12">
                                        <label class="form-label mb-1" for="entity_info">Information (optional)</label>
                                        <textarea class="form-control" name="entity_info" id="entity_info" rows="10"></textarea>
                                    </div>





                                </div>



                            </div>
                            <div class="card-footer">
                                <button class="btn btn-primary me-2" type="submit">
                                    Create Bot & Continue to Update Section 
                                    <i class="ti ti-chevron-right me-0 ms-2"></i>
                                </button>
                                <!--
                                <button type="reset" class="btn btn-light">Reset</button>
                                -->
                            </div>
                        </div>
                    </div>
                    <!-- [ form-element ] end -->


                </div>
            </form>
  
        
        
        
        
        
        
        
        </div>
    </div>





















{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/libs/jquery.repeater/jquery.repeater.min.js"></script>
    <script src="/static/assets/libs/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="/static/assets/js/forms/repeater-init.js"></script>
    <script src="/static/assets/js/plugins/bootstrap-validation-init.js"></script>
    <script>
        $(document).ready(function() {
            /*
            $('form').on('submit', function() {
                $(':submit').prop('disabled', true);
                $(':submit i').removeClass('fa-arrow-alt-circle-right').addClass('fa-spin fa-spinner');
            });
             */
            
            $("#sample-phone-bot-instruction").click(function() {
                let text = `{{ sample_phone_bot_instruction|safe }}`;
                $("#phone_system_prompt").val(text);
            });
            
            $("#sample-sms-bot-instruction").click(function() {
                let text = `{{ sample_sms_bot_instruction|safe }}`;
                $("#sms_system_prompt").val(text);
            });
            
            
        });
    </script>
{% endblock javascripts %}
