{% extends "layouts/base_site.html" %}
{% load humanize %}
{% block title %} Contact {% endblock %}

{% block content %}


                    <!-- Dashboard Center Content -->
                    <div class="rbt-dashboard-content">





    <div class="banner-area">
                            <!-- AiWavesmall Slider -->
                            <div class="settings-area">
                                <h3 class="title">Contact Us</h3>
                            </div>
                        </div>



    <!-- Start Contact Area  -->

                    <div class="row row--15">
                        <div class="col-lg-8">
                            <div class="contact-details-box">

                                {% if success %}
                                    <div class="alert alert-success alert-dismissible radius fade show" role="alert">
                                        <i class="fa-sharp fa-regular fa-circle-exclamation me-2"></i>
                                        <strong>Success!</strong> Your message has been successfully delivered.
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                {% endif %}

                                <h3 class="title">Fill out the form below to drop us a line</h3>


                                            <form action="{% url 'home_contact' %}" method="post" class="rbt-profile-row rbt-default-form row row--15">
                                                 {% csrf_token %}
                                                <div class="col-lg-6 col-md-6 col-sm-6 col-12">
                                                    <div class="form-group">
                                                        <label for="firstname">First Name</label>
                                                        <input name="firstname" id="firstname" type="text" placeholder="First Name" required>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-md-6 col-sm-6 col-12">
                                                    <div class="form-group">
                                                        <label for="lastname">Last Name</label>
                                                        <input name="lastname" id="lastname" type="text" placeholder="Last Name">
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-md-6 col-sm-6 col-12">
                                                    <div class="form-group">
                                                        <label for="email">Email</label>
                                                        <input name="email" id="email" type="text" placeholder="Best Email Address" required>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 col-md-6 col-sm-6 col-12">
                                                    <div class="form-group">
                                                        <label for="phonenumber">Phone Number</label>
                                                        <input name="phonenumber" id="phonenumber" type="tel" placeholder="Phone Number">
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label for="bio1">Message</label>
                                                        <textarea name="message" id="message" cols="20" rows="5" placeholder=""></textarea>
                                                    </div>
                                                </div>
                                                <div class="col-12 mt--20">
                                                    <div class="form-group mb--0">
                                                        <button class="btn-default" type="submit" id="submitButton">
                                                            Send Contact Now
                                                            <i class="fa-sharp fa-regular fa-arrow-alt-circle-right ms-2" id="submitIcon"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                            <!-- End Profile Row  -->


                            </div>
                        </div>
                        <div class="col-lg-4 mt_md--30 mt_sm--30">
                            <div class="rainbow-address mt-0">
                                <div class="icon">
                                    <i class="fa-sharp fa-regular fa-location-dot"></i>
                                </div>
                                <div class="inner">
                                    <h4 class="title">Location</h4>
                                    <p class="b2">
                                        452 E. Silverado Ranch #273<br />
                                        Las Vegas, NV 89183
                                    </p>
                                </div>
                            </div>
                            <div class="rainbow-address">
                                <div class="icon">
                                    <i class="fa-sharp fa-solid fa-headphones"></i>
                                </div>
                                <div class="inner">
                                    <h4 class="title">Contact Number</h4>
                                    <p class="b2"><a href="tel:+12569528118">+****************</a></p>
                                </div>
                            </div>
                            <div class="rainbow-address">
                                <div class="icon">
                                    <i class="fa-sharp fa-regular fa-envelope"></i>
                                </div>
                                <div class="inner">
                                    <h4 class="title">Email</h4>
                                    <p class="b2"><a href="mailto:<EMAIL>"><EMAIL></a></p>
                                </div>
                            </div>
                        </div>
                    </div>






            <!-- Start CTA Area -->
            <div class="rainbow-cta-area rainbow-section-gap-big">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="aiwave-cta">
                                <div class="inner">
                                    <div class="content-left">
                                        <div class="section-title text-left" data-sal="slide-up" data-sal-duration="400" data-sal-delay="150">
                                            <h4 class="subtitle">
                                            <span class="theme-gradient">
                                                See Silo AI in Action
                                            </span>
                                        </h4>
                                        <h2 class="title w-600 mb--20">
                                            Experience the<br />Future of Automation
                                        </h2>
                                        <!--<p class="description b1">-->
                                        <h4 class="title">
                                            Try our demo now to see how Silo AI’s virtual assistants can send emails, texts, and perform tasks seamlessly—unlocking endless possibilities for your business.
                                        </h4>
                                        </div>
                                        <div class="app-store-btn">
                                            <a class="btn-default btn-large color-blacked" href="{% url 'home_demo_chat' %}">
                                                Try It Now
                                                <i class="fa-sharp fa-light fa-arrow-right ml--5"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="bg-shape-one">
                                        <img src="/static/assets-site/images/cta-img/bg-shape.png" alt="Bg shape">
                                    </div>
                                </div>
                                <div class="bg-shape-inside">
                                    <img src="/static/assets-site/images/bg/bg-shape-tree.png" alt="Bg shape">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>




                    </div>

    <!-- End Contact Area  -->

















{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script>
    $(document).ready(function () {
        // Attach event handler to the form's submit event
        $('form').on('submit', function (e) {
            // Disable the submit button to prevent multiple submissions
            $('#submitButton').prop('disabled', true);

            // Change the icon to a spinning loader (FontAwesome example)
            $('#submitIcon').removeClass('fa-arrow-alt-circle-right').addClass('fa-spinner fa-spin');
        });
    });
</script>
{% endblock javascripts %}