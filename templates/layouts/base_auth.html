<!DOCTYPE html>
<html lang="en" dir="ltr" data-bs-theme="light" data-color-theme="Aqua_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="/static/assets/images/logos/favicon.png" />

  <!-- Core Css -->
  <link rel="stylesheet" href="/static/assets/css/styles.css" />

  <title>Silo Enterprise AI - {% block title %}{% endblock %} </title>
</head>

<body>
  <!-- Preloader -->
  <div class="preloader">
    <img src="/static/assets/images/logos/favicon.png" alt="loader" class="lds-ripple img-fluid" />
  </div>
  <div id="main-wrapper">
    <div class="position-relative overflow-hidden radial-gradient min-vh-100 w-100">
      <div class="position-relative z-index-5">
        <div class="row gx-0">

          <div class="col-lg-6 col-xl-5 col-xxl-4">
            <div class="min-vh-100 bg-body row justify-content-center align-items-center p-5">
              <div class="col-12 auth-card">
                <a href="{% url 'login' %}" class="text-nowrap logo-img d-block w-100">
                  <img src="/static/assets/images/logos/logo-icon.png" class="dark-logo xw-75" alt="Logo-Dark" />
                </a>
                
                  
                {% include 'main/includes/messages.html' %}
 
                {% block content %}{% endblock content %}
                <div class="d-flex align-items-center justify-content-center">
                    <p class="fs-4 mb-0 fw-medium">
                        Copyright &copy; 2024 - Operation Silo LLC
                    </p>
                </div>
              
              
              </div>
            </div>
          </div>

          <div class="col-lg-6 col-xl-7 col-xxl-8 position-relative overflow-hidden bg-dark d-none d-lg-block">
            <div class="circle-top"></div>
            <div>
              <img src="/static/assets/images/logos/logo-icon-color.svg" class="circle-bottom opacity-25" alt="Logo-Dark" />
            </div>
            <div class="d-lg-flex align-items-center z-index-5 position-relative h-n80">
              <div class="row justify-content-center w-100">
                <div class="col-lg-6">
                  <h2 class="text-white fs-10 mb-3 lh-sm">
                    Welcome to<br />
                    Silo Enterprise<br />
                    Business AI System
                  </h2>
                  <span class="opacity-75 fs-4 text-white d-block mb-3">
                      Future-Proof Your Business with the Ultimate AI Platform.
                  </span>
                  <a href="{% url 'login' %}" class="btn btn-primary">Learn More</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="dark-transparent sidebartoggler"></div>
  <!-- Import Js Files -->
  <script src="/static/assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/static/assets/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="/static/assets/js/theme/app.init.js"></script>
  <script src="/static/assets/js/theme/theme.js"></script>
  <script src="/static/assets/js/theme/app.min.js"></script>

  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>

  <script>
      document.addEventListener('DOMContentLoaded', (event) => {
        const forms = document.querySelectorAll('form');
        forms.forEach((form) => {
            form.addEventListener('submit', function() {
                const submitButtons = form.querySelectorAll('[type="submit"]');
                submitButtons.forEach((submitButton) => {
                    submitButton.disabled = true;
    
                    const submitButtonIcons = submitButton.querySelectorAll('i');
                    submitButtonIcons.forEach((icon) => {
                        icon.classList.remove('ti-chevron-right');
                        icon.classList.add('spinner-border');
                        icon.classList.add('spinner-border-sm');
                    });
                });
            });
        });
      });
  </script>
</body>

</html>



                            






