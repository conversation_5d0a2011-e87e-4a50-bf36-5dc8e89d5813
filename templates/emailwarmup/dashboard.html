{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}Email Warm-up Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h4 class="text-themecolor">Email Warm-up Dashboard</h4>
        </div>
        <div class="col-md-7 align-self-center text-end">
            <div class="d-flex justify-content-end align-items-center">
                <ol class="breadcrumb justify-content-end">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                    <li class="breadcrumb-item active">Email Warm-up</li>
                </ol>
                <a href="{% url 'emailwarmup:trusted_accounts' %}" class="btn btn-info d-none d-lg-block m-l-15">
                    <i class="fa fa-address-book"></i> Trusted Accounts
                </a>
                <button type="button" class="btn btn-success d-none d-lg-block m-l-15" data-bs-toggle="modal" data-bs-target="#createCampaignModal">
                    <i class="fa fa-plus-circle"></i> New Campaign
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">Your Warm-up Campaigns</h4>
                    <h6 class="card-subtitle">Manage and monitor your email warm-up campaigns</h6>
                    
                    {% if campaign_stats %}
                        <div class="table-responsive m-t-20">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Campaign</th>
                                        <th>Email Account</th>
                                        <th>Status</th>
                                        <th>Week</th>
                                        <th>Last Activity</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in campaign_stats %}
                                        <tr>
                                            <td>{{ stat.campaign.name }}</td>
                                            <td>{{ stat.campaign.email_account.email_address }}</td>
                                            <td>
                                                {% if stat.campaign.status == 'active' %}
                                                    <span class="badge bg-success">Active</span>
                                                {% elif stat.campaign.status == 'pending' %}
                                                    <span class="badge bg-warning">Pending</span>
                                                {% elif stat.campaign.status == 'paused' %}
                                                    <span class="badge bg-info">Paused</span>
                                                {% elif stat.campaign.status == 'completed' %}
                                                    <span class="badge bg-primary">Completed</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Failed</span>
                                                {% endif %}
                                            </td>
                                            <td>Week {{ stat.campaign.current_week }}</td>
                                            <td>{{ stat.campaign.date_updated }}</td>
                                            <td>
                                                <a href="{% url 'emailwarmup:campaign_detail' stat.campaign.id %}" class="btn btn-sm btn-info">
                                                    <i class="fa fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info m-t-20">
                            <p>You don't have any warm-up campaigns yet. Click the "New Campaign" button to create one.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Campaign Modal -->
<div class="modal fade" id="createCampaignModal" tabindex="-1" aria-labelledby="createCampaignModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{% url 'emailwarmup:create_campaign' %}" method="post">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="createCampaignModalLabel">Create New Warm-up Campaign</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="email_account">Email Account</label>
                        <select class="form-control" id="email_account" name="email_account" required>
                            <option value="">Select an email account</option>
                            {% for account in email_accounts %}
                                <option value="{{ account.id }}">{{ account.account_name }} ({{ account.email_address }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="name">Campaign Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="description">Description (Optional)</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Create Campaign</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Initialize any charts or other JS functionality here
</script>
{% endblock %}