{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}Trusted Email Accounts{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h4 class="text-themecolor">Trusted Email Accounts</h4>
        </div>
        <div class="col-md-7 align-self-center text-end">
            <div class="d-flex justify-content-end align-items-center">
                <ol class="breadcrumb justify-content-end">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'emailwarmup:dashboard' %}">Email Warm-up</a></li>
                    <li class="breadcrumb-item active">Trusted Accounts</li>
                </ol>
                <button type="button" class="btn btn-success d-none d-lg-block m-l-15" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                    <i class="fa fa-plus-circle"></i> Add Account
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">Trusted Email Accounts</h4>
                    <h6 class="card-subtitle">Manage trusted email accounts for warm-up campaigns</h6>

                    {% if accounts %}
                        <div class="table-responsive m-t-20">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email Address</th>
                                        <th>Status</th>
                                        <th>SMTP</th>
                                        <th>IMAP</th>
                                        <th>Description</th>
                                        <th>Date Added</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for account in accounts %}
                                        <tr>
                                            <td>{{ account.name }}</td>
                                            <td>{{ account.email_address }}</td>
                                            <td>
                                                {% if account.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if account.smtp_server %}
                                                    <span class="badge bg-success">Configured</span>
                                                {% else %}
                                                    <span class="badge bg-warning">Not Configured</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if account.imap_server %}
                                                    <span class="badge bg-success">Configured</span>
                                                {% else %}
                                                    <span class="badge bg-warning">Not Configured</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if account.description %}
                                                    {{ account.description }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ account.date_created }}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#editAccountModal{{ account.id }}">
                                                    <i class="fa fa-edit"></i> Edit
                                                </button>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info m-t-20">
                            <p>You don't have any trusted email accounts yet. Click the "Add Account" button to add one.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Account Modal -->
<div class="modal fade" id="addAccountModal" tabindex="-1" aria-labelledby="addAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{% url 'emailwarmup:trusted_accounts' %}" method="post">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="addAccountModalLabel">Add Trusted Email Account</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="email_address">Email Address</label>
                        <input type="email" class="form-control" id="email_address" name="email_address" value="{% if form_data and not form_data.account_id %}{{ form_data.email_address }}{% endif %}" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="name">Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="{% if form_data and not form_data.account_id %}{{ form_data.name }}{% endif %}" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="description">Description (Optional)</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{% if form_data and not form_data.account_id %}{{ form_data.description }}{% endif %}</textarea>
                    </div>

                    <hr>
                    <h5>Email Server Settings</h5>
                    <p class="text-muted">These settings are required for the trusted account to automatically reply to emails.</p>

                    <!-- SMTP Settings -->
                    <h6 class="mt-3">SMTP Settings (for sending emails)</h6>
                    <div class="form-group mb-3">
                        <label for="smtp_server">SMTP Server</label>
                        <input type="text" class="form-control" id="smtp_server" name="smtp_server" value="{% if form_data and not form_data.account_id %}{{ form_data.smtp_server }}{% endif %}" placeholder="e.g., smtp.gmail.com">
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="smtp_port">SMTP Port</label>
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="{% if form_data and not form_data.account_id %}{{ form_data.smtp_port }}{% else %}587{% endif %}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3 mt-4">
                                <input class="form-check-input" type="checkbox" id="smtp_use_tls" name="smtp_use_tls" {% if not form_data or not form_data.account_id and form_data.smtp_use_tls %}checked{% endif %}>
                                <label class="form-check-label" for="smtp_use_tls">
                                    Use TLS
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="smtp_use_ssl" name="smtp_use_ssl" {% if form_data and not form_data.account_id and form_data.smtp_use_ssl %}checked{% endif %}>
                                <label class="form-check-label" for="smtp_use_ssl">
                                    Use SSL
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- IMAP Settings -->
                    <h6 class="mt-3">IMAP Settings (for receiving emails)</h6>
                    <div class="form-group mb-3">
                        <label for="imap_server">IMAP Server</label>
                        <input type="text" class="form-control" id="imap_server" name="imap_server" value="{% if form_data and not form_data.account_id %}{{ form_data.imap_server }}{% endif %}" placeholder="e.g., imap.gmail.com">
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="imap_port">IMAP Port</label>
                                <input type="number" class="form-control" id="imap_port" name="imap_port" value="{% if form_data and not form_data.account_id %}{{ form_data.imap_port }}{% else %}993{% endif %}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3 mt-4">
                                <input class="form-check-input" type="checkbox" id="imap_use_ssl" name="imap_use_ssl" {% if not form_data or not form_data.account_id and form_data.imap_use_ssl %}checked{% endif %}>
                                <label class="form-check-label" for="imap_use_ssl">
                                    Use SSL
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Email Password -->
                    <div class="form-group mb-3">
                        <label for="email_password">Email Password</label>
                        <input type="password" class="form-control" id="email_password" name="email_password" placeholder="Password for the email account">
                        <small class="form-text text-muted">This password is stored securely and used to authenticate with the email server.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Add Account</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Account Modals -->
{% for account in accounts %}
    <div class="modal fade" id="editAccountModal{{ account.id }}" tabindex="-1" aria-labelledby="editAccountModalLabel{{ account.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{% url 'emailwarmup:trusted_accounts' %}" method="post">
                    {% csrf_token %}
                    <input type="hidden" name="account_id" value="{{ account.id }}">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editAccountModalLabel{{ account.id }}">Edit Trusted Email Account</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group mb-3">
                            <label for="email_address{{ account.id }}">Email Address</label>
                            <input type="email" class="form-control" id="email_address{{ account.id }}" name="email_address" value="{{ account.email_address }}" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="name{{ account.id }}">Name</label>
                            <input type="text" class="form-control" id="name{{ account.id }}" name="name" value="{{ account.name }}" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="description{{ account.id }}">Description (Optional)</label>
                            <textarea class="form-control" id="description{{ account.id }}" name="description" rows="3">{{ account.description }}</textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_active{{ account.id }}" name="is_active" {% if account.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active{{ account.id }}">
                                Active
                            </label>
                        </div>

                        <hr>
                        <h5>Email Server Settings</h5>
                        <p class="text-muted">These settings are required for the trusted account to automatically reply to emails.</p>

                        <!-- SMTP Settings -->
                        <h6 class="mt-3">SMTP Settings (for sending emails)</h6>
                        <div class="form-group mb-3">
                            <label for="smtp_server{{ account.id }}">SMTP Server</label>
                            <input type="text" class="form-control" id="smtp_server{{ account.id }}" name="smtp_server" value="{{ account.smtp_server }}" placeholder="e.g., smtp.gmail.com">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="smtp_port{{ account.id }}">SMTP Port</label>
                                    <input type="number" class="form-control" id="smtp_port{{ account.id }}" name="smtp_port" value="{{ account.smtp_port }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3 mt-4">
                                    <input class="form-check-input" type="checkbox" id="smtp_use_tls{{ account.id }}" name="smtp_use_tls" {% if account.smtp_use_tls %}checked{% endif %}>
                                    <label class="form-check-label" for="smtp_use_tls{{ account.id }}">
                                        Use TLS
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="smtp_use_ssl{{ account.id }}" name="smtp_use_ssl" {% if account.smtp_use_ssl %}checked{% endif %}>
                                    <label class="form-check-label" for="smtp_use_ssl{{ account.id }}">
                                        Use SSL
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- IMAP Settings -->
                        <h6 class="mt-3">IMAP Settings (for receiving emails)</h6>
                        <div class="form-group mb-3">
                            <label for="imap_server{{ account.id }}">IMAP Server</label>
                            <input type="text" class="form-control" id="imap_server{{ account.id }}" name="imap_server" value="{{ account.imap_server }}" placeholder="e.g., imap.gmail.com">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="imap_port{{ account.id }}">IMAP Port</label>
                                    <input type="number" class="form-control" id="imap_port{{ account.id }}" name="imap_port" value="{{ account.imap_port }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3 mt-4">
                                    <input class="form-check-input" type="checkbox" id="imap_use_ssl{{ account.id }}" name="imap_use_ssl" {% if account.imap_use_ssl %}checked{% endif %}>
                                    <label class="form-check-label" for="imap_use_ssl{{ account.id }}">
                                        Use SSL
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Email Password -->
                        <div class="form-group mb-3">
                            <label for="email_password{{ account.id }}">Email Password</label>
                            <input type="password" class="form-control" id="email_password{{ account.id }}" name="email_password" placeholder="Enter new password to change" value="">
                            <small class="form-text text-muted">Leave blank to keep the current password. This password is stored securely and used to authenticate with the email server.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Account</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endfor %}
{% if form_data %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        {% if form_data.account_id %}
            // Open the edit modal for the account that had connection errors
            var editModal = document.getElementById('editAccountModal{{ form_data.account_id }}');
            if (editModal) {
                // Populate form fields with form_data values
                var formData = {
                    email_address: "{{ form_data.email_address|escapejs }}",
                    name: "{{ form_data.name|escapejs }}",
                    description: "{{ form_data.description|escapejs }}",
                    smtp_server: "{{ form_data.smtp_server|escapejs }}",
                    smtp_port: "{{ form_data.smtp_port }}",
                    smtp_use_tls: {% if form_data.smtp_use_tls %}true{% else %}false{% endif %},
                    smtp_use_ssl: {% if form_data.smtp_use_ssl %}true{% else %}false{% endif %},
                    imap_server: "{{ form_data.imap_server|escapejs }}",
                    imap_port: "{{ form_data.imap_port }}",
                    imap_use_ssl: {% if form_data.imap_use_ssl %}true{% else %}false{% endif %}
                };

                // Set form field values
                var accountId = "{{ form_data.account_id }}";
                document.getElementById('email_address' + accountId).value = formData.email_address;
                document.getElementById('name' + accountId).value = formData.name;
                document.getElementById('description' + accountId).value = formData.description;
                document.getElementById('smtp_server' + accountId).value = formData.smtp_server;
                document.getElementById('smtp_port' + accountId).value = formData.smtp_port;
                document.getElementById('smtp_use_tls' + accountId).checked = formData.smtp_use_tls;
                document.getElementById('smtp_use_ssl' + accountId).checked = formData.smtp_use_ssl;
                document.getElementById('imap_server' + accountId).value = formData.imap_server;
                document.getElementById('imap_port' + accountId).value = formData.imap_port;
                document.getElementById('imap_use_ssl' + accountId).checked = formData.imap_use_ssl;

                var modal = new bootstrap.Modal(editModal);
                modal.show();
            }
        {% else %}
            // Open the add account modal if there was an error adding a new account
            var addModal = document.getElementById('addAccountModal');
            if (addModal) {
                var modal = new bootstrap.Modal(addModal);
                modal.show();
            }
        {% endif %}
    });
</script>
{% endif %}
{% endblock %}
