{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}Campaign Details - {{ campaign.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h4 class="text-themecolor">Campaign Details</h4>
        </div>
        <div class="col-md-7 align-self-center text-end">
            <div class="d-flex justify-content-end align-items-center">
                <ol class="breadcrumb justify-content-end">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'emailwarmup:dashboard' %}">Email Warm-up</a></li>
                    <li class="breadcrumb-item active">Campaign Details</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div>
                            <h4 class="card-title">{{ campaign.name }}</h4>
                            <h6 class="card-subtitle">{{ campaign.email_account.email_address }}</h6>
                        </div>
                        <div class="ms-auto">
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    Actions
                                </button>
                                <div class="dropdown-menu">
                                    {% if campaign.status == 'pending' %}
                                        <form action="{% url 'emailwarmup:update_campaign_status' campaign.id %}" method="post" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="status" value="active">
                                            <button type="submit" class="dropdown-item">Start Campaign</button>
                                        </form>
                                    {% elif campaign.status == 'active' %}
                                        <form action="{% url 'emailwarmup:update_campaign_status' campaign.id %}" method="post" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="status" value="paused">
                                            <button type="submit" class="dropdown-item">Pause Campaign</button>
                                        </form>
                                    {% elif campaign.status == 'paused' %}
                                        <form action="{% url 'emailwarmup:update_campaign_status' campaign.id %}" method="post" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="status" value="active">
                                            <button type="submit" class="dropdown-item">Resume Campaign</button>
                                        </form>
                                    {% endif %}
                                    <form action="{% url 'emailwarmup:update_campaign_status' campaign.id %}" method="post" class="d-inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="status" value="completed">
                                        <button type="submit" class="dropdown-item">Complete Campaign</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Campaign Information</h5>
                                    <div class="table-responsive">
                                        <table class="table">
                                            <tbody>
                                                <tr>
                                                    <th>Status</th>
                                                    <td>
                                                        {% if campaign.status == 'active' %}
                                                            <span class="badge bg-success">Active</span>
                                                        {% elif campaign.status == 'pending' %}
                                                            <span class="badge bg-warning">Pending</span>
                                                        {% elif campaign.status == 'paused' %}
                                                            <span class="badge bg-info">Paused</span>
                                                        {% elif campaign.status == 'completed' %}
                                                            <span class="badge bg-primary">Completed</span>
                                                        {% else %}
                                                            <span class="badge bg-danger">Failed</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Current Week</th>
                                                    <td>
                                                        Week {{ campaign.current_week }}
                                                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" data-bs-toggle="modal" data-bs-target="#updateWeekModal">
                                                            Change
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Start Date</th>
                                                    <td>{{ campaign.start_date }}</td>
                                                </tr>
                                                <tr>
                                                    <th>End Date</th>
                                                    <td>
                                                        {% if campaign.end_date %}
                                                            {{ campaign.end_date }}
                                                        {% else %}
                                                            Not completed
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Description</th>
                                                    <td>
                                                        {% if campaign.description %}
                                                            {{ campaign.description }}
                                                        {% else %}
                                                            No description
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Campaign Statistics</h5>
                                    <div class="row">
                                        <div class="col-6 col-md-4 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    <span class="text-info display-6"><i class="fa fa-envelope"></i></span>
                                                </div>
                                                <div>
                                                    <h3 class="mb-0">{{ emails.count }}</h3>
                                                    <span class="text-muted">Total Emails</span>
                                                </div>
                                            </div>
                                        </div>
                                        {% for status_count in status_counts %}
                                            <div class="col-6 col-md-4 mb-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        {% if status_count.status == 'sent' %}
                                                            <span class="text-success display-6"><i class="fa fa-paper-plane"></i></span>
                                                        {% elif status_count.status == 'pending' %}
                                                            <span class="text-warning display-6"><i class="fa fa-clock"></i></span>
                                                        {% elif status_count.status == 'replied' %}
                                                            <span class="text-primary display-6"><i class="fa fa-reply"></i></span>
                                                        {% else %}
                                                            <span class="text-danger display-6"><i class="fa fa-exclamation-circle"></i></span>
                                                        {% endif %}
                                                    </div>
                                                    <div>
                                                        <h3 class="mb-0">{{ status_count.count }}</h3>
                                                        <span class="text-muted">{{ status_count.status }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Weekly Settings</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Week</th>
                                                    <th>Emails Per Day</th>
                                                    <th>Replies Per Thread</th>
                                                    <th>Min Reply Delay</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for setting in settings %}
                                                    <tr {% if setting.week_number == campaign.current_week %}class="table-primary"{% endif %}>
                                                        <td>Week {{ setting.week_number }}</td>
                                                        <td>{{ setting.min_emails_per_day }} - {{ setting.max_emails_per_day }}</td>
                                                        <td>{{ setting.min_replies_per_thread }} - {{ setting.max_replies_per_thread }}</td>
                                                        <td>{{ setting.min_reply_delay_minutes }} minutes</td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Recent Emails</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Subject</th>
                                                    <th>Trusted Account</th>
                                                    <th>Type</th>
                                                    <th>Status</th>
                                                    <th>Scheduled Time</th>
                                                    <th>Sent Time</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for email in emails %}
                                                    {% if forloop.counter <= 20 %}
                                                        <tr>
                                                            <td>{{ email.subject }}</td>
                                                            <td>{{ email.trusted_account.email_address }}</td>
                                                            <td>
                                                                {% if email.email_type == 'initial' %}
                                                                    <span class="badge bg-primary">Initial</span>
                                                                {% else %}
                                                                    <span class="badge bg-info">Reply</span>
                                                                {% endif %}
                                                            </td>
                                                            <td>
                                                                {% if email.status == 'pending' %}
                                                                    <span class="badge bg-warning">Pending</span>
                                                                {% elif email.status == 'sent' %}
                                                                    <span class="badge bg-success">Sent</span>
                                                                {% elif email.status == 'delivered' %}
                                                                    <span class="badge bg-info">Delivered</span>
                                                                {% elif email.status == 'replied' %}
                                                                    <span class="badge bg-primary">Replied</span>
                                                                {% else %}
                                                                    <span class="badge bg-danger">Failed</span>
                                                                {% endif %}
                                                            </td>
                                                            <td>{{ email.scheduled_time }}</td>
                                                            <td>
                                                                {% if email.sent_time %}
                                                                    {{ email.sent_time }}
                                                                {% else %}
                                                                    -
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                    {% endif %}
                                                {% empty %}
                                                    <tr>
                                                        <td colspan="6" class="text-center">No emails yet</td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Week Modal -->
<div class="modal fade" id="updateWeekModal" tabindex="-1" aria-labelledby="updateWeekModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{% url 'emailwarmup:update_campaign_week' campaign.id %}" method="post">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="updateWeekModalLabel">Update Campaign Week</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="week">Week Number</label>
                        <select class="form-control" id="week" name="week" required>
                            {% for setting in settings %}
                                <option value="{{ setting.week_number }}" {% if setting.week_number == campaign.current_week %}selected{% endif %}>
                                    Week {{ setting.week_number }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Week</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Initialize any charts or other JS functionality here
    document.addEventListener('DOMContentLoaded', function() {
        // Add any JavaScript functionality here
    });
</script>
{% endblock %}