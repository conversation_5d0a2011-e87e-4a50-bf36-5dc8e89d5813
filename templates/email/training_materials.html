{% extends "email/base.html" %}

{% block page_title %}Training Materials{% endblock %}

{% block email_content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h5>Manage Training Materials</h5>
            <a href="{% url 'training_material_add' %}" class="btn btn-primary">Add Training Material</a>
        </div>
    </div>
</div>

<div class="row">
    {% if materials %}
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for material in materials %}
                            <tr>
                                <td>{{ material.name }}</td>
                                <td>{{ material.description|truncatechars:50 }}</td>
                                <td>
                                    {% if material.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ material.date_created|date:"M d, Y" }}</td>
                                <td>{{ material.date_updated|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'training_material_edit' material.id %}" class="btn btn-sm btn-outline-primary">Edit</a>
                                        <a href="{% url 'training_material_delete' material.id %}" class="btn btn-sm btn-outline-danger">Delete</a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <div class="col-md-12">
            <div class="alert alert-info">
                No training materials found. <a href="{% url 'training_material_add' %}">Add a training material</a> to get started.
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
