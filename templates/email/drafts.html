{% extends "email/base.html" %}

{% block page_title %}
    {% if account %}
        Email Drafts for {{ account.account_name }}
    {% else %}
        Email Drafts
    {% endif %}
{% endblock %}

{% block email_content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                {% if accounts %}
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="accountDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            {% if account %}
                                {{ account.account_name }}
                            {% else %}
                                All Accounts
                            {% endif %}
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="accountDropdown">
                            <li><a class="dropdown-item" href="{% url 'email_drafts' %}">All Accounts</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% for acc in accounts %}
                                <li><a class="dropdown-item" href="{% url 'email_drafts_account' acc.id %}">{{ acc.account_name }}</a></li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Subject</th>
                            <th>To</th>
                            <th>Date Created</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for draft in page_obj %}
                            <tr>
                                <td>{{ draft.subject|truncatechars:50 }}</td>
                                <td>{{ draft.recipients }}</td>
                                <td>{{ draft.date_created|date:"M d, Y H:i" }}</td>
                                <td>
                                    {% if draft.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif draft.status == 'generated' %}
                                        <span class="badge bg-info">Generated</span>
                                    {% elif draft.status == 'approved' %}
                                        <span class="badge bg-success">Approved</span>
                                    {% elif draft.status == 'sent' %}
                                        <span class="badge bg-primary">Sent</span>
                                    {% elif draft.status == 'rejected' %}
                                        <span class="badge bg-danger">Rejected</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'email_draft_detail' draft.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                        <a href="{% url 'email_draft_edit' draft.id %}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                        <a href="{% url 'email_draft_send' draft.id %}" class="btn btn-sm btn-outline-success">Send</a>
                                        <a href="{% url 'email_draft_delete' draft.id %}" class="btn btn-sm btn-outline-danger">Delete</a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                            <li class="page-item active"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                        {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                            <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% else %}
            <div class="alert alert-info">
                No drafts found.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
