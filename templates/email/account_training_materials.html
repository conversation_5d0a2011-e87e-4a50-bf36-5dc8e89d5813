{% extends "email/base.html" %}

{% block page_title %}Manage Training Materials for {{ account.account_name }}{% endblock %}

{% block email_content %}
<div class="row">
    <div class="col-md-12">
        <form method="post">
            {% csrf_token %}
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">Select Training Materials</h5>
                </div>
                <div class="card-body">
                    {% if all_materials %}
                        <div class="row">
                            {% for material in all_materials %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="materials" value="{{ material.id }}" id="material-{{ material.id }}" {% if material.id in account_materials %}checked{% endif %}>
                                                <label class="form-check-label" for="material-{{ material.id }}">
                                                    <h6 class="card-title">{{ material.name }}</h6>
                                                </label>
                                            </div>
                                            <p class="card-text">{{ material.description|truncatechars:100 }}</p>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    {% if material.is_active %}
                                                        <span class="badge bg-success">Active</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">Inactive</span>
                                                    {% endif %}
                                                </small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            No training materials found. <a href="{% url 'training_material_add' %}">Add a training material</a> first.
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'email_accounts' %}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
