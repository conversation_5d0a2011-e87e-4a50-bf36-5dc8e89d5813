{% extends "email/base.html" %}

{% block page_title %}
    {% if material %}
        Edit Training Material
    {% else %}
        Add Training Material
    {% endif %}
{% endblock %}

{% block email_content %}
<div class="row">
    <div class="col-md-12">
        <form method="post">
            {% csrf_token %}
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">Training Material Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">Name</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.content.id_for_label }}" class="form-label">Content</label>
                            {{ form.content }}
                            {% if form.content.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.content.errors }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Enter the training material content. This will be used to train the LLM for generating responses.
                            </small>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'training_materials' %}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    {% if material %}
                        Update Training Material
                    {% else %}
                        Add Training Material
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
