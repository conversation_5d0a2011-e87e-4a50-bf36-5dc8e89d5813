{% extends "email/base.html" %}

{% block page_title %}Email Response Dashboard{% endblock %}

{% block email_content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <h5>Email Accounts</h5>
            <a href="{% url 'email_account_add' %}" class="btn btn-primary btn-sm">Add Account</a>
        </div>
    </div>
</div>

<div class="row">
    {% if accounts %}
        {% for account in accounts %}
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ account.account_name }}</h5>
                    <p class="card-text">{{ account.email_address }}</p>
                    <p class="card-text">
                        <small class="text-muted">
                            {% if account.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                            
                            {% if account.auto_process %}
                                <span class="badge bg-info">Auto-Process</span>
                            {% endif %}
                        </small>
                    </p>
                    <div class="btn-group" role="group">
                        <a href="{% url 'email_account_edit' account.id %}" class="btn btn-sm btn-outline-primary">Edit</a>
                        <a href="{% url 'account_training_materials' account.id %}" class="btn btn-sm btn-outline-info">Training</a>
                        <a href="{% url 'email_history_account' account.id %}" class="btn btn-sm btn-outline-secondary">History</a>
                        <a href="{% url 'email_drafts_account' account.id %}" class="btn btn-sm btn-outline-success">Drafts</a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-md-12">
            <div class="alert alert-info">
                No email accounts found. <a href="{% url 'email_account_add' %}">Add an account</a> to get started.
            </div>
        </div>
    {% endif %}
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Recent Drafts</h5>
            </div>
            <div class="card-body">
                {% if drafts %}
                    <div class="list-group">
                        {% for draft in drafts %}
                            <a href="{% url 'email_draft_detail' draft.id %}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ draft.subject }}</h6>
                                    <small>{{ draft.date_created|date:"M d, Y" }}</small>
                                </div>
                                <p class="mb-1">To: {{ draft.recipients }}</p>
                                <small>Status: {{ draft.get_status_display }}</small>
                            </a>
                        {% endfor %}
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'email_drafts' %}" class="btn btn-sm btn-outline-primary">View All Drafts</a>
                    </div>
                {% else %}
                    <p class="text-muted">No drafts found.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Recent Emails</h5>
            </div>
            <div class="card-body">
                {% if emails %}
                    <div class="list-group">
                        {% for email in emails %}
                            <a href="{% url 'email_detail' email.id %}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ email.subject }}</h6>
                                    <small>{{ email.received_date|date:"M d, Y" }}</small>
                                </div>
                                <p class="mb-1">From: {{ email.sender }} &lt;{{ email.sender_email }}&gt;</p>
                                <small>
                                    {% if email.is_processed %}
                                        <span class="badge bg-success">Processed</span>
                                    {% else %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% endif %}
                                    
                                    {% if email.is_replied %}
                                        <span class="badge bg-info">Replied</span>
                                    {% endif %}
                                </small>
                            </a>
                        {% endfor %}
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'email_history' %}" class="btn btn-sm btn-outline-primary">View All Emails</a>
                    </div>
                {% else %}
                    <p class="text-muted">No emails found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Training Materials</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <p>Manage training materials for your email responses.</p>
                    <a href="{% url 'training_materials' %}" class="btn btn-primary btn-sm">Manage Training Materials</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
