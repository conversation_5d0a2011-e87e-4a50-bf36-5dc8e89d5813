{% extends "email/base.html" %}

{% block page_title %}Email Accounts{% endblock %}

{% block email_content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h5>Manage Email Accounts</h5>
            <a href="{% url 'email_account_add' %}" class="btn btn-primary">Add Account</a>
        </div>
    </div>
</div>

<div class="row">
    {% if accounts %}
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Account Name</th>
                            <th>Email Address</th>
                            <th>Status</th>
                            <th>Auto Process</th>
                            <th>Last Checked</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for account in accounts %}
                            <tr>
                                <td>{{ account.account_name }}</td>
                                <td>{{ account.email_address }}</td>
                                <td>
                                    {% if account.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.auto_process %}
                                        <span class="badge bg-info">Enabled</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Disabled</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.last_checked %}
                                        {{ account.last_checked|date:"M d, Y H:i" }}
                                    {% else %}
                                        Never
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'email_account_edit' account.id %}" class="btn btn-sm btn-outline-primary">Edit</a>
                                        <a href="{% url 'account_training_materials' account.id %}" class="btn btn-sm btn-outline-info">Training</a>
                                        <a href="{% url 'email_history_account' account.id %}" class="btn btn-sm btn-outline-secondary">History</a>
                                        <a href="{% url 'email_drafts_account' account.id %}" class="btn btn-sm btn-outline-success">Drafts</a>
                                        <a href="{% url 'email_account_delete' account.id %}" class="btn btn-sm btn-outline-danger">Delete</a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <div class="col-md-12">
            <div class="alert alert-info">
                No email accounts found. <a href="{% url 'email_account_add' %}">Add an account</a> to get started.
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
