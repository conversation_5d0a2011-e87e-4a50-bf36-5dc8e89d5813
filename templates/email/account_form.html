{% extends "layouts/base.html" %}

{% block title %}{% if account %}Edit Email Account{% else %}Add Email Account{% endif %}{% endblock %}

{% block stylesheets %}
<!-- Include Quill stylesheet -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
{% endblock %}

{% block javascripts %}
<!-- Include Quill library -->
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Quill editor
        var quill = new Quill('#quill-editor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    [{ 'font': [] }, { 'size': [] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'script': 'sub'}, { 'script': 'super' }],
                    [{ 'header': 1 }, { 'header': 2 }, 'blockquote', 'code-block'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'indent': '-1'}, { 'indent': '+1' }],
                    [{ 'direction': 'rtl' }, { 'align': [] }],
                    ['link', 'image', 'video'],
                    ['clean']
                ]
            },
            placeholder: 'Compose your HTML signature here...'
        });

        // Set initial content if available
        var signatureHtml = document.getElementById('id_signature_html').value;
        if (signatureHtml) {
            quill.root.innerHTML = signatureHtml;
        }

        // Update hidden input before form submission
        document.querySelector('form').addEventListener('submit', function() {
            document.getElementById('id_signature_html').value = quill.root.innerHTML;
        });
    });
</script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{% if account %}Edit Email Account{% else %}Add Email Account{% endif %}</h4>
                </div>
                <div class="card-body">
                    <!-- Debug message -->
                    <!--<div class="alert alert-info">
                        Debug: Account form template loaded
                    </div>-->
                    <div class="row">
                        <div class="col-md-12">
                            <form method="post">
                                {% csrf_token %}

                                <!-- Simple form display -->
                                <!--<div class="alert alert-warning">
                                    Debug: Form fields: {{ form.fields.keys }}
                                </div>-->

                                <div class="mb-3">
                                    <label for="id_account_name" class="form-label">Account Name</label>
                                    <input type="text" name="account_name" class="form-control" id="id_account_name" value="{% if account %}{{ account.account_name }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="id_email_address" class="form-label">Email Address</label>
                                    <input type="email" name="email_address" class="form-control" id="id_email_address" value="{% if account %}{{ account.email_address }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="id_email_password" class="form-label">Password</label>
                                    <input type="password" name="email_password" class="form-control" id="id_email_password" value="{% if account %}{{ account.email_password }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="id_imap_server" class="form-label">IMAP Server</label>
                                    <input type="text" name="imap_server" class="form-control" id="id_imap_server" value="{% if account %}{{ account.imap_server }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="id_imap_port" class="form-label">IMAP Port</label>
                                    <input type="number" name="imap_port" class="form-control" id="id_imap_port" value="{% if account %}{{ account.imap_port }}{% else %}993{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="id_smtp_server" class="form-label">SMTP Server</label>
                                    <input type="text" name="smtp_server" class="form-control" id="id_smtp_server" value="{% if account %}{{ account.smtp_server }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="id_smtp_port" class="form-label">SMTP Port</label>
                                    <input type="number" name="smtp_port" class="form-control" id="id_smtp_port" value="{% if account %}{{ account.smtp_port }}{% else %}587{% endif %}" required>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" name="imap_use_ssl" class="form-check-input" id="id_imap_use_ssl" {% if not account or account.imap_use_ssl %}checked{% endif %}>
                                    <label class="form-check-label" for="id_imap_use_ssl">IMAP Use SSL</label>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" name="smtp_use_tls" class="form-check-input" id="id_smtp_use_tls" {% if not account or account.smtp_use_tls %}checked{% endif %}>
                                    <label class="form-check-label" for="id_smtp_use_tls">SMTP Use TLS</label>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" name="smtp_use_ssl" class="form-check-input" id="id_smtp_use_ssl" {% if account and account.smtp_use_ssl %}checked{% endif %}>
                                    <label class="form-check-label" for="id_smtp_use_ssl">SMTP Use SSL</label>
                                    <small class="form-text text-muted d-block">Use SSL instead of TLS (for providers like Gmail)</small>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" name="is_active" class="form-check-input" id="id_is_active" {% if not account or account.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="id_is_active">Active</label>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" name="auto_process" class="form-check-input" id="id_auto_process" {% if account and account.auto_process %}checked{% endif %}>
                                    <label class="form-check-label" for="id_auto_process">Auto Process</label>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" name="auto_send" class="form-check-input" id="id_auto_send" {% if account and account.auto_send %}checked{% endif %}>
                                    <label class="form-check-label" for="id_auto_send">Auto Send</label>
                                    <small class="form-text text-muted d-block">Automatically send responses without human review</small>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" name="import_history" class="form-check-input" id="id_import_history" {% if account and account.import_history %}checked{% endif %}>
                                    <label class="form-check-label" for="id_import_history">Import History</label>
                                </div>

                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="card-title">Email Signature</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3 form-check">
                                            <input type="checkbox" name="use_signature" class="form-check-input" id="id_use_signature" {% if not account or account.use_signature %}checked{% endif %}>
                                            <label class="form-check-label" for="id_use_signature">Use Signature</label>
                                        </div>

                                        <div class="mb-3">
                                            <label for="id_signature_text" class="form-label">Text Signature</label>
                                            <textarea name="signature_text" class="form-control" id="id_signature_text" rows="4">{% if account %}{{ account.signature_text }}{% endif %}</textarea>
                                            <small class="form-text text-muted">Plain text version of your signature for non-HTML email clients.</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="id_signature_html" class="form-label">HTML Signature</label>
                                            <div id="quill-editor" style="height: 200px;"></div>
                                            <textarea name="signature_html" class="form-control d-none" id="id_signature_html">{% if account %}{{ account.signature_html }}{% endif %}</textarea>
                                            <small class="form-text text-muted">Rich text signature for HTML email clients. You can add formatting, links, and images.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'email_accounts' %}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">{% if account %}Update Account{% else %}Add Account{% endif %}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
