{% extends "email/base.html" %}

{% block page_title %}Email Detail{% endblock %}

{% block email_content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="{% url 'email_history' %}" class="btn btn-secondary">Back to History</a>
                {% if not email.is_processed %}
                    <a href="{% url 'generate_response' email.id %}" class="btn btn-success">Generate Response</a>
                {% endif %}
            </div>
            <div>
                <span class="badge bg-secondary">{{ email.email_account.account_name }}</span>
                {% if email.is_processed %}
                    <span class="badge bg-success">Processed</span>
                {% else %}
                    <span class="badge bg-warning">Pending</span>
                {% endif %}
                
                {% if email.is_replied %}
                    <span class="badge bg-info">Replied</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">{{ email.subject }}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>From:</strong> {{ email.sender }} &lt;{{ email.sender_email }}&gt;
                    </div>
                    <div class="col-md-6 text-end">
                        <strong>Date:</strong> {{ email.received_date|date:"M d, Y H:i" }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <strong>To:</strong> {{ email.recipients }}
                    </div>
                </div>
                {% if email.cc %}
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>CC:</strong> {{ email.cc }}
                        </div>
                    </div>
                {% endif %}
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        {% if email.body_html %}
                            <div class="email-html-content">
                                <iframe srcdoc="{{ email.body_html|safe }}" style="width: 100%; height: 500px; border: 1px solid #ddd;"></iframe>
                            </div>
                        {% else %}
                            <div class="email-text-content">
                                <pre style="white-space: pre-wrap;">{{ email.body_text }}</pre>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if email.drafts.all %}
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Generated Responses</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for draft in email.drafts.all %}
                            <a href="{% url 'email_draft_detail' draft.id %}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ draft.subject }}</h6>
                                    <small>{{ draft.date_created|date:"M d, Y H:i" }}</small>
                                </div>
                                <p class="mb-1">Status: {{ draft.get_status_display }}</p>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
