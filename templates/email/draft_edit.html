{% extends "email/base.html" %}

{% block page_title %}Edit Draft{% endblock %}

{% block email_content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="{% url 'email_draft_detail' draft.id %}" class="btn btn-secondary">Back to Draft</a>
            </div>
            <div>
                <span class="badge bg-secondary">{{ draft.email_account.account_name }}</span>
                {% if draft.status == 'pending' %}
                    <span class="badge bg-warning">Pending</span>
                {% elif draft.status == 'generated' %}
                    <span class="badge bg-info">Generated</span>
                {% elif draft.status == 'approved' %}
                    <span class="badge bg-success">Approved</span>
                {% elif draft.status == 'sent' %}
                    <span class="badge bg-primary">Sent</span>
                {% elif draft.status == 'rejected' %}
                    <span class="badge bg-danger">Rejected</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <form method="post">
            {% csrf_token %}
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">Edit Draft</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" value="{{ draft.subject }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="recipients" class="form-label">To</label>
                            <input type="text" class="form-control" id="recipients" name="recipients" value="{{ draft.recipients }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="cc" class="form-label">CC</label>
                            <input type="text" class="form-control" id="cc" name="cc" value="{{ draft.cc }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="bcc" class="form-label">BCC</label>
                            <input type="text" class="form-control" id="bcc" name="bcc" value="{{ draft.bcc }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="body_text" class="form-label">Body</label>
                            <textarea class="form-control" id="body_text" name="body_text" rows="15">{{ draft.body_text }}</textarea>
                        </div>
                    </div>
                    {% if draft.body_html %}
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="body_html" class="form-label">HTML Body</label>
                                <textarea class="form-control" id="body_html" name="body_html" rows="15">{{ draft.body_html }}</textarea>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'email_draft_detail' draft.id %}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
