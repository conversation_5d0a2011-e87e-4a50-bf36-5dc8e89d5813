{% extends "email/base.html" %}

{% block page_title %}
    {% if account %}
        Email History for {{ account.account_name }}
    {% else %}
        Email History
    {% endif %}
{% endblock %}

{% block email_content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                {% if accounts %}
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="accountDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            {% if account %}
                                {{ account.account_name }}
                            {% else %}
                                All Accounts
                            {% endif %}
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="accountDropdown">
                            <li><a class="dropdown-item" href="{% url 'email_history' %}">All Accounts</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% for acc in accounts %}
                                <li><a class="dropdown-item" href="{% url 'email_history_account' acc.id %}">{{ acc.account_name }}</a></li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Subject</th>
                            <th>From</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for email in page_obj %}
                            <tr>
                                <td>{{ email.subject|truncatechars:50 }}</td>
                                <td>{{ email.sender_email }}</td>
                                <td>{{ email.received_date|date:"M d, Y H:i" }}</td>
                                <td>
                                    {% if email.is_processed %}
                                        <span class="badge bg-success">Processed</span>
                                    {% else %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% endif %}
                                    
                                    {% if email.is_replied %}
                                        <span class="badge bg-info">Replied</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'email_detail' email.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                        {% if not email.is_processed %}
                                            <a href="{% url 'generate_response' email.id %}" class="btn btn-sm btn-outline-success">Generate Response</a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                            <li class="page-item active"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                        {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                            <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% else %}
            <div class="alert alert-info">
                No emails found.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
