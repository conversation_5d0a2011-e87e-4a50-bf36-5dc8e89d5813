{% extends "layouts/base.html" %}

{% block title %} Dashboard {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link href="/assets-auth/plugins/custom/vis-timeline/vis-timeline.bundle.css" rel="stylesheet" type="text/css" />
{% endblock stylesheets %}

{% block content %}


<div class="d-flex flex-column flex-column-fluid container-fluid ps-0-important">

    <div class="content flex-column-fluid" id="kt_content">



        <div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
            <!--begin::Page title-->
            <div class="page-title d-flex flex-column me-3">
                <!--begin::Title-->
                <h1 class="d-flex text-dark fw-bold my-1 fs-3">
                    <i class="ki-duotone ki-calendar fs-2 me-2">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                        <span class="path4"></span>
                    </i>
                    Jan 1, 2023 - Feb 1, 2023
                </h1>
                <!--end::Title-->
            </div>
            <!--end::Page title-->
        </div>



        <div class="row g-5 g-xl-8">

            <!--begin::Col-->
            <div class="col-xl-6">
                <!--begin::Card widget 1-->
                <div class="card card-flush border-0 h-xl-100" data-bs-theme="light" style="background-color: #22232B">
                    <!--begin::Header-->
                    <div class="card-header pt-2">
                        <!--begin::Title-->
                        <h3 class="card-title">
                            <span class="text-white fs-3 fw-bold me-2">Web Traffic</span>
                            <!--
                            <span class="badge badge-success">Active</span>
                            -->
                        </h3>
                        <!--end::Title-->
                        <!--begin::Toolbar-->
                        <div class="card-toolbar">
                            <!--begin::Menu-->
                            <button class="btn btn-sm bg-white bg-opacity-10 btn-color-white btn-active-success">
                                View Report
                                <i class="ki-duotone ki-document fs-5 pe-0">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </button>
                            <!--end::Menu-->
                        </div>
                        <!--end::Toolbar-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Body-->
                    <div class="card-body d-flex justify-content-between flex-column pt-1 px-0 pb-0">
                        <!--begin::Wrapper-->
                        <div class="d-flex flex-wrap px-9 mb-5">
                            <!--begin::Stat-->
                            <div class="rounded min-w-125px py-3 px-4 my-1 me-6" style="border: 1px dashed rgba(255, 255, 255, 0.15)">
                                <!--begin::Number-->
                                <div class="d-flex align-items-center">
                                    <div class="text-white fs-2 fw-bold" data-kt-countup="true" data-kt-countup-value="4368" data-kt-countup-prefix="">0</div>
                                </div>
                                <!--end::Number-->
                                <!--begin::Label-->
                                <div class="fw-semibold fs-6 text-white opacity-50">Bounce Rate</div>
                                <!--end::Label-->
                            </div>
                            <!--end::Stat-->
                            <!--begin::Stat-->
                            <div class="rounded min-w-125px py-3 px-4 my-1" style="border: 1px dashed rgba(255, 255, 255, 0.15)">
                                <!--begin::Number-->
                                <div class="d-flex align-items-center">
                                    <div class="text-white fs-2 fw-bold" data-kt-countup="true" data-kt-countup-value="120,000">0</div>
                                </div>
                                <!--end::Number-->
                                <!--begin::Label-->
                                <div class="fw-semibold fs-6 text-white opacity-50">Session</div>
                                <!--end::Label-->
                            </div>
                            <!--end::Stat-->
                        </div>
                        <!--end::Wrapper-->
                        <!--begin::Chart-->
                        <div id="kt_card_widget_1_chart" data-kt-chart-color="primary" style="height: 105px"></div>
                        <!--end::Chart-->
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Card widget 1-->
            </div>
            <!--end::Col-->










            <!--begin::Col-->
            <div class="col-xl-6 mb-0">
                <!--begin::Chart widget 30-->
                <div class="card card-flush h-xl-100">
                    <!--begin::Header-->
                    <div class="card-header pt-2 mb-0">
                        <!--begin::Title-->
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold text-gray-800 fs-3">Brand Growth</span>
                            <!--<span class="text-gray-400 mt-1 fw-semibold fs-6">22% Increase</span>-->
                        </h3>
                        <!--end::Title-->
                        <!--begin::Toolbar-->
                        <div class="card-toolbar">
                            <a href="" class="btn btn-sm btn-light">
                                View Report
                                <i class="ki-duotone ki-document fs-5 pe-0">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </a>
                        </div>
                        <!--end::Toolbar-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Body-->
                    <div class="card-body d-flex justify-content-between flex-column pt-0">
                        <!--begin::Items-->
                        <div class="d-flex flex-wrap d-grid gap-5 mb-5">
                            <!--begin::Item-->
                            <div class="pe-xxl-7 me-xxl-5">
                                <!--begin::Statistics-->
                                <div class="d-flex mb-2">
                                    <span class="fs-3 fw-bold text-gray-800 me-2 lh-1 ls-n2">22% Increase in people search for your brand name</span>
                                </div>
                                <!--end::Statistics-->
                            </div>
                            <!--end::Item-->
                        </div>
                        <!--end::Items-->
                        <!--begin::Chart container-->
                        <div id="kt_charts_widget_30_chart2" class="w-100 h-200px"></div>
                        <!--end::Chart container-->
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Chart widget 30-->
            </div>
            <!--end::Col-->











            <div class="col-xl-6">
                <!--begin::List Widget 3-->
                <div class="card card-xl-stretch mb-5 mb-xl-8">
                    <!--begin::Header-->
                    <div class="card-header border-0">
                        <h3 class="card-title fw-bold text-dark fs-3">Action items to build brand</h3>

                        <!--begin::Toolbar-->
                        <div class="card-toolbar">
                            <a href="" class="btn btn-sm btn-light">
                                See more details
                                <i class="ki-duotone ki-arrow-right fs-5 pe-0">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </a>
                        </div>
                        <!--end::Toolbar-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Body-->
                    <div class="card-body pt-2">
                        <!--begin::Item-->
                        <div class="d-flex align-items-center mb-8">
                            <!--begin::Bullet-->
                            <span class="bullet bullet-vertical h-40px bg-success"></span>
                            <!--end::Bullet-->
                            <!--begin::Checkbox-->
                            <div class="form-check form-check-custom form-check-solid mx-5">
                                <input class="form-check-input" type="checkbox" value="" />
                            </div>
                            <!--end::Checkbox-->
                            <!--begin::Description-->
                            <div class="flex-grow-1">
                                <a href="#" class="text-gray-800 text-hover-primary fw-bold fs-6">Item #1</a>
                                <span class="text-muted fw-semibold d-block">AI suggestions based on stats and modules</span>
                            </div>
                            <!--end::Description-->
                        </div>
                        <!--end:Item-->
                        <!--begin::Item-->
                        <div class="d-flex align-items-center mb-8">
                            <!--begin::Bullet-->
                            <span class="bullet bullet-vertical h-40px bg-primary"></span>
                            <!--end::Bullet-->
                            <!--begin::Checkbox-->
                            <div class="form-check form-check-custom form-check-solid mx-5">
                                <input class="form-check-input" type="checkbox" value="" />
                            </div>
                            <!--end::Checkbox-->
                            <!--begin::Description-->
                            <div class="flex-grow-1">
                                <a href="#" class="text-gray-800 text-hover-primary fw-bold fs-6">Item #1</a>
                                <span class="text-muted fw-semibold d-block">AI suggestions based on stats and modules</span>
                            </div>
                            <!--end::Description-->
                        </div>
                        <!--end:Item-->
                        <!--begin::Item-->
                        <div class="d-flex align-items-center mb-8">
                            <!--begin::Bullet-->
                            <span class="bullet bullet-vertical h-40px bg-warning"></span>
                            <!--end::Bullet-->
                            <!--begin::Checkbox-->
                            <div class="form-check form-check-custom form-check-solid mx-5">
                                <input class="form-check-input" type="checkbox" value="" />
                            </div>
                            <!--end::Checkbox-->
                            <!--begin::Description-->
                            <div class="flex-grow-1">
                                <a href="#" class="text-gray-800 text-hover-primary fw-bold fs-6">Item #1</a>
                                <span class="text-muted fw-semibold d-block">AI suggestions based on stats and modules</span>
                            </div>
                            <!--end::Description-->
                        </div>
                        <!--end:Item-->
                        <!--begin::Item-->
                        <div class="d-flex align-items-center mb-8">
                            <!--begin::Bullet-->
                            <span class="bullet bullet-vertical h-40px bg-danger"></span>
                            <!--end::Bullet-->
                            <!--begin::Checkbox-->
                            <div class="form-check form-check-custom form-check-solid mx-5">
                                <input class="form-check-input" type="checkbox" value="" />
                            </div>
                            <!--end::Checkbox-->
                            <!--begin::Description-->
                            <div class="flex-grow-1">
                                <a href="#" class="text-gray-800 text-hover-primary fw-bold fs-6">Item #1</a>
                                <span class="text-muted fw-semibold d-block">AI suggestions based on stats and modules</span>
                            </div>
                            <!--end::Description-->
                        </div>
                        <!--end:Item-->
                        <!--begin::Item-->
                        <div class="d-flex align-items-center">
                            <!--begin::Bullet-->
                            <span class="bullet bullet-vertical h-40px bg-success"></span>
                            <!--end::Bullet-->
                            <!--begin::Checkbox-->
                            <div class="form-check form-check-custom form-check-solid mx-5">
                                <input class="form-check-input" type="checkbox" value="" />
                            </div>
                            <!--end::Checkbox-->
                            <!--begin::Description-->
                            <div class="flex-grow-1">
                                <a href="#" class="text-gray-800 text-hover-primary fw-bold fs-6">Item #1</a>
                                <span class="text-muted fw-semibold d-block">AI suggestions based on stats and modules</span>
                            </div>
                            <!--end::Description-->
                        </div>
                        <!--end:Item-->

                    </div>
                    <!--end::Body-->
                </div>
                <!--end:List Widget 3-->
            </div>








            <div class="col-xl-6">

                <div class="card card-flush">
                    <!--begin::Header-->
                    <div class="card-header pt-2 mb-0">
                        <!--begin::Title-->
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold text-gray-800 fs-3">Return on Investment</span>
                            <!--<span class="text-gray-400 mt-1 fw-semibold fs-6">22% Increase</span>-->
                        </h3>
                        <!--begin::Toolbar-->
                        <div class="card-toolbar">
                            <a href="" class="btn btn-sm btn-light">
                                See more details
                                <i class="ki-duotone ki-arrow-right fs-5 pe-0">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </a>
                        </div>
                        <!--end::Toolbar-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Body-->
                    <div class="card-body pt-2">

                        <!--begin::Row-->
                        <div class="row p-0 mb-5 px-9">
                            <!--begin::Col-->
                            <div class="col">
                                <div class="border border-dashed border-gray-300 text-center min-w-125px rounded pt-4 pb-2 my-3">
                                    <span class="fs-4 fw-semibold text-primary d-block">Conversion Rate</span>
                                    <span class="fs-2hx fw-bold text-gray-900" data-kt-countup="true" data-kt-countup-value="3.2" data-kt-countup-suffix="%">0</span>
                                </div>
                            </div>
                            <!--end::Col-->
                            <!--begin::Col-->
                            <div class="col">
                                <div class="border border-dashed border-gray-300 text-center min-w-125px rounded pt-4 pb-2 my-3">
                                    <span class="fs-4 fw-semibold text-danger d-block">Cost per conversion</span>
                                    <span class="fs-2hx fw-bold text-gray-900" data-kt-countup="true" data-kt-countup-value="34" data-kt-countup-prefix="$">0</span>
                                </div>
                            </div>
                            <!--end::Col-->
                        </div>
                        <!--end::Row-->

                    </div>
                    <!--end::Body-->

                </div>




                <!--begin::List Widget 3-->
                <div class="card mb-5 mb-xl-8 mt-8">
                    <!--begin::Body-->
                    <div class="card-body p-0">
                        <div class="">
                            <!--begin::Notice-->
                            <div class="notice d-flex bg-light-primary rounded border-primary border border-dashed flex-shrink-0 p-6">
                                <!--begin::Icon-->
                                <i class="ki-duotone ki-technology-2 fs-2tx text-primary me-4">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                <!--end::Icon-->
                                <!--begin::Wrapper-->
                                <div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
                                    <!--begin::Content-->
                                    <div class="mb-3 mb-md-0 fw-semibold">
                                        <h4 class="text-gray-900 fw-bold">Total AIR Used:  987/1000</h4>
                                        <div class="fs-6 text-gray-700 pe-7">What is AI and how is it calculated? <a href="#">Learn more here</a></div>
                                    </div>
                                    <!--end::Content-->
                                    <!--begin::Action-->
                                    <a href="#" class="btn btn-primary px-6 align-self-center text-nowrap">Upgrade</a>
                                    <!--end::Action-->
                                </div>
                                <!--end::Wrapper-->
                            </div>
                            <!--end::Notice-->
                        </div>

                    </div>

                </div>

            </div>





    </div>

</div>




{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/assets-auth/plugins/custom/vis-timeline/vis-timeline.bundle.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/map.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldLow.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/continentsLow.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/usaLow.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldTimeZonesLow.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldTimeZoneAreasLow.js"></script>
    <script>
        am5.ready(function() {

        // Create root element
        // https://www.amcharts.com/docs/v5/getting-started/#Root_element
        var root = am5.Root.new("kt_charts_widget_30_chart2");

        // Set themes
        // https://www.amcharts.com/docs/v5/concepts/themes/
        root.setThemes([
          am5themes_Animated.new(root)
        ]);

        // Create chart
        // https://www.amcharts.com/docs/v5/charts/radar-chart/
        var chart = root.container.children.push(
          am5radar.RadarChart.new(root, {
            panX: false,
            panY: false,
            startAngle: 180,
            endAngle: 360
          })
        );

        chart.getNumberFormatter().set("numberFormat", "#'%'");

        // Create axis and its renderer
        // https://www.amcharts.com/docs/v5/charts/radar-chart/gauge-charts/#Axes
        var axisRenderer = am5radar.AxisRendererCircular.new(root, {
          innerRadius: -40
        });

        axisRenderer.grid.template.setAll({
          stroke: root.interfaceColors.get("background"),
          visible: true,
          strokeOpacity: 0.8
        });

        var xAxis = chart.xAxes.push(
          am5xy.ValueAxis.new(root, {
            maxDeviation: 0,
            min: 0,
            max: 100,
            strictMinMax: true,
            renderer: axisRenderer
          })
        );

        // Add clock hand
        // https://www.amcharts.com/docs/v5/charts/radar-chart/gauge-charts/#Clock_hands
        var axisDataItem = xAxis.makeDataItem({});

        var clockHand = am5radar.ClockHand.new(root, {
          pinRadius: 22,
          radius: am5.percent(100),
          innerRadius: 22,
          bottomWidth: 0,
          topWidth: 0
        });

        clockHand.pin.setAll({
          fillOpacity: 0,
          strokeOpacity: 0.5,
          stroke: am5.color(0x000000),
          strokeWidth: 1,
          strokeDasharray: [2, 2]
        });
        clockHand.hand.setAll({
          fillOpacity: 0,
          strokeOpacity: 0.5,
          stroke: am5.color(0x000000),
          strokeWidth: 0.5
        });

        var bullet = axisDataItem.set(
          "bullet",
          am5xy.AxisBullet.new(root, {
            sprite: clockHand
          })
        );

        xAxis.createAxisRange(axisDataItem);

        var label = chart.radarContainer.children.push(
          am5.Label.new(root, {
            centerX: am5.percent(50),
            textAlign: "center",
            centerY: am5.percent(50),
            fontSize: "1.5em"
          })
        );

        axisDataItem.set("value", 22);
        bullet.get("sprite").on("rotation", function () {
          var value = axisDataItem.get("value");
          label.set("text", Math.round(value).toString() + "%");
        });

        chart.bulletsContainer.set("mask", undefined);

        var colorSet = am5.ColorSet.new(root, {});

        var axisRange0 = xAxis.createAxisRange(
          xAxis.makeDataItem({
            above: true,
            value: 0,
            endValue: 22
          })
        );

        axisRange0.get("axisFill").setAll({
          visible: true,
          fill: colorSet.getIndex(0)
        });

        axisRange0.get("label").setAll({
          forceHidden: true
        });

        var axisRange1 = xAxis.createAxisRange(
          xAxis.makeDataItem({
            above: true,
            value: 22,
            endValue: 100
          })
        );

        axisRange1.get("axisFill").setAll({
          visible: true,
          fill: colorSet.getIndex(4)
        });

        axisRange1.get("label").setAll({
          forceHidden: true
        });

        // Make stuff animate on load
        chart.appear(1000, 100);

        }); // end am5.ready()
    </script>
{% endblock javascripts %}