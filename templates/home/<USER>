{% extends "layouts/base_site.html" %}
{% load humanize %}
{% block title %} Demo Chatbot {% endblock %}

{% block optional_expand_button %}
    <div class="expand-btn-grp @@display-class">
        <button class="bg-solid-primary popup-dashboardright-btn">
            <i class="fa-sharp fa-regular fa-sidebar-flip"></i>
        </button>
    </div>
{% endblock optional_expand_button %}

{% block content %}
    <style>
    .chat-content h6 {
        margin-top: 0px !important;
    }
    </style>


    <!-- Dashboard Center Content -->
    <div class="rbt-dashboard-content">
        <div class="content-page">

            <div class="chat-box-section">
                <div class="chat-top-bar">
                    <div class="section-title">
                        <div class="icon">
                            <img src="/static/assets-site/images/icons/document-file.png" alt="">
                        </div>
                        <h6 class="title">Silo AI chat bot demo</h6>
                    </div>
                    <!--
                    <div class="dropdown history-box-dropdown">
                        <button type="button" class="more-info-icon dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa-regular fa-ellipsis"></i>
                        </button>
                        <ul class="dropdown-menu style-one">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fa-sharp fa-solid fa-arrows-rotate"></i>
                                    Regenerate
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fa-sharp fa-solid fa-tag"></i>
                                    Pin Chat
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fa-solid fa-file-lines"></i>
                                    Rename
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fa-solid fa-share-nodes"></i>
                                    Share
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item delete-item" href="#">
                                    <i class="fa-solid fa-trash-can"></i>
                                    Delete Chat
                                </a>
                            </li>
                        </ul>
                    </div>
                    -->
                </div>

                <div class="chat-box-list pt--30 xvh-100" id="chatContainer" style="min-height: calc(100vh - 280px);">



                    <!-- Image Generator -->
                    <div class="chat-box ai-speech">
                        <div class="inner">

                            <div class="chat-section">
                                <div class="author">
                                    <img class="w-100 radius" src="/static/assets-site/images/team/bot.png" alt="Author">
                                </div>
                                <div class="chat-content">
                                    <h6 class="title">Silo AI</h6>
                                    <p>
                                        Welcome to the Silo AI chat bot demo.  How can I help you?
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- Image Generator -->
                    <!--
                    <div class="chat-box author-speech">
                        <div class="inner d-flex justify-content-end">

                            <div class="chat-section xms-auto">
                                <div class="xchat-content text-end me-3">
                                    <h6 class="title mb-2">User</h6>
                                    <p class="mb-3">
                                        Please, Make 4 variant of this image Quickly As Soon As possible
                                    </p>

                                    <pre><code class="language-none">My balls is hot</code></pre>

                                    <h1>H1 Headline Text Example</h1>
                                    <h2>H2 Headline Text Example</h2>
                                    <h3>H3 Headline Text Example</h3>
                                    <h4>H4 Headline Text Example</h4>
                                    <h5>H5 Headline Text Example</h5>
                                    <h6>H6 Headline Text Example</h6>

                                    <div class="img-box-grp mt--25 mb--20">
                                        <div class="img-box">
                                            <img class="radius" src="/static/assets-site/images/generator-img/photo-15.png" alt="Image Generation">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    -->

                    <!-- Image Generator -->
                    <!--
                    <div class="chat-box author-speech ">
                        <div class="inner">

                            <div class="chat-section">
                                <div class="author">
                                    <img class="w-100 radius" src="/static/assets-site/images/team/bot.png" alt="Author">
                                </div>
                                <div class="chat-content">
                                    <h6 class="title">Silo AI</h6>
                                    <p>
                                        Please, Make 4 variant of this image Quickly As Soon As possible
                                    </p>

                                    <div class="chat-section generate-section">
                                        <div class="author">
                                            <img src="/static/assets-site/images/icons/loader-one.gif" alt="Loader Images">
                                        </div>
                                    </div>

                                    <div class="img-box-grp mt--25 mb--20">
                                        <div class="img-box">
                                            <img class="radius" src="/static/assets-site/images/generator-img/photo-15.png" alt="Image Generation">
                                        </div>
                                    </div>

                                    <div class="img-box xl-size mb--20">
                                        <img class="radius" src="/static/assets-site/images/generator-img/photo-19.png" alt="Image Generation">
                                        <button class="download-btn btn-default btn-small bg-solid-primary">
                                            <i class="fa-sharp fa-regular fa-download"></i>
                                            <span>Download</span>
                                        </button>
                                    </div>

                                    <div class="reaction-section">
                                        <div class="btn-grp">
                                            <div class="left-side-btn dropup">
                                                <button data-bs-toggle="modal" data-bs-target="#likeModal" class="react-btn btn-default btn-small btn-border">
                                                    <i class="fa-sharp fa-regular fa-thumbs-up"></i>
                                                </button>
                                                <button data-bs-toggle="modal" data-bs-target="#dislikeModal" class="react-btn btn-default btn-small btn-border">
                                                    <i class="fa-sharp fa-regular fa-thumbs-down"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>




                                </div>
                            </div>
                        </div>
                    </div>
                    -->




                </div>

                <div class="rbt-static-bar">
                    <form class="new-chat-form border-gradient">
                        <textarea id="chatForm" rows="1" placeholder="Send a message...">{% if prompt %}{{ prompt }}{% endif %}</textarea>
                        <div class="left-icons">
                            <div title="AiWave" class="form-icon icon-gpt">
                                <i class="fa-sharp xfa-regular fas fa-terminal"></i>
                            </div>
                        </div>
                        <div class="right-icons">
                            <div class="form-icon icon-plus" id="fileButton" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-custom-class="custom-tooltip" data-bs-title="Choose File">
                                <input type="file" id="inputFile" class="input-file" name="myfile" multiple>
                                <i class="fa-sharp fa-regular fa-paperclip"></i>
                            </div>
                            <!--
                            <button type="button" class="form-icon icon-mic" id="audioButton" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-custom-class="custom-tooltip" data-bs-title="Voice Search">
                                <i class="fa-regular fa-microphone"></i>
                            </button>
                            -->
                            <button type="button" class="form-icon icon-send" id="sendButton" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-custom-class="custom-tooltip" data-bs-title="Send message">
                                <i class="fa-sharp fa-solid fa-paper-plane-top"></i>
                            </button>
                        </div>
                    </form>
                    <p class="b3 small-text">This virtual assistant is for demonstration purposes only.</p>
                </div>
            </div>
        </div>
    </div>
























    <!-- Dashboard Right Content -->
    <div class="rbt-right-side-panel popup-dashboardright-section collapsed">

        <!--
        <div class="right-side-top">
            <a class="btn-default bg-solid-primary" data-bs-toggle="modal" data-bs-target="#newchatModal">
                <span class="icon"><i class="fa-sharp fa-regular fa-circle-plus"></i></span>
                <span>New Chat 2</span>
            </a>
        </div>
        -->
        <div class="right-side-bottom">
            <!--
            <div class="small-search search-section mb--20">
                <input type="search" placeholder="Search Here...">
                <i class="fa-sharp fa-regular fa-search"></i>
            </div>
            -->

            <div class="chat-history-section has-show-more">
                <h6 class="title">You’ve Just Experienced a Silo AI Demo</h6>
                <p>This panel demonstrates how Silo AI’s virtual assistants can provide detailed, interactive content tailored to your needs. Imagine your business using this feature to:</p>

                <ul>
                    <li>Display customer information, product details, or real-time updates.</li>
                    <li>Showcase forms for scheduling appointments or completing transactions.</li>
                    <li>Engage users with personalized, dynamic content in real-time.</li>
                </ul>

                <p>Silo AI goes beyond standard chatbots by integrating directly with your systems, cutting costs by up to 73%, and boosting sales and customer satisfaction.</p>

                <p><b>Ready to see more? Schedule a free, no-obligation call today to learn how Silo AI can revolutionize your business. Plus, receive $2,000 in free AI tools just for meeting with us!</b></p>

                <a href="{% url 'home_demo_book' %}" target="_blank" class="btn-default bg-solid-primary">
                    <span class="icon"><i class="fa-sharp fa-regular fa-circle-plus"></i></span>
                    <span>Click to Schedule</span>
                </a>
                <div class="rbt-show-more-btn">Show More</div>
            </div>


        </div>
    </div>





















{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}

<script>

    /*
    let messages = $('.page-wrapper'),
    d, h, m,
    i = 0;
     */

    $(function() {
        {% if prompt %}
        fetchData();
        {% endif %}


        $('.popup-dashboardright-btn').addClass('collapsed');
        $('.popup-dashboardright-section').addClass('collapsed');

        updateScrollbar();

        let replaceUrlsWithLinks = (text) => {
            let urlPattern = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;

            return text.replace(urlPattern, function(url) {
                return '<a href="' + url + '" target="_blank">' + url + '</a>';
            });
        }


        /*
         * The AI puts tokens in text where HTML should be.  Fix.
         * We also like to make it more readable by putting headings and bolding.
         */
        let convertToHTML = (text) => {
            let isCodeTagOpen = false;
            text = text.replace(/```/g, () => {
                isCodeTagOpen = !isCodeTagOpen;
                return isCodeTagOpen ? '<pre><code class="language-none">' : '</code></pre>';
            });

            text = replaceUrlsWithLinks(text);

            // Match lines ending with two <br /> tags and not ending with punctuation or : or !
            const regex = /([^.:!?])<br \/><br \/>/g;
            text = text.replace(regex, '<h6>$1</h6><br /><br />');

            // Match lines that start with text (without punctuation) followed by a colon
            const regexStartsWithColon = /^([a-zA-Z0-9\s]+):/gm;
            text = text.replace(regexStartsWithColon, '<b>$1</b>:');

            const regexBoldWords = /\*\*(.*?)\*\*/g;
            text = text.replace(regexBoldWords, '<b>$1</b>');

            const regexItalicsWords = /\*(.*?)\*/g;
            text = text.replace(regexItalicsWords, '<i>$1</i>');

            // Remove all newlines
            return text.replace(/\n/g, '');
        };

        function downloadImage(imageId) {
            // Get the image element by its unique ID
            const imgElement = document.getElementById(imageId);

            if (imgElement) {
                // Create an anchor element
                const link = document.createElement('a');
                link.href = imgElement.src; // Set image source as the link
                link.download = `downloaded-${imageId}.png`; // Generate a unique filename based on the image ID

                // Trigger the download
                link.click();
            } else {
                console.error(`Image with ID "${imageId}" not found.`);
            }
        }

        let inH4Tag = false;
        let inH3Tag = false;
        let inH2Tag = false;
        let inH1Tag = false;
        let inBTag = false;
        let inITag = false;
        let inUTag = false;
        let inCodeTag = false;
        let tempBuffer = '';
        let newlineCount = 0;
        let imageCount = 1;

        function printVars() {
            console.log({
                inH4Tag,
                inH3Tag,
                inH2Tag,
                inH1Tag,
                inBTag,
                inITag,
                inUTag,
                inCodeTag,
                tempBuffer,
                newlineCount
            });
        }

        function processIncomingFragment2(fragment) {
            console.log(fragment);
            printVars();

            if (fragment === '.' && newlineCount > 0) {
                return '';
            }

            if (fragment === '\n') {
                newlineCount += 1;
            } else if (fragment === '\n\n') {
                newlineCount += 2;
            } else {
                newlineCount = 0;
            }

            let returnFragment = fragment;

            if (returnFragment.includes('---')) {
                returnFragment = returnFragment.replace('---', '<hr />');
            }

            // Handle `####` headings. H4 tags.
            if (returnFragment.includes('####') && !inH4Tag) {
                const headingContent = returnFragment.replace('####', '<h4>').trim();
                tempBuffer = `${headingContent}`;
                inH4Tag = true;
                return '';
            }
            if (inH4Tag && !returnFragment.includes('\n')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inH4Tag && returnFragment.includes('\n')) {
                //returnFragment = returnFragment.replace('\n', '<br />');
                tempBuffer += returnFragment + '</h4>';
                returnFragment = tempBuffer.replace(/\n/g, '<br />');
                tempBuffer = '';
                inH4Tag = false;
                return returnFragment;
            }

            // Handle `###` headings. H3 tags.
            if (returnFragment.includes('###') && !inH3Tag) {
                const headingContent = returnFragment.replace('###', '<h3>').trim();
                tempBuffer = `${headingContent}`;
                inH3Tag = true;
                return '';
            }
            if (inH3Tag && !returnFragment.includes('\n')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inH3Tag && returnFragment.includes('\n')) {
                //returnFragment = returnFragment.replace('\n', '<br />');
                tempBuffer += returnFragment + '</h3>';
                returnFragment = tempBuffer.replace(/\n/g, '<br />');
                tempBuffer = '';
                inH3Tag = false;
                return returnFragment;
            }

            // Handle `##` headings. H2 tags.
            if (returnFragment.includes('##') && !inH2Tag) {
                const headingContent = returnFragment.replace('##', '<h2>').trim();
                tempBuffer = `${headingContent}`;
                inH2Tag = true;
                return '';
            }
            if (inH2Tag && !returnFragment.includes('\n')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inH2Tag && returnFragment.includes('\n')) {
                //returnFragment = returnFragment.replace('\n', '<br />');
                tempBuffer += returnFragment + '</h2>';
                returnFragment = tempBuffer.replace(/\n/g, '<br />');
                tempBuffer = '';
                inH2Tag = false;
                return returnFragment;
            }

            // Handle `#` headings. H1 tags.
            if (returnFragment.includes('#') && !inH1Tag) {
                const headingContent = returnFragment.replace('#', '<h1>').trim();
                tempBuffer = `${headingContent}`;
                inH1Tag = true;
                return '';
            }
            if (inH1Tag && !returnFragment.includes('\n')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inH1Tag && returnFragment.includes('\n')) {
                //returnFragment = returnFragment.replace('\n', '<br />');
                tempBuffer += returnFragment + '</h1>';
                returnFragment = tempBuffer.replace(/\n/g, '<br />');
                tempBuffer = '';
                inH1Tag = false;
                return returnFragment;
            }











            // Handle `. **` headings. H6 tags.
            /*
            if (returnFragment.trim().startsWith('**') && newlineCount > 0) {
                const headingContent = returnFragment.replace('**', '').trim();
                tempBuffer = `<h6>${headingContent}`;
                inH6Tag = true;
                return '';
            }
            if (inH6Tag && !returnFragment.includes('**')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inH6Tag && returnFragment.includes('**')) {
                returnFragment = returnFragment.replace('\n', '');
                tempBuffer += returnFragment + '</h6>';
                returnFragment = tempBuffer.replace('\n', '<br />');
                tempBuffer = '';
                inH6Tag = false;
                return returnFragment;
            }
             */

            // Handle ``` headings. CODE tags.
            if (returnFragment.includes('```') && !inCodeTag) {
                const headingContent = returnFragment.replace('```', '').trim();
                tempBuffer = `<pre><code class="language-none">${headingContent}`;
                inCodeTag = true;
                return '';
            }
            if (inCodeTag && !returnFragment.includes('\n')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inCodeTag && returnFragment.includes('\n')) {
                tempBuffer += returnFragment + '</code></pre>';
                returnFragment = tempBuffer.replace(/\n/g, '<br />');
                tempBuffer = '';
                inCodeTag = false;
                return returnFragment;
            }






            // Handle `**` headings. B (bold) tags.
            if (returnFragment.includes('**') && !inBTag) {
                tempBuffer = returnFragment.replace('**', '<b>');
                inBTag = true;
                return '';
            }
            if (inBTag && !returnFragment.includes('**')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inBTag && returnFragment.includes('**')) {
                tempBuffer += returnFragment.replace('**', '</b>');
                returnFragment = tempBuffer.replace(/\n/g, '<br />');
                tempBuffer = '';
                inBTag = false;
                return returnFragment;
            }





            // Handle `*` headings. I (italics) tags.
            if (returnFragment.includes('*') && !inITag) {
                tempBuffer = returnFragment.replace('*', '<i>');
                inITag = true;
                return '';
            }
            if (inITag && !returnFragment.includes('*')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inITag && returnFragment.includes('*')) {
                tempBuffer += returnFragment.replace('*', '</i>');
                returnFragment = tempBuffer.replace(/\n/g, '<br />');
                tempBuffer = '';
                inITag = false;
                return returnFragment;
            }





            // Handle `+` headings. U (underline) tags.
            if (returnFragment.includes('+') && !inUTag) {
                tempBuffer = returnFragment.replace('+', '<u>');
                inUTag = true;
                return '';
            }
            if (inUTag && !returnFragment.includes('+')) {
                tempBuffer += returnFragment;
                return '';
            }
            if (inUTag && returnFragment.includes('+')) {
                tempBuffer += returnFragment.replace('+', '</u>');
                returnFragment = tempBuffer.replace(/\n/g, '<br />');
                tempBuffer = '';
                inUTag = false;
                return returnFragment;
            }











            const imageRegex = /\/uploads\/generated\/([a-z0-9\-]+?)\.png/;
            if (imageRegex.test(returnFragment)) {
                const imageTag = `<div class="img-box xl-size mb--20">
                                        <img class="radius" src="/get_image/${returnFragment}" alt="Image Generation" id="image-${imageCount}">
                                        <button class="download-btn btn-default btn-small bg-solid-primary" onclick="downloadImage('image-${imageCount}')">
                                            <i class="fa-sharp fa-regular fa-download"></i>
                                            <span>Download</span>
                                        </button>
                                    </div>`;
                imageCount += 1;
                return imageTag; // No need to add this to the current line.
            }

            // Handle newlines (replace with <br /> for most cases).
            if (returnFragment.includes('\n')) {
                returnFragment = returnFragment.replace(/\n/g, '<br />');
            }

            console.log('Regular return...');
            console.log(returnFragment);
            return returnFragment;
        }





































        function cleanHeadingsInDiv() {
            // Select the target div
            const div = document.querySelector('.mainChatBubble');

            if (!div) {
                console.error(`Div with selector "${divSelector}" not found.`);
                return;
            }

            // Replace patterns for all heading tags inside the div
            div.innerHTML = div.innerHTML
                .replace(/<h6>\s*\./g, '<h6>') // Handle <h6> . or <h6>.
                .replace(/<h5>\s*\./g, '<h5>') // Handle <h5> . or <h5>.
                .replace(/<h4>\s*\./g, '<h4>') // Handle <h4> . or <h4>.
                .replace(/<h3>\s*\./g, '<h3>') // Handle <h3> . or <h3>.
                .replace(/<h2>\s*\./g, '<h2>') // Handle <h2> . or <h2>.
                .replace(/<h1>\s*\./g, '<h1>'); // Handle <h1> . or <h1>.
        }













        async function fetchData() {
            // updateScrollbar();

            // Disable buttons and show spinner
            $("#fileButton").prop('disabled', true);
            $("#audioButton").prop('disabled', true);
            $("#sendButton").prop('disabled', true);
            $('#sendButton i').removeClass('fa-paper-plane-top').addClass('fa-spin fa-spinner');

            // Get the text input and clear the input field
            let userMessage = $('#chatForm').val();
            $('#chatForm').val('');

            // Create FormData for the request
            const formData = new FormData();
            formData.append('prompt', userMessage);
            formData.append('csrfmiddlewaretoken', '{{ csrf_token }}'); // CSRF token for Django

            // Check if an image is selected
            const fileInput = document.querySelector('#inputFile'); // File input element
            const files = fileInput.files;

            // **Validation Check**
            if (!userMessage.trim() && (!files || files.length === 0)) {
                Swal.fire({
                    title: 'Input Required',
                    text: 'Please provide a message or upload a file before sending.',
                    icon: 'error'
                });

                // Re-enable buttons and stop the spinner
                $("#fileButton").prop('disabled', false);
                $("#audioButton").prop('disabled', false);
                $("#sendButton").prop('disabled', false);
                $('#sendButton i').removeClass('fa-spin fa-spinner').addClass('fa-paper-plane-top');

                return; // Exit the function
            }

            // Add a default message if userMessage is empty but a file is present
            if (!userMessage.trim() && files && files.length > 0) {
                const defaultMessage = "File uploaded without a message."; // Example of a default message
                userMessage = defaultMessage;
                formData.set('prompt', defaultMessage); // Update FormData with the default message
            }

            // AI chat message template
            let aiMessageDiv = $('<div class="chat-box author-speech"><div class="inner"><div class="chat-section"><div class="author"><img class="w-100 radius" src="/static/assets-site/images/team/bot.png" alt="Author"></div><div class="chat-content"><h6 class="title">Silo AI</h6><p class="mainChatBubble"></p><div class="chat-section generate-section" id="loadingIcon"><div class="author"><img src="/static/assets-site/images/icons/loader-one.gif" alt="Loader Images"></div></div></div></div></div></div>');
            //let mainChatBubble = aiMessageDiv.find('.mainChatBubble');
            mainChatBubble = aiMessageDiv.find('.mainChatBubble');

            let imageHtml = '';
            if (files && files.length > 0) {
                const file = files[0];

                // Check if the file is an image
                if (!file.type.startsWith('image/')) {
                    Swal.fire({
                        title: 'Invalid Type',
                        text: 'The selected file is not an image. Please upload an image file.',
                        icon: 'error'
                    });
                    return;
                }

                // Append the image to the FormData
                formData.append('file', file);

                // Create a promise for reading the file
                const readFileAsDataURL = (file) => {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = (e) => resolve(e.target.result); // Resolve with the image data
                        reader.onerror = (e) => reject(new Error('Failed to read file.'));
                        reader.readAsDataURL(file); // Start reading the file
                    });
                };

                // Wait until the file is fully read before continuing
                readFileAsDataURL(file)
                    .then((imageData) => {
                        // Once the file is read, assign it to imageHtml and call the appropriate function
                        insertUserMessage(userMessage, imageData);
                        $("#chatContainer").append(aiMessageDiv);
                        updateScrollbar();
                    })
                    .catch((error) => {
                        console.error(error.message); // Handle reading errors
                        Swal.fire({
                            title: 'File Error',
                            text: 'There was an error reading the file.',
                            icon: 'error'
                        });
                    });
            } else {
                // If no file is selected, call insertUserMessage without imageHtml
                insertUserMessage(userMessage, null);
                $("#chatContainer").append(aiMessageDiv);
                updateScrollbar();
            }

            // Create a user message container in the chat
            // insertUserMessage(userMessage, imageHtml);
            // updateScrollbar();

            // Append the AI response placeholder to the chat container
            //$("#chatContainer").append(aiMessageDiv);
            //updateScrollbar();
            //return;

            // Send the request to the server
            const url = '{% url 'home_api_ai_chat_tools' 1 %}';
            const response = await fetch(url, {
                method: 'POST',
                body: formData
            });

            // Stream the AI response and display it
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullResponse = '';
            while (true) {
                const { value, done } = await reader.read();
                if (done) {
                    // Enable buttons after completion
                    console.log('Connection Closed');
                    $("#fileButton").prop('disabled', false);
                    $("#audioButton").prop('disabled', false);
                    $("#sendButton").prop('disabled', false);
                    $('#sendButton i').removeClass('fa-spin fa-spinner').addClass('fa-paper-plane-top');
                    enableFileButton();
                    // Remove the loading spinner
                    $('#loadingIcon').remove();
                    break;
                }

                // Decode and format the streamed response
                //let outputText = decoder.decode(value).replace(/\n/g, '<br>');
                //outputText = outputText.replace(/uploads\/generated\/([a-z0-9\-]+?)\.png/gi,
                //    '<a class="image-popup-vertical-fit" href="/get_image/$&" target="_blank"><img class="w-100" src="/get_image/$&" alt="Image" /></a>');
                let outputText = decoder.decode(value);
                if (outputText === 'open_demo_sub_window') {
                    $('.popup-dashboardright-btn').removeClass('collapsed');
                    $('.popup-dashboardright-section').removeClass('collapsed');
                    continue;
                }
                outputText = processIncomingFragment2(outputText);





                fullResponse += outputText;
                mainChatBubble.append(outputText);
                cleanHeadingsInDiv();
                updateScrollbar();
            }

            // Final formatting of the AI response
            //fullResponse = convertToHTML(fullResponse);
            //mainChatBubble.html(fullResponse);
            mainChatBubble.removeClass('mainChatBubble');
            updateScrollbar();
        }


        /*
         * Connect to AI server, and download the streamed AI response.
         */
        async function fetchDataOLD() {
            updateScrollbar();

            // We want to let the user know that they need to wait until this communication is over.
            // Changes the send icon with a 'waiting' spinner.
            $("#fileButton").prop('disabled', true);
            $("#audioButton").prop('disabled', true);
            $("#sendButton").prop('disabled', true);
            $('#sendButton i').removeClass('fa-paper-plane-top').addClass('fa-spin fa-spinner');

            // To add to each communication HTML
            let currentdate = new Date();
            let datetime = (currentdate.getMonth()+1) + "/" + currentdate.getDate()  + "/" + currentdate.getFullYear() +
                    " - " + currentdate.getHours() + ":" + currentdate.getMinutes() + ":" + currentdate.getSeconds();

            // We don't want the user to have to delete their message after they submit.
            const userMessage = $('#chatForm').val();
            $('#chatForm').val('');


            // Create a new user chat message HTML
            insertUserMessage(userMessage);
            // let userMessageDiv = $('<div class="hstack gap-3 align-items-start mb-7 justify-content-end"> <div class="text-end"><h6 class="fs-2 text-muted">' + datetime + '</h6><div class="p-2 bg-light-info text-dark rounded-1 d-inline-block fs-3 chat-bubble">' + userMessage + '</div></div></div>');

            // Create a new AI chat message HTML
            //let aiMessageDiv = $('<div class="hstack gap-3 align-items-start mb-7 justify-content-start"><img src="/static/assets-portal/images/chat/Bot2.webp" alt="user8" width="40" height="40" class="rounded-circle" /> <div><h6 class="fs-2 text-muted">' + datetime + '</h6><div class="p-2 bg-light rounded-1 d-inline-block text-dark fs-3 main-chat-bubble chat-bubble"><i class="fs-5 ms-0 me-1 ti spinner-border"></i></div></div></div>');
            //let aiMessageDiv = $('<div class="d-flex justify-content-start mb-10 "><div class="d-flex flex-column align-items-start"><div class="d-flex align-items-center mb-2 mt-1"><div class="symbol  symbol-35px symbol-circle "><img alt="AI Data Scientist" src="/static/assets-login/images/favicon.png"/></div><div class="ms-3"><a name="#" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">AI Data Scientist</a><span class="text-muted fs-7 mb-1">2 mins</span></div></div><div class="p-5 rounded bg-light-info text-gray-900 fw-semibold mw-lg-400px text-start main-chat-bubble" data-kt-element="message-text"><h6 class="m-b-0"><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div></h6></div></div></div>');
            let aiMessageDiv = $('<div class="chat-box author-speech "><div class="inner"><div class="chat-section"><div class="author"><img class="w-100 radius" src="/static/assets-site/images/team/bot.png" alt="Author"></div><div class="chat-content"><h6 class="title">Silo AI</h6><p class="mainChatBubble"></p><div class="chat-section generate-section" id="loadingIcon"><div class="author"><img src="/static/assets-site/images/icons/loader-one.gif" alt="Loader Images"></div></div></div></div></div></div>');
            let mainChatBubble = aiMessageDiv.find('.mainChatBubble');

            // Append the new div to the #chatwindow
            // $("#chat-scroll").append(userMessageDiv);
            $("#chatContainer").append(aiMessageDiv);
            updateScrollbar();

            // Form to send to the AI server
            const formData = new FormData();
            formData.append('prompt', userMessage);
            formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

            // Connect to AI server
            const url = '{% url 'home_api_ai_chat_tools' 1 %}';
            const response = await fetch(url, {
                method: 'POST',
                body: formData
            })

            // Wait and read data as it comes in, streaming the AI data so the user doesn't wait a long time.
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullResponse = '';
            while (true) {
                const { value, done } = await reader.read();
                if (done) {
                    // We're done so re-enable button
                    console.log('Connection Closed');
                    $("#fileButton").prop('disabled', false);
                    $("#audioButton").prop('disabled', false);
                    $("#sendButton").prop('disabled', false);
                    $('#sendButton i').removeClass('fa-spin fa-spinner').addClass('fa-paper-plane-top');
                    break;
                }
                // Remove <i> since the HTML starts with a spinner icon to not confuse the user with a blank box.
                //mainChatBubble.find('h6').remove();
                $('#loadingIcon').remove();
                let outputText = decoder.decode(value).replace(/\n/g, '<br>');

                outputText = outputText.replace(/uploads\/generated\/([a-z0-9\-]+?)\.png/gi,
                    '<a class="image-popup-vertical-fit" href="/get_image/$&" target="_blank"><img class="w-100" src="/get_image/$&" alt="Image" /></a>');

                console.log('outputText');
                console.log(outputText);

                fullResponse += outputText;  // Save for later.
                mainChatBubble.append(outputText);

                updateScrollbar();
            }

            // Adding HTML like <code>, <h6>, etc. causes issues because browsers 'fix' non ended tags.
            // So we fix the HTML after stream is done.
            fullResponse = convertToHTML(fullResponse);
            mainChatBubble.html(fullResponse);
            mainChatBubble.removeClass('mainChatBubble')
            updateScrollbar();
            /*
            $(".image-popup-vertical-fit").magnificPopup({
                type: "image",
                closeOnContentClick: true,
                mainClass: "mfp-img-mobile",
                image: {verticalFit: true},
            });
             */
        }




        function enableFileButton() {
            // Enable the file input
            $('#fileButton').removeClass('disabled').css({ pointerEvents: "auto", opacity: 1 });

            // Change the icon class back from fa-file to fa-paperclip
            $('#fileButton i').removeClass('fa-image').addClass('fa-paperclip');

            // Restore the original color (assuming default color is black; modify if needed)
            $('#fileButton i').css('color', '');

            // Clear the selected file from the input
            $('#inputFile').val(''); // Resets the file input value
        }


        function disableFileButton() {
            // Disable the file input
            $('#fileButton').addClass('disabled').css({ pointerEvents: "none", opacity: 1 });

            // Change the icon class from fa-paperclip to fa-file
            $('#fileButton i').removeClass('fa-paperclip').addClass('fa-image');

            // Change the color of the icon to chartreuse
            $('#fileButton i').css('color', 'chartreuse');
        }



        // On file selection
        $('#inputFile').on('change', function (event) {
            const file = event.target.files[0]; // Get the first selected file

            // Validate file type
            if (file && !file.type.startsWith('image/')) {
                Swal.fire({
                    title: 'Invalid Type',
                    text: 'The selected file is not an image. Please upload an image file.',
                    icon: 'error'
                });

                // Clear the input file
                $(this).val(''); // Reset the file input value
                return;
            }

            // If valid, proceed with the current actions
            disableFileButton();
        });











        $('#sendButton').click(function() {
            console.log('Submit...');
            // insertMessage();
            fetchData();
        });

        $('#chatForm').submit(function(e){
            e.preventDefault();
        });

        $('#chatForm').on('keydown', function(e) {
            console.log('Key...');
            if (e.which == 13) {
                console.log('down...');
                // insertMessage();
                fetchData();
                return false;
            }
        });
    });

    let canExecute = true;
    function updateScrollbar() {

        if (canExecute) {
            // Execute the scrolling code
            $("html, body").animate({ scrollTop: $(document).height() }, 500);
            console.log('Scrolling...');

            // Prevent code execution for the next 2 seconds
            canExecute = false;

            // Reset the throttle timer after 2 seconds
            setTimeout(() => {
                canExecute = true;
            }, 1000);
        } else {
            // Optional: Log or handle cases when it's throttled
            //console.log('Throttle limit reached. Skipping execution.');
        }

    }

    function setDate(){
        d = new Date()
        if (m != d.getMinutes()) {
            m = d.getMinutes();
            $('<div class="timestamp">' + d.getHours() + ':' + m + '</div>').appendTo($('.message:last'));
            $('<div class="checkmark-sent-delivered">&check;</div>').appendTo($('.message:last'));
            $('<div class="checkmark-read">&check;</div>').appendTo($('.message:last'));
        }
    }

    function escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    function insertUserMessage(message, image) {
        //$('#chat-scroll').append('<div class="d-flex justify-content-end mb-10 " ><div class="d-flex flex-column align-items-end"><div class="d-flex align-items-center mb-2"><div class="me-3"><span class="text-muted fs-7 mb-1">5 mins</span><a name="#" class="fs-5 fw-bold text-gray-900 text-hover-primary ms-1">User</a>  </div><div class="symbol  symbol-35px symbol-circle "><img alt="Pic" src="static/assets-main/media/avatars/blank.png"/></div></div><div class="p-5 rounded bg-light-primary text-gray-900 fw-semibold mw-lg-400px text-end" data-kt-element="message-text">' + msg + '</div></div></div>');
        let safeMessage = escapeHtml(message);
        let messageHtml = `
                <div class="chat-box ai-speech">
                    <div class="inner d-flex justify-content-end">
                        <div class="chat-section">
                            <div class="text-end me-3">
                                <h6 class="title mb-2">User</h6>
                                <p class="mb-3">${safeMessage}</p>`;
        console.log('image');
        console.log(image);
        if (image) {
            messageHtml += `
                <div class="img-box-grp mt--25 mb--20">
                                        <div class="img-box">
                                            <img class="radius w-50" src="${image}" alt="Image Generation">
                                        </div>
                                    </div>`;
        }
        messageHtml += '</div></div></div></div>';
        $('#chatContainer').append(messageHtml);
        updateScrollbar();
    }







    /*
    function startAdvocateMessage() {
        $('<div class="row m-b-20 received-chat align-items-end" id="chats-loading"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center  theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0"><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div><div class="spinner-grow spinner-grow-sm" role="status"><span class="sr-only">.</span></div></h6></div></div></div>').appendTo($('#chat-scroll'));

        updateScrollbar();
    }

    function insertAdminMessage(msg) {
        $('#chats-loading').remove();
        $('#chat-scroll').append('<div class="row m-b-20 received-chat align-items-end"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">S</h5></div><div class="col"><div class="msg"><h6 class="m-b-0">' + msg + '</h6></div></div></div>');
        updateScrollbar();
    }

    function insertAdvocateMessage(msg, pause, msg2) {
        if (pause) {
            setTimeout(function () {
                $('#chats-loading').remove();
                $('#chat-scroll').append('<div class="row m-b-20 received-chat align-items-end"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0">' + msg + '</h6></div></div></div>');

                if (msg2 != 'null') {
                    $('#chat-scroll').append('<div class="row m-b-20 received-chat align-items-end"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0">' + msg2 + '</h6></div></div></div>');
                }

                updateScrollbar();
            }, 1000 + (Math.random() * 20) * 100);
        }
        else
        {
            $('#chats-loading').remove();
            $('#chat-scroll').append('<div class="row m-b-20 received-chat align-items-end"><div class="col-auto p-r-0"><h5 class="text-white d-flex align-items-center theme-bg2 justify-content-center">AI</h5></div><div class="col"><div class="msg"><h6 class="m-b-0">' + msg + '</h6></div></div></div>');
            updateScrollbar();
        }
        i++;
    }

     */


















</script>

{% endblock javascripts %}
































