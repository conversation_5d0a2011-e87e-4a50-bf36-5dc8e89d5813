{% extends "layouts/base.html" %}
{% load humanize %}
{% block title %} Dashboard {% endblock %}

<!-- Specific CSS goes HERE -->
{% block stylesheets %}
    <link rel="stylesheet" href="/static/assets/vendor/swiper/css/swiper-bundle.min.css">
    <link rel="stylesheet" href="/static/assets/vendor/dropzone/dist/dropzone.css">
{% endblock stylesheets %}

{% block content-body %}{% endblock content-body %}
{% block content-fluid %}{% endblock content-fluid %}
{% block content %}


    <div class="row">
        <div class="col-xl-12">

            <div class="row">
                <div class="col-xl-12">
                    <div class="page-titles">
                        <div class="d-flex align-items-center">
                            <h2 class="heading">Dashboard</h2>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
            
                <div class="col-xl-6">
                    <div class="card Upgrade">
                        <div class="card-body d-flex align-items-center ps-0">
                            <div class="d-inline-block position-relative donut-chart-sale ">
                                <div id="redial"></div>
                            </div>
                            <div class="upgread-stroage">
                                <h1 class="fs-40 text-primary">Coming Soon</h1>
                                <!--
                                <p>
                                    You currently have
                                    <strong>
                                        {{ user.credits|intcomma }}
                                    </strong>
                                    credits left to use with AI Article SEO.
                                </p>
                                <a class="btn btn-primary" href="{% url 'buy_credits' %}">
                                    Buy More Credits <i class="fas fa-chevron-circle-right ms-2"></i>
                                </a>
                                -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Box of credit details -->
                <!--
                <div class="col-xl-6">
                    <div class="card Upgrade">
                        <div class="card-body d-flex align-items-center ps-0">
                            <div class="d-inline-block position-relative donut-chart-sale ">
                                <div id="redial"></div>
                            </div>
                            <div class="upgread-stroage">
                                <h3 class="fs-20 text-primary">Your Remaining Credits</h3>
                                <p>
                                    You currently have
                                    <strong>
                                        {{ user.credits|intcomma }}
                                    </strong>
                                    credits left to use with AI Article SEO.
                                </p>
                                <a class="btn btn-primary" href="{% url 'buy_credits' %}">
                                    Buy More Credits <i class="fas fa-chevron-circle-right ms-2"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                -->

                <!-- Box of subscription details -->
                <div class="col-xl-6">
                    <!--

                    <div class="card">
                        <div class="">
                            <div class="prot-blog">
                                <div class="d-flex post justify-content-between mb-2 align-items-center">
                                    <h3 class="text d-inline mb-0">
                                        Your Current Plan
                                    </h3>
                                    <a href="{% url 'view_subscriptions' %}" class="btn btn-white btn-outline-dark">
                                        View Subscriptions <i class="fas fa-chevron-circle-right ms-2"></i>
                                    </a>
                                </div>
                                <div class="d-flex fill justify-content-between align-items-center">
                                    <h2 class="text">{{ user.subscription_type }} Subscription</h2>
                                </div>
                                <p class="mb-0 mt-2">{{ user.subscription_type.description }}</p>
                                <div class="shape">
                                    <svg width="488" height="353" viewBox="0 0 488 353" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <mask id="mask0_51_1209" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="438" height="283">
                                            <rect width="438" height="283" fill="url(#paint0_linear_51_1209)"></rect>
                                        </mask>
                                        <g mask="url(#mask0_51_1209)">
                                            <path d="M165 410.5H15L465.5 88H487.5L165 410.5Z" fill="#a5e8d4"></path>
                                            <path d="M264 369.5H114L564.5 47H586.5L264 369.5Z" fill="#a5e8d4"></path>
                                        </g>
                                        <defs>
                                            <linearGradient id="paint0_linear_51_1209" x1="308.075" y1="-143.042" x2="316.634" y2="468.334" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#363B64"></stop>
                                                <stop offset="1" stop-color="#4CBC9A"></stop>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    -->

                    <!--
                    <div class="card overflow-hidden">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between flex-wrap">
                                <div>
                                    <h4 class="fs-28 mb-0">
                                        Your Current Plan
                                    </h4>
                                    <span class="fs-18 text-primary font-w600 mb-3 d-block">{{ user.subscription_type }} Subscription</span>
                                </div>
                                <div class="compose-btn">
                                    <a class="btn btn-primary" href="{% url 'view_subscriptions' %}">
                                        View Subscriptions <i class="fas fa-arrow-circle-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                            <p class="mb-0">{{ user.subscription_type.description }}</p>
                            <div class="mail-img">
                                <svg width="156" height="84" viewBox="0 0 156 84" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.1" d="M164.961 6.14744C165.013 5.67345 165.013 5.1969 164.961 4.72291L164.136 3.36961C164.136 3.36961 164.136 2.87103 163.678 2.65735L163.22 2.30122L161.754 1.37527C161.353 1.05833 160.888 0.79377 160.379 0.591786L158.821 0.164429H156.988H8.52299H6.69009L5.13212 0.663011C4.62116 0.832312 4.15505 1.07382 3.75745 1.37527L2.29113 2.30122C2.29113 2.30122 2.29113 2.30122 2.29113 2.65735C2.29113 3.01348 2.29113 3.15593 1.8329 3.36961L1.00809 4.72291C0.956224 5.1969 0.956224 5.67345 1.00809 6.14744L0 6.93093V92.4025C0 94.2916 0.965543 96.1032 2.68422 97.439C4.4029 98.7747 6.73393 99.5252 9.16451 99.5252H91.6451C94.0756 99.5252 96.4067 98.7747 98.1253 97.439C99.844 96.1032 100.81 94.2916 100.81 92.4025C100.81 90.5135 99.844 88.7018 98.1253 87.3661C96.4067 86.0303 94.0756 85.2799 91.6451 85.2799H18.329V21.1762L76.9818 55.3648C78.5682 56.2895 80.4976 56.7894 82.4805 56.7894C84.4635 56.7894 86.3929 56.2895 87.9792 55.3648L146.632 21.1762V85.2799H128.303C125.872 85.2799 123.541 86.0303 121.823 87.3661C120.104 88.7018 119.139 90.5135 119.139 92.4025C119.139 94.2916 120.104 96.1032 121.823 97.439C123.541 98.7747 125.872 99.5252 128.303 99.5252H155.797C158.227 99.5252 160.558 98.7747 162.277 97.439C163.996 96.1032 164.961 94.2916 164.961 92.4025V6.93093C164.961 6.93093 164.961 6.43234 164.961 6.14744ZM82.4805 40.7634L36.658 14.0536H128.303L82.4805 40.7634Z" fill="#9568FF"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    -->
                </div>

                <!-- Box of links -->
                <div class="col-xl-6">

                    <!--
                    <div class="card">
                        <div class="card-body">
                            <div class="profile-news">
                                <h3 class="text-primary mb-3">Your AI Tools</h3>

                                <div class="row">
                                    <div class="col-xl-6 col-xxl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="widget-stat card bg-danger">
                                            <div class="card-body p-3">
                                                <div class="media">
                                                    <span class="me-3">
                                                        <i class="flaticon-381-calendar-1"></i>
                                                    </span>
                                                    <div class="media-body text-white text-end">
                                                        <h5 class="text-white">Templates</h5>
                                                        <a href="{% url 'create_view_templates' %}" class="btn btn-outline-light text-white mb-2">Start <i class="fas fa-chevron-circle-right ms-2"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-xxl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="widget-stat card bg-success">
                                            <div class="card-body p-3">
                                                <div class="media">
                                                    <span class="me-3">
                                                        <i class="fas fa-comments"></i>
                                                    </span>
                                                    <div class="media-body text-white text-end">
                                                        <h5 class="text-white">SEO Assistant</h5>
                                                        <a href="" class="btn btn-outline-light text-white mb-2">Start <i class="fas fa-chevron-circle-right ms-2"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-xxl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="widget-stat card bg-info">
                                            <div class="card-body p-3">
                                                <div class="media">
                                                    <span class="me-3">
                                                        <i class="fas fa-link"></i>
                                                    </span>
                                                    <div class="media-body text-white text-end">
                                                        <h5 class="text-white">Your Sites</h5>
                                                        <a href="" class="btn btn-outline-light text-white mb-2">Start <i class="fas fa-chevron-circle-right ms-2"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-xxl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="widget-stat card bg-primary">
                                            <div class="card-body p-3">
                                                <div class="media">
                                                    <span class="me-3">
                                                        <i class="fas fa-images"></i>
                                                    </span>
                                                    <div class="media-body text-white text-end">
                                                        <h5 class="text-white">AI Images</h5>
                                                        <a href="" class="btn btn-outline-light text-white mb-2">Start <i class="fas fa-chevron-circle-right ms-2"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-xxl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="widget-stat card bg-warning">
                                            <div class="card-body p-3">
                                                <div class="media">
                                                    <span class="me-3">
                                                        <i class="fas fa-folder"></i>
                                                    </span>
                                                    <div class="media-body text-white text-end">
                                                        <h5 class="text-white">Documents</h5>
                                                        <a href="{% url 'create_view_documents' %}" class="btn btn-outline-light text-white mb-2">Start <i class="fas fa-chevron-circle-right ms-2"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-xxl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="widget-stat card bg-secondary">
                                            <div class="card-body p-3">
                                                <div class="media">
                                                    <span class="me-3">
                                                        <i class="fas fa-pencil-alt"></i>
                                                    </span>
                                                    <div class="media-body text-white text-end">
                                                        <h5 class="text-white">AI Writer</h5>
                                                        <a href="{% url 'create_document' '1' %}" class="btn btn-outline-light text-white mb-2">Start <i class="fas fa-chevron-circle-right ms-2"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                            </div>
                        </div>
                    </div>
                    -->


                </div>

                <!-- Box of article links -->
                <div class="col-xl-6">
                    <!--
                    <div class="card">
                        <div class="card-body">
                            <div class="profile-news">
                                <h3 class="text-primary d-inline">Our Latest Articles</h3>

                                {% for article in articles %}
                                    <div class="media pt-3 pb-3">
                                        <a href="{% url 'article_view' article.pk %}">
                                            <img src="/get_image/{{ article.thumbnail_file }}" alt="image" class="me-3 rounded" width="75">
                                        </a>
                                        <div class="media-body">
                                            <h5 class="m-b-5">
                                                <a href="{% url 'article_view' article.pk %}" class="text-black">
                                                    {{ article.title }}
                                                </a>
                                            </h5>
                                            <p class="mb-0">{{ article.description }}</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            
                            
                            
                                <h4 class="heading">Import Settings</h4>
										<p>Upload a settings json file to import settings and/or prompts.</p>
										<div class="dz-default mb-3">	
											<form action="#" class="dropzone upload-img text-center mb-3">
												
												<div class="fallback">
													<input name="file" type="file" multiple>
												</div>
												<svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
													<path d="M27.1666 26.6667L20.4999 20L13.8333 26.6667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
													<path d="M20.5 20V35" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
													<path d="M34.4833 30.6501C36.1088 29.7638 37.393 28.3615 38.1331 26.6644C38.8731 24.9673 39.027 23.0721 38.5703 21.2779C38.1136 19.4836 37.0724 17.8926 35.6111 16.7558C34.1497 15.619 32.3514 15.0013 30.4999 15.0001H28.3999C27.8955 13.0488 26.9552 11.2373 25.6498 9.70171C24.3445 8.16614 22.708 6.94647 20.8634 6.1344C19.0189 5.32233 17.0142 4.93899 15.0001 5.01319C12.9861 5.0874 11.015 5.61722 9.23523 6.56283C7.45541 7.50844 5.91312 8.84523 4.7243 10.4727C3.53549 12.1002 2.73108 13.9759 2.37157 15.959C2.01205 17.9421 2.10678 19.9809 2.64862 21.9222C3.19047 23.8634 4.16534 25.6565 5.49994 27.1667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
													<path d="M27.1666 26.6667L20.4999 20L13.8333 26.6667" stroke="#DADADA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
												</svg>
												
											</form>
											<small><strong class="text-black">Important:</strong> It's highly recommended to backup your current settings before importing new settings, so you can revert back if needed.</small>
                                        </div>
                            
                            
                            

                            </div>
                        </div>
                    </div>
                    -->
                
                </div>

            </div>
        </div>
    </div>


{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
    <script src="/static/assets/vendor/dropzone/dist/dropzone.js"></script>
    <script src="/static/assets/vendor/apexchart/apexchart.js"></script>
    <script>
        var options = {
			series: [{{ percent }}],
			chart: {
				width: 180,
				height: 180,
				type: 'radialBar',
			},
			colors:["#20C997"],
			plotOptions: {
				radialBar: {
					startAngle: -180,
					endAngle: 180,
                    hollow: {
                        size: '60%',
						background: 'var(--rgba-primary-1)',
						margin:15
                    },
					dataLabels: {
                      show: true,
                      name: {
                        offsetY: 20,
                        show: true,
                        color: '#888',
                        fontSize: '12px'
                      },
                      value: {
                        formatter: function(val) {
                          return val + "%"
                        },
                        offsetY: -10,
                        color: '#000000',
                        fontWeight:700,
                        fontSize: '18px',
                        show: true,
                      }
                    },
					track: {
						background: '#FFF',
					}
				}
			},
            stroke: {
              lineCap: 'round'
            },
			labels: [''],
			responsive: [{
				breakpoint: 575,
				options: {
					chart: {
						height: 200,
					},
				}
			}],
        };
		var chart = new ApexCharts(document.querySelector("#redial"), options);
		chart.render();
	</script>

{% endblock javascripts %}