{% extends "layouts/base.html" %}

{% block title %}Create New Sequence: {{ campaign.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Create New Email Sequence</h1>
            <p>Create a new email sequence for campaign: {{ campaign.name }}</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Sequence Details</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="form-group mb-3">
                            <label for="name">Sequence Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <small class="form-text text-muted">Give your sequence a descriptive name.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            <small class="form-text text-muted">Optional description of the sequence's purpose.</small>
                        </div>
                        <button type="submit" class="btn btn-primary">Create Sequence</button>
                        <a href="{% url 'email_bulk:sequence_list' campaign_id=campaign.id %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">About Email Sequences</h5>
                </div>
                <div class="card-body">
                    <p>Email sequences allow you to send a series of emails to your leads over time. Each sequence can contain multiple email templates that will be sent in order, with configurable delays between each email.</p>
                    <p>After creating a sequence, you'll need to add email templates to it.</p>
                    <p>For example, you might create a sequence with:</p>
                    <ol>
                        <li>An initial introduction email (sent immediately)</li>
                        <li>A follow-up email (sent 3 days later)</li>
                        <li>A final reminder email (sent 7 days after the follow-up)</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Campaign Details</h5>
                </div>
                <div class="card-body">
                    <p><strong>Campaign:</strong> {{ campaign.name }}</p>
                    <p><strong>Description:</strong> {{ campaign.description }}</p>
                    <p><strong>Status:</strong> <span class="badge bg-primary">{{ campaign.get_status_display }}</span></p>
                    <p><strong>Created:</strong> {{ campaign.date_created|date }}</p>
                    {% if campaign.start_date %}
                    <p><strong>Started:</strong> {{ campaign.start_date|date }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}