{% extends "layouts/base.html" %}

{% block title %}Edit Campaign: {{ campaign.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Edit Campaign</h1>
            <p>Update your bulk email campaign details.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Campaign Details</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="form-group mb-3">
                            <label for="name">Campaign Name</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ campaign.name }}" required>
                            <small class="form-text text-muted">Give your campaign a descriptive name.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ campaign.description }}</textarea>
                            <small class="form-text text-muted">Optional description of the campaign's purpose.</small>
                        </div>
                        <button type="submit" class="btn btn-primary">Update Campaign</button>
                        <a href="{% url 'email_bulk:campaign_detail' campaign_id=campaign.id %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Campaign Status</h5>
                </div>
                <div class="card-body">
                    <p><strong>Status:</strong> <span class="badge bg-primary">{{ campaign.get_status_display }}</span></p>
                    <p><strong>Created:</strong> {{ campaign.date_created|date }}</p>
                    {% if campaign.start_date %}
                    <p><strong>Started:</strong> {{ campaign.start_date|date }}</p>
                    {% endif %}
                    
                    <div class="mt-3">
                        <a href="{% url 'email_bulk:campaign_detail' campaign_id=campaign.id %}" class="btn btn-primary">View Campaign</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}