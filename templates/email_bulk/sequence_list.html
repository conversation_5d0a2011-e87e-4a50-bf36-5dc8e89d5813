{% extends "layouts/base.html" %}

{% block title %}Email Sequences: {{ campaign.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Email Sequences for {{ campaign.name }}</h1>
            <p>Manage the email sequences in this campaign.</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <a href="{% url 'email_bulk:sequence_create' campaign_id=campaign.id %}" class="btn btn-primary">Create New Sequence</a>
            <a href="{% url 'email_bulk:campaign_detail' campaign_id=campaign.id %}" class="btn btn-secondary">Back to Campaign</a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">All Sequences</h5>
                </div>
                <div class="card-body">
                    {% if sequences %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                    <th>Templates</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sequence in sequences %}
                                <tr>
                                    <td>{{ sequence.name }}</td>
                                    <td>{{ sequence.description }}</td>
                                    <td>{% if sequence.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span>{% endif %}</td>
                                    <td>{{ sequence.email_templates.count }}</td>
                                    <td>{{ sequence.date_created|date }}</td>
                                    <td>
                                        <a href="{% url 'email_bulk:sequence_detail' sequence_id=sequence.id %}" class="btn btn-sm btn-primary">View</a>
                                        <a href="{% url 'email_bulk:template_create' sequence_id=sequence.id %}" class="btn btn-sm btn-info">Add Template</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p>No sequences found. <a href="{% url 'email_bulk:sequence_create' campaign_id=campaign.id %}">Create your first sequence</a>.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">About Email Sequences</h5>
                </div>
                <div class="card-body">
                    <p>Email sequences allow you to send a series of emails to your leads over time. Each sequence can contain multiple email templates that will be sent in order, with configurable delays between each email.</p>
                    <p>For example, you might create a sequence with:</p>
                    <ol>
                        <li>An initial introduction email (sent immediately)</li>
                        <li>A follow-up email (sent 3 days later)</li>
                        <li>A final reminder email (sent 7 days after the follow-up)</li>
                    </ol>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Campaign Details</h5>
                </div>
                <div class="card-body">
                    <p><strong>Campaign:</strong> {{ campaign.name }}</p>
                    <p><strong>Status:</strong> <span class="badge bg-primary">{{ campaign.get_status_display }}</span></p>
                    <p><strong>Created:</strong> {{ campaign.date_created|date }}</p>
                    {% if campaign.start_date %}
                    <p><strong>Started:</strong> {{ campaign.start_date|date }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}