{% extends "layouts/base.html" %}

{% block title %}Sequence: {{ sequence.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>{{ sequence.name }}</h1>
            <p>{{ sequence.description }}</p>
            <div class="mb-3">
                <span class="badge {% if sequence.is_active %}bg-success{% else %}bg-danger{% endif %}">
                    {% if sequence.is_active %}Active{% else %}Inactive{% endif %}
                </span>
                <span class="text-muted">Created: {{ sequence.date_created|date }}</span>
            </div>
            <div class="mb-3">
                <a href="{% url 'email_bulk:template_create' sequence_id=sequence.id %}" class="btn btn-primary">Add Email Template</a>
                <a href="{% url 'email_bulk:sequence_list' campaign_id=sequence.campaign.id %}" class="btn btn-secondary">Back to Sequences</a>
                <a href="{% url 'email_bulk:campaign_detail' campaign_id=sequence.campaign.id %}" class="btn btn-info">View Campaign</a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Email Templates</h5>
                </div>
                <div class="card-body">
                    {% if templates %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Order</th>
                                    <th>Name</th>
                                    <th>Subject</th>
                                    <th>Delay (days)</th>
                                    <th>Generation</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for template in templates %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ template.name }}</td>
                                    <td>{{ template.subject }}</td>
                                    <td>{{ template.delay_days }}</td>
                                    <td>{{ template.get_generation_type_display }}</td>
                                    <td>{% if template.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span>{% endif %}</td>
                                    <td>
                                        <a href="{% url 'email_bulk:template_edit' template_id=template.id %}" class="btn btn-sm btn-primary">Edit</a>
                                        {% if template.generation_type == 'manual' %}
                                        <a href="{% url 'email_bulk:generate_email' template_id=template.id %}" class="btn btn-sm btn-info">Generate with AI</a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p>No email templates found. <a href="{% url 'email_bulk:template_create' sequence_id=sequence.id %}">Create your first template</a>.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Sequence Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Name:</strong> {{ sequence.name }}</p>
                    <p><strong>Description:</strong> {{ sequence.description }}</p>
                    <p><strong>Status:</strong> {% if sequence.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span>{% endif %}</p>
                    <p><strong>Created:</strong> {{ sequence.date_created|date }}</p>
                    <p><strong>Templates:</strong> {{ templates|length }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Campaign Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Campaign:</strong> {{ sequence.campaign.name }}</p>
                    <p><strong>Status:</strong> <span class="badge bg-primary">{{ sequence.campaign.get_status_display }}</span></p>
                    <p><strong>Created:</strong> {{ sequence.campaign.date_created|date }}</p>
                    {% if sequence.campaign.start_date %}
                    <p><strong>Started:</strong> {{ sequence.campaign.start_date|date }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}