{% extends "layouts/base.html" %}

{% block title %}Generate Email Content: {{ template.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Generate Email Content with AI</h1>
            <p>Use OpenAI to generate content for template: {{ template.name }}</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">AI Generation Options</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="form-group mb-3">
                            <label for="tone">Email Tone</label>
                            <select class="form-control" id="tone" name="tone">
                                <option value="professional">Professional</option>
                                <option value="friendly">Friendly</option>
                                <option value="casual">Casual</option>
                                <option value="formal">Formal</option>
                                <option value="persuasive">Persuasive</option>
                            </select>
                            <small class="form-text text-muted">The tone of the generated email.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="length">Email Length</label>
                            <select class="form-control" id="length" name="length">
                                <option value="short">Short (1-2 paragraphs)</option>
                                <option value="medium" selected>Medium (2-3 paragraphs)</option>
                                <option value="long">Long (3-4 paragraphs)</option>
                            </select>
                            <small class="form-text text-muted">The length of the generated email.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="purpose">Email Purpose</label>
                            <select class="form-control" id="purpose" name="purpose">
                                <option value="introduction">Introduction</option>
                                <option value="follow_up">Follow-up</option>
                                <option value="reminder">Reminder</option>
                                <option value="promotion">Promotion</option>
                                <option value="information">Information</option>
                            </select>
                            <small class="form-text text-muted">The purpose of the email.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="additional_instructions">Additional Instructions (Optional)</label>
                            <textarea class="form-control" id="additional_instructions" name="additional_instructions" rows="3"></textarea>
                            <small class="form-text text-muted">Any additional instructions for the AI.</small>
                        </div>
                        <button type="submit" class="btn btn-primary">Generate Email Content</button>
                        <a href="{% url 'email_bulk:template_edit' template_id=template.id %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">About AI Generation</h5>
                </div>
                <div class="card-body">
                    <p>This feature uses OpenAI to generate email content based on your specifications.</p>
                    <p>The AI will create both the subject line and body content for your email template.</p>
                    <p>You can customize the tone, length, and purpose of the email to get the best results.</p>
                    <p>After generation, you can still edit the content before saving it to your template.</p>
                </div>
            </div>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">Template Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Template:</strong> {{ template.name }}</p>
                    <p><strong>Sequence:</strong> {{ template.sequence.name }}</p>
                    <p><strong>Campaign:</strong> {{ template.sequence.campaign.name }}</p>
                    <p><strong>Delay:</strong> {{ template.delay_days }} days</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}