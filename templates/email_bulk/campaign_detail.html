{% extends "layouts/base.html" %}

{% block title %}Campaign Details: {{ campaign.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>{{ campaign.name }}</h1>
            <p>{{ campaign.description }}</p>
            <div class="mb-3">
                <span class="badge bg-primary">{{ campaign.get_status_display }}</span>
                <span class="text-muted">Created: {{ campaign.date_created|date }}</span>
                {% if campaign.start_date %}
                <span class="text-muted">Started: {{ campaign.start_date|date }}</span>
                {% endif %}
            </div>
            <div class="mb-3">
                {% if campaign.status == 'draft' %}
                <form method="post" action="{% url 'email_bulk:campaign_start' campaign_id=campaign.id %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-success">Start Campaign</button>
                </form>
                {% elif campaign.status == 'active' %}
                <form method="post" action="{% url 'email_bulk:campaign_pause' campaign_id=campaign.id %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-warning">Pause Campaign</button>
                </form>
                {% elif campaign.status == 'paused' %}
                <form method="post" action="{% url 'email_bulk:campaign_resume' campaign_id=campaign.id %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-success">Resume Campaign</button>
                </form>
                {% endif %}
                <a href="{% url 'email_bulk:campaign_edit' campaign_id=campaign.id %}" class="btn btn-primary">Edit Campaign</a>
                <a href="{% url 'email_bulk:lead_select' campaign_id=campaign.id %}" class="btn btn-secondary">Add Leads</a>
                <a href="{% url 'email_bulk:sequence_create' campaign_id=campaign.id %}" class="btn btn-info">Add Sequence</a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-2">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Leads</h5>
                    <h2 class="card-text">{{ lead_count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Emails</h5>
                    <h2 class="card-text">{{ email_count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Sent</h5>
                    <h2 class="card-text">{{ sent_count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Opened</h5>
                    <h2 class="card-text">{{ opened_count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Replied</h5>
                    <h2 class="card-text">{{ replied_count }}</h2>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Email Sequences</h5>
                </div>
                <div class="card-body">
                    {% if sequences %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Status</th>
                                    <th>Templates</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sequence in sequences %}
                                <tr>
                                    <td>{{ sequence.name }}</td>
                                    <td>{% if sequence.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span>{% endif %}</td>
                                    <td>{{ sequence.email_templates.count }}</td>
                                    <td>
                                        <a href="{% url 'email_bulk:sequence_detail' sequence_id=sequence.id %}" class="btn btn-sm btn-primary">View</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p>No sequences found. <a href="{% url 'email_bulk:sequence_create' campaign_id=campaign.id %}">Create your first sequence</a>.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Emails</h5>
                </div>
                <div class="card-body">
                    {% if recent_emails %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Subject</th>
                                    <th>Lead</th>
                                    <th>Status</th>
                                    <th>Sent</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for email in recent_emails %}
                                <tr>
                                    <td>{{ email.subject }}</td>
                                    <td>{{ email.campaign_lead.lead.email }}</td>
                                    <td><span class="badge bg-primary">{{ email.get_status_display }}</span></td>
                                    <td>{% if email.sent_time %}{{ email.sent_time|date }}{% else %}-{% endif %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p>No emails have been sent yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Campaign Leads</h5>
                </div>
                <div class="card-body">
                    {% if leads %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Sequence Position</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for campaign_lead in leads %}
                                <tr>
                                    <td>{{ campaign_lead.lead.first_name }} {{ campaign_lead.lead.last_name }}</td>
                                    <td>{{ campaign_lead.lead.email }}</td>
                                    <td><span class="badge bg-primary">{{ campaign_lead.get_status_display }}</span></td>
                                    <td>{{ campaign_lead.current_sequence_position }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p>No leads have been added to this campaign. <a href="{% url 'email_bulk:lead_select' campaign_id=campaign.id %}">Add leads</a>.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <a href="{% url 'email_bulk:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>
</div>
{% endblock %}