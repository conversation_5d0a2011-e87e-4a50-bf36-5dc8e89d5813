{% extends "layouts/base.html" %}

{% block title %}Create Email Template: {{ sequence.name }}{% endblock %}

{% block extra_css %}
<!-- Include Summernote CSS -->
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Create New Email Template</h1>
            <p>Create a new email template for sequence: {{ sequence.name }}</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Template Details</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="form-group mb-3">
                            <label for="name">Template Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <small class="form-text text-muted">Give your template a descriptive name.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="subject">Email Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                            <small class="form-text text-muted">The subject line of the email.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="body_text">Email Body (Text)</label>
                            <textarea class="form-control" id="body_text" name="body_text" rows="10" required></textarea>
                            <small class="form-text text-muted">The plain text content of the email.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="body_html">Email Body (HTML)</label>
                            <textarea class="form-control" id="body_html" name="body_html" rows="10"></textarea>
                            <small class="form-text text-muted">Optional HTML content of the email.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="delay_days">Delay (days)</label>
                            <input type="number" class="form-control" id="delay_days" name="delay_days" min="0" value="0">
                            <small class="form-text text-muted">Number of days to wait before sending this email after the previous one.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="generation_type">Generation Type</label>
                            <select class="form-control" id="generation_type" name="generation_type">
                                <option value="manual">Manual</option>
                                <option value="openai">OpenAI Generated</option>
                                <option value="library">From Template Library</option>
                            </select>
                            <small class="form-text text-muted">How the email content was generated.</small>
                        </div>

                        <div class="form-group mb-3" id="library_template_section" style="display: none;">
                            <label for="library_template">Select Template from Library</label>
                            <select class="form-control" id="library_template" name="library_template">
                                <option value="">-- Select a template --</option>
                                {% for template in library_templates %}
                                <option value="{{ template.id }}">{{ template.name }} ({{ template.get_category_display }})</option>
                                {% endfor %}
                            </select>
                            <small class="form-text text-muted">Select a template from your library to use as a base.</small>
                            <div class="mt-2">
                                <a href="{% url 'email_bulk:template_library_list' %}" target="_blank" class="btn btn-sm btn-info">
                                    <i class="fas fa-external-link-alt"></i> View Template Library
                                </a>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Create Template</button>
                        <a href="{% url 'email_bulk:sequence_detail' sequence_id=sequence.id %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">About Email Templates</h5>
                </div>
                <div class="card-body">
                    <p>Email templates define the content of emails sent in a sequence. Each template includes a subject line and body content.</p>
                    <p>You can create templates manually or use AI to generate content.</p>
                    <p>Templates are sent in order based on their delay settings. For example:</p>
                    <ul>
                        <li>Template 1: Delay 0 days (sent immediately)</li>
                        <li>Template 2: Delay 3 days (sent 3 days after Template 1)</li>
                        <li>Template 3: Delay 5 days (sent 5 days after Template 2)</li>
                    </ul>
                </div>
            </div>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">Sequence Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Sequence:</strong> {{ sequence.name }}</p>
                    <p><strong>Campaign:</strong> {{ sequence.campaign.name }}</p>
                    <p><strong>Status:</strong> {% if sequence.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span>{% endif %}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include Summernote JS -->
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize Summernote WYSIWYG editor
        $('#body_html').summernote({
            height: 300,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ]
        });

        // Function to toggle the library template section based on generation type
        function toggleLibraryTemplateSection() {
            if ($('#generation_type').val() === 'library') {
                $('#library_template_section').show();
                // Hide the subject and body fields when using a library template
                $('#subject').closest('.form-group').hide();
                $('#body_text').closest('.form-group').hide();
                $('#body_html').closest('.form-group').hide();
                $('.note-editor').hide(); // Hide the Summernote editor
            } else {
                $('#library_template_section').hide();
                // Show the subject and body fields for other generation types
                $('#subject').closest('.form-group').show();
                $('#body_text').closest('.form-group').show();
                $('#body_html').closest('.form-group').show();
                $('.note-editor').show(); // Show the Summernote editor
            }
        }

        // Initial toggle based on the selected value
        toggleLibraryTemplateSection();

        // Toggle when the generation type changes
        $('#generation_type').change(function() {
            toggleLibraryTemplateSection();
        });
    });
</script>
{% endblock %}
