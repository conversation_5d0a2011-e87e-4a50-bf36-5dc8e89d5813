{% extends "layouts/base.html" %}

{% block title %}Email Template Library{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Email Template Library</h1>
            <p>Manage your reusable email templates that can be used across all campaigns.</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <a href="{% url 'email_bulk:template_library_create' %}" class="btn btn-primary">Create New Template</a>
            <a href="{% url 'email_bulk:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    {% if templates_by_category %}
        {% for category, templates in templates_by_category.items %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">{{ category }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Subject</th>
                                            <th>Owner</th>
                                            <th>Public</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for template in templates %}
                                            <tr>
                                                <td>{{ template.name }}</td>
                                                <td>{{ template.description }}</td>
                                                <td>{{ template.subject }}</td>
                                                <td>{{ template.user.username }}</td>
                                                <td>{% if template.is_public %}<span class="badge bg-success">Yes</span>{% else %}<span class="badge bg-secondary">No</span>{% endif %}</td>
                                                <td>{{ template.date_created|date }}</td>
                                                <td>
                                                    {% if template.user == request.user %}
                                                        <a href="{% url 'email_bulk:template_library_edit' template_id=template.id %}" class="btn btn-sm btn-primary">Edit</a>
                                                        <form method="post" action="{% url 'email_bulk:template_library_delete' template_id=template.id %}" class="d-inline">
                                                            {% csrf_token %}
                                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this template?')">Delete</button>
                                                        </form>
                                                    {% else %}
                                                        <span class="text-muted">View Only</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <p>No templates found in the library. <a href="{% url 'email_bulk:template_library_create' %}">Create your first template</a>.</p>
                </div>
            </div>
        </div>
    {% endif %}

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">About the Template Library</h5>
                </div>
                <div class="card-body">
                    <p>The template library allows you to create and manage reusable email templates that can be used across all your campaigns.</p>
                    <p>Templates in the library can be:</p>
                    <ul>
                        <li><strong>Private</strong> - Only visible to you</li>
                        <li><strong>Public</strong> - Visible to all users</li>
                    </ul>
                    <p>When creating a template for a sequence, you can choose to use a template from the library instead of creating a new one from scratch.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}