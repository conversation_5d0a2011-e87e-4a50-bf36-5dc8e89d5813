{% extends "layouts/base.html" %}

{% block title %}Bulk Email Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Bulk Email Dashboard</h1>
            <p>Manage your bulk email campaigns and monitor their performance.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Active Campaigns</h5>
                    <h2 class="card-text">{{ active_campaigns }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Completed Campaigns</h5>
                    <h2 class="card-text">{{ completed_campaigns }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Sent Emails</h5>
                    <h2 class="card-text">{{ sent_emails }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Opened Emails</h5>
                    <h2 class="card-text">{{ opened_emails }}</h2>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Campaigns</h5>
                </div>
                <div class="card-body">
                    {% if recent_campaigns %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for campaign in recent_campaigns %}
                                <tr>
                                    <td>{{ campaign.name }}</td>
                                    <td>{{ campaign.get_status_display }}</td>
                                    <td>{{ campaign.date_created|date }}</td>
                                    <td>
                                        <a href="{% url 'email_bulk:campaign_detail' campaign_id=campaign.id %}" class="btn btn-sm btn-primary">View</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p>No campaigns found. <a href="{% url 'email_bulk:campaign_create' %}">Create your first campaign</a>.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <a href="{% url 'email_bulk:campaign_create' %}" class="btn btn-primary">Create New Campaign</a>
            <a href="{% url 'email_bulk:campaign_list' %}" class="btn btn-secondary">View All Campaigns</a>
            <a href="{% url 'email_bulk:template_library_list' %}" class="btn btn-info">Manage Template Library</a>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Email Template Library</h5>
                </div>
                <div class="card-body">
                    <p>The Email Template Library allows you to create and manage reusable email templates that can be used across all your campaigns.</p>
                    <p>Features:</p>
                    <ul>
                        <li><strong>Reusable Templates</strong> - Create once, use in multiple campaigns</li>
                        <li><strong>Rich Text Editor</strong> - Create professional HTML emails with the WYSIWYG editor</li>
                        <li><strong>Template Categories</strong> - Organize templates by purpose (introduction, follow-up, etc.)</li>
                        <li><strong>Public/Private</strong> - Share templates with other users or keep them private</li>
                    </ul>
                    <a href="{% url 'email_bulk:template_library_list' %}" class="btn btn-primary">Go to Template Library</a>
                    <a href="{% url 'email_bulk:template_library_create' %}" class="btn btn-success">Create New Template</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
