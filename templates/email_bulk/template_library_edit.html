{% extends "layouts/base.html" %}

{% block title %}Edit Template: {{ template.name }}{% endblock %}

{% block extra_css %}
<!-- Include Summernote CSS -->
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Edit Template</h1>
            <p>Update your reusable email template.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Template Details</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="form-group mb-3">
                            <label for="name">Template Name</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ template.name }}" required>
                            <small class="form-text text-muted">Give your template a descriptive name.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="description">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="2">{{ template.description }}</textarea>
                            <small class="form-text text-muted">Optional description of the template's purpose.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="category">Category</label>
                            <select class="form-control" id="category" name="category">
                                {% for value, label in categories %}
                                <option value="{{ value }}" {% if template.category == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            <small class="form-text text-muted">The category this template belongs to.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="subject">Email Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" value="{{ template.subject }}" required>
                            <small class="form-text text-muted">The subject line of the email.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="body_text">Email Body (Text)</label>
                            <textarea class="form-control" id="body_text" name="body_text" rows="10" required>{{ template.body_text }}</textarea>
                            <small class="form-text text-muted">The plain text content of the email.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label for="body_html">Email Body (HTML)</label>
                            <textarea class="form-control" id="body_html" name="body_html" rows="10">{{ template.body_html }}</textarea>
                            <small class="form-text text-muted">The HTML content of the email. Use the editor to format your content.</small>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" id="is_public" name="is_public" {% if template.is_public %}checked{% endif %}>
                            <label class="form-check-label" for="is_public">Make this template public</label>
                            <small class="form-text text-muted d-block">If checked, other users will be able to use this template.</small>
                        </div>
                        <button type="submit" class="btn btn-primary">Update Template</button>
                        <a href="{% url 'email_bulk:template_library_list' %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">About Templates</h5>
                </div>
                <div class="card-body">
                    <p>Templates in the library can be used across all your campaigns.</p>
                    <p>Each template should include:</p>
                    <ul>
                        <li><strong>Text version</strong> - Required for all email clients</li>
                        <li><strong>HTML version</strong> - For rich formatting in supported email clients</li>
                    </ul>
                    <p>You can use the WYSIWYG editor to create the HTML version without knowing HTML code.</p>
                    <p>Templates can be private (only visible to you) or public (visible to all users).</p>
                </div>
            </div>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">Template Variables</h5>
                </div>
                <div class="card-body">
                    <p>You can use these variables in your templates:</p>
                    <ul>
                        <li><code>{{lead.first_name}}</code> - Lead's first name</li>
                        <li><code>{{lead.last_name}}</code> - Lead's last name</li>
                        <li><code>{{lead.email}}</code> - Lead's email address</li>
                        <li><code>{{lead.company}}</code> - Lead's company</li>
                        <li><code>{{user.first_name}}</code> - Your first name</li>
                        <li><code>{{user.last_name}}</code> - Your last name</li>
                        <li><code>{{user.email}}</code> - Your email address</li>
                    </ul>
                    <p>These will be replaced with actual values when the email is sent.</p>
                </div>
            </div>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">Template Usage</h5>
                </div>
                <div class="card-body">
                    <p>This template is currently used in:</p>
                    {% if template.sequence_templates.exists %}
                    <ul>
                        {% for seq_template in template.sequence_templates.all %}
                        <li>{{ seq_template.sequence.campaign.name }} - {{ seq_template.sequence.name }}</li>
                        {% endfor %}
                    </ul>
                    <p class="text-warning">Changes to this template will affect all campaigns using it.</p>
                    {% else %}
                    <p>This template is not currently used in any campaigns.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include Summernote JS -->
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
<script>
    $(document).ready(function() {
        $('#body_html').summernote({
            height: 300,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ]
        });
    });
</script>
{% endblock %}