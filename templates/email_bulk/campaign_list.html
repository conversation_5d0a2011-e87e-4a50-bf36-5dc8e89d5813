{% extends "layouts/base.html" %}

{% block title %}Email Campaigns{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Email Campaigns</h1>
            <p>Manage your bulk email campaigns and monitor their performance.</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <a href="{% url 'email_bulk:campaign_create' %}" class="btn btn-primary">Create New Campaign</a>
            <a href="{% url 'email_bulk:dashboard' %}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">All Campaigns</h5>
                </div>
                <div class="card-body">
                    {% if campaigns %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Status</th>
                                    <th>Leads</th>
                                    <th>Emails</th>
                                    <th>Sent</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for campaign in campaigns %}
                                <tr>
                                    <td>{{ campaign.name }}</td>
                                    <td><span class="badge bg-primary">{{ campaign.get_status_display }}</span></td>
                                    <td>{{ campaign.lead_count }}</td>
                                    <td>{{ campaign.email_count }}</td>
                                    <td>{{ campaign.sent_count }}</td>
                                    <td>{{ campaign.date_created|date }}</td>
                                    <td>
                                        <a href="{% url 'email_bulk:campaign_detail' campaign_id=campaign.id %}" class="btn btn-sm btn-primary">View</a>
                                        <a href="{% url 'email_bulk:campaign_edit' campaign_id=campaign.id %}" class="btn btn-sm btn-secondary">Edit</a>
                                        {% if campaign.status == 'draft' %}
                                        <form method="post" action="{% url 'email_bulk:campaign_start' campaign_id=campaign.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-sm btn-success">Start</button>
                                        </form>
                                        {% elif campaign.status == 'active' %}
                                        <form method="post" action="{% url 'email_bulk:campaign_pause' campaign_id=campaign.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-sm btn-warning">Pause</button>
                                        </form>
                                        {% elif campaign.status == 'paused' %}
                                        <form method="post" action="{% url 'email_bulk:campaign_resume' campaign_id=campaign.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-sm btn-success">Resume</button>
                                        </form>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p>No campaigns found. <a href="{% url 'email_bulk:campaign_create' %}">Create your first campaign</a>.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}