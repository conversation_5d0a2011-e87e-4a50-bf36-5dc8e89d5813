{% extends "layouts/base.html" %}

{% block title %}Select Leads: {{ campaign.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Select Leads for Campaign</h1>
            <p>Choose lead categories to add to campaign: {{ campaign.name }}</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Lead Categories</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="form-group mb-3">
                            <label>Select Lead Categories</label>
                            <div class="alert alert-info">
                                Selecting a category will add all leads in that category to your campaign.
                                Only leads with valid email addresses will be added.
                            </div>
                            {% if categories %}
                            <div class="list-group">
                                {% for category in categories %}
                                <div class="list-group-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="categories" value="{{ category.id }}" id="category-{{ category.id }}">
                                        <label class="form-check-label" for="category-{{ category.id }}">
                                            <strong>{{ category.name }}</strong>
                                            <p class="text-muted mb-0">{{ category.description }}</p>
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="alert alert-warning">
                                No lead categories found. Please create lead categories first.
                            </div>
                            {% endif %}
                        </div>
                        <button type="submit" class="btn btn-primary">Add Selected Leads</button>
                        <a href="{% url 'email_bulk:campaign_detail' campaign_id=campaign.id %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Campaign Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Campaign:</strong> {{ campaign.name }}</p>
                    <p><strong>Description:</strong> {{ campaign.description }}</p>
                    <p><strong>Status:</strong> <span class="badge bg-primary">{{ campaign.get_status_display }}</span></p>
                    <p><strong>Created:</strong> {{ campaign.date_created|date }}</p>
                    {% if campaign.start_date %}
                    <p><strong>Started:</strong> {{ campaign.start_date|date }}</p>
                    {% endif %}
                </div>
            </div>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">About Lead Selection</h5>
                </div>
                <div class="card-body">
                    <p>Leads are the contacts that will receive emails from your campaign.</p>
                    <p>You can add leads to your campaign by selecting lead categories. All leads in the selected categories will be added to your campaign.</p>
                    <p>Only leads with valid email addresses will be included in the campaign.</p>
                    <p>Leads that are already in the campaign will not be added again.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}