# WebSocket Testing Tools

This repository contains tools for testing WebSocket communication with the voicebot endpoint at `wss://silo.intelliclabs.com/ws/voicebot/websocket/`.

## Available Testing Tools

There are two different tools you can use for testing:

1. **Python Command-Line Client** (`websocket_client_test.py`)
2. **HTML/JavaScript Web Interface** (`templates/voicebot/websocket_test.html`)

Both tools allow you to connect to the WebSocket endpoint and send various message types to test the communication.

## 1. Python Command-Line Client

### Requirements

- Python 3.7+
- `websockets` library (install with `pip install websockets`)

### Running the Client

```bash
python websocket_client_test.py
```

### Usage

The Python client provides an interactive command-line interface with the following commands:

- `start` - Send a start message to initiate a session
- `speech <text>` - Send a speech message with the specified text
- `dtmf <digits>` - Send DTMF (touch-tone) digits
- `mark` - Send a mark message for tracking
- `exit` - Exit the program

Example session:

```
Available commands:
1. start - Send a start message
2. speech <text> - Send a speech message with optional text
3. dtmf <digits> - Send DTMF digits
4. mark - Send a mark message
5. exit - Exit the program

Enter command: start
[INFO] Sending message: {
  "type": "start",
  "callSid": "test_call_20230615123456"
}

Enter command: speech Hello, how can you help me today?
[INFO] Sending message: {
  "type": "speech",
  "callSid": "test_call_20230615123456",
  "transcript": "Hello, how can you help me today?"
}
```

## 2. HTML/JavaScript Web Interface

### Opening the Interface

Open the file `templates/voicebot/websocket_test.html` in a web browser.

### Usage

The web interface provides a user-friendly way to test WebSocket communication:

1. **Connection Panel**:
   - Enter the WebSocket URL (default: `wss://silo.intelliclabs.com/ws/voicebot/websocket/`)
   - Click "Connect" to establish a connection
   - Click "Disconnect" to close the connection

2. **Message Composition**:
   - Select a message type from the dropdown (start, speech, dtmf, mark)
   - Fill in the required fields for the selected message type
   - Click "Send Message" to send the message

3. **Raw JSON Editor**:
   - View and edit the raw JSON message
   - Click "Send Raw JSON" to send a custom JSON message

4. **Message Log**:
   - View sent and received messages with timestamps
   - Click "Clear Log" to clear the message log

## Message Types

Both tools support the following message types:

1. **start** - Initiates a new session
   ```json
   {
     "type": "start",
     "callSid": "test_call_20230615123456"
   }
   ```

2. **speech** - Sends transcribed speech
   ```json
   {
     "type": "speech",
     "callSid": "test_call_20230615123456",
     "transcript": "Hello, how are you today?"
   }
   ```

3. **dtmf** - Sends touch-tone input
   ```json
   {
     "type": "dtmf",
     "callSid": "test_call_20230615123456",
     "digits": "1"
   }
   ```

4. **mark** - Sends a tracking mark
   ```json
   {
     "type": "mark",
     "callSid": "test_call_20230615123456",
     "name": "test_mark"
   }
   ```

## Notes

- Always start a session with a `start` message before sending other message types
- The `callSid` is automatically generated for `start` messages and reused for subsequent messages
- The server should respond with messages that will be displayed in the log