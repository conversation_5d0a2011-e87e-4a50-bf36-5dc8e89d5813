from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from apps.authentication.models import User


class EmailAccount(models.Model):
    """
    Model to store email account credentials and settings for IMAP/SMTP access.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='email_accounts')
    account_name = models.CharField(_('Account Name'), max_length=255)
    email_address = models.EmailField(_('Email Address'))

    # IMAP settings
    imap_server = models.CharField(_('IMAP Server'), max_length=255)
    imap_port = models.IntegerField(_('IMAP Port'), default=993)
    imap_use_ssl = models.BooleanField(_('IMAP Use SSL'), default=True)

    # SMTP settings
    smtp_server = models.CharField(_('SMTP Server'), max_length=255)
    smtp_port = models.IntegerField(_('SMTP Port'), default=587)
    smtp_use_tls = models.BooleanField(_('SMTP Use TLS'), default=True)
    smtp_use_ssl = models.BooleanField(_('SMTP Use SSL'), default=False)

    # Credentials (encrypted)
    email_password = models.CharField(_('Email Password'), max_length=255)

    # Signature
    signature_text = models.TextField(_('Text Signature'), blank=True, null=True)
    signature_html = models.TextField(_('HTML Signature'), blank=True, null=True)
    use_signature = models.BooleanField(_('Use Signature'), default=True)

    # Settings
    is_active = models.BooleanField(_('Is Active'), default=True)
    auto_process = models.BooleanField(_('Auto Process'), default=False)
    auto_send = models.BooleanField(_('Auto Send'), default=False)
    last_checked = models.DateTimeField(_('Last Checked'), null=True, blank=True)

    is_bulk_mail_account = models.BooleanField(_('Is this a bulk email account?'), default=True)
    is_warming_up = models.BooleanField(
        _('Is this account warming up?'),
        default=True,
        help_text=_('Accounts warming up are not usable for sending emails.')
    )

    # History import
    import_history = models.BooleanField(_('Import History'), default=False)
    history_imported = models.BooleanField(_('History Imported'), default=False)
    history_import_date = models.DateTimeField(_('History Import Date'), null=True, blank=True)

    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "email_account"
        verbose_name = _("Email Account")
        verbose_name_plural = _("Email Accounts")

    def __str__(self):
        return f"{self.account_name} ({self.email_address})"


class TrainingMaterial(models.Model):
    """
    Model to store training materials for the LLM to use when generating responses.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='training_materials')
    name = models.CharField(_('Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True, null=True)
    content = models.TextField(_('Content'))

    is_active = models.BooleanField(_('Is Active'), default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "email_training_material"
        verbose_name = _("Training Material")
        verbose_name_plural = _("Training Materials")

    def __str__(self):
        return self.name


class EmailTemplate(models.Model):
    """
    Model to store email templates for responses.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='email_templates')
    name = models.CharField(_('Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True, null=True)
    subject = models.CharField(_('Subject'), max_length=255, blank=True, null=True)
    body = models.TextField(_('Body'))

    is_active = models.BooleanField(_('Is Active'), default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "email_template"
        verbose_name = _("Email Template")
        verbose_name_plural = _("Email Templates")

    def __str__(self):
        return self.name


class EmailAccountTrainingMaterial(models.Model):
    """
    Model to associate training materials with email accounts.
    """
    email_account = models.ForeignKey(EmailAccount, on_delete=models.CASCADE, related_name='training_materials')
    training_material = models.ForeignKey(TrainingMaterial, on_delete=models.CASCADE, related_name='email_accounts')

    date_created = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "email_account_training_material"
        verbose_name = _("Email Account Training Material")
        verbose_name_plural = _("Email Account Training Materials")
        unique_together = ('email_account', 'training_material')

    def __str__(self):
        return f"{self.email_account} - {self.training_material}"


class EmailHistory(models.Model):
    """
    Model to store email history for training and reference.
    """
    email_account = models.ForeignKey(EmailAccount, on_delete=models.CASCADE, related_name='email_history')
    message_id = models.CharField(_('Message ID'), max_length=255)
    conversation_id = models.CharField(_('Conversation ID'), max_length=255, blank=True, null=True)

    sender = models.CharField(_('Sender'), max_length=255)
    sender_email = models.EmailField(_('Sender Email'))
    recipients = models.TextField(_('Recipients'))
    cc = models.TextField(_('CC'), blank=True, null=True)
    bcc = models.TextField(_('BCC'), blank=True, null=True)

    subject = models.CharField(_('Subject'), max_length=255)
    body_text = models.TextField(_('Body Text'))
    body_html = models.TextField(_('Body HTML'), blank=True, null=True)

    received_date = models.DateTimeField(_('Received Date'))
    is_processed = models.BooleanField(_('Is Processed'), default=False)
    is_replied = models.BooleanField(_('Is Replied'), default=False)

    date_created = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "email_history"
        verbose_name = _("Email History")
        verbose_name_plural = _("Email History")
        unique_together = ('email_account', 'message_id')

    def __str__(self):
        return f"{self.subject} ({self.sender_email})"


class EmailDraft(models.Model):
    """
    Model to store draft responses generated by the LLM.
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('generated', _('Generated')),
        ('approved', _('Approved')),
        ('sent', _('Sent')),
        ('rejected', _('Rejected')),
    )

    email_account = models.ForeignKey(EmailAccount, on_delete=models.CASCADE, related_name='email_drafts')
    original_email = models.ForeignKey(EmailHistory, on_delete=models.CASCADE, related_name='drafts')

    subject = models.CharField(_('Subject'), max_length=255)
    body_text = models.TextField(_('Body Text'))
    body_html = models.TextField(_('Body HTML'), blank=True, null=True)

    recipients = models.TextField(_('Recipients'))
    cc = models.TextField(_('CC'), blank=True, null=True)
    bcc = models.TextField(_('BCC'), blank=True, null=True)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')
    imap_uid = models.CharField(_('IMAP UID'), max_length=255, blank=True, null=True)

    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    date_sent = models.DateTimeField(_('Date Sent'), blank=True, null=True)

    class Meta:
        db_table = "email_draft"
        verbose_name = _("Email Draft")
        verbose_name_plural = _("Email Drafts")

    def __str__(self):
        return f"Draft: {self.subject}"
