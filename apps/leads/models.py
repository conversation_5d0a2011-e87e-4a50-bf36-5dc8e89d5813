import datetime

from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.db import models

from django.contrib.auth.base_user import AbstractBase<PERSON><PERSON>, BaseUserManager
from django.contrib.auth.models import PermissionsMixin, UserManager
from django.contrib.auth.validators import UnicodeUsernameValidator
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from phonenumber_field.modelfields import PhoneN<PERSON>ber<PERSON>ield
from timezone_field import TimeZoneField
from apps.authentication.models import User, UserType, SubscriptionType, PurchaseType


class LeadNote(models.Model):
    '''
    '''
    user = models.ForeignKey(
        User,
        models.CASCADE,
        related_name="lead_note_user",
        verbose_name="User Who Added",
        db_column="user",
        help_text='',
        blank=True, null=True
    )
    note = models.TextField(blank=True, null=True)
    date_created = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "lead_note"
        verbose_name = _("Lead Note")
        verbose_name_plural = _("Lead Notes")

    def __str__(self):
        return f'{self.note}'


class LeadCategory(models.Model):
    '''
    '''
    name = models.CharField(max_length=100, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    is_active = models.BooleanField('Is Active', default=True)

    class Meta:
        db_table = "lead_category"
        verbose_name = _("Lead Category")
        verbose_name_plural = _("Lead Categories")

    def __str__(self):
        return f'{self.name}'


class LeadUpload(models.Model):
    '''
    '''
    FIELD_MAPS = [
        ['index_first_name', 'first_name', False],
        ['index_last_name', 'last_name', False],
        ['index_address', 'address', False],
        ['index_city', 'city', False],
        ['index_state', 'state', False],
        ['index_zip', 'zip', False],
        ['index_date_of_birth', 'date_of_birth', False],
        ['index_phone_home', 'phone_home', True],
        ['index_phone_work', 'phone_work', True],
        ['index_phone_cell', 'phone_cell', True],
        ['index_email', 'email', False],
        ['index_ip_address', 'ip_address', False]
    ]

    user = models.ForeignKey(
        User,
        models.CASCADE,
        related_name="lead_upload_user",
        verbose_name="User Who Added",
        db_column="user",
        help_text='',
        blank=True, null=True
    )
    lead_category = models.ForeignKey(
        LeadCategory,
        models.CASCADE,
        related_name="lead_upload_lead_category",
        verbose_name="Lead Category",
        db_column="lead_category",
        help_text='',
        blank=True, null=True
    )
    name = models.CharField(max_length=100, blank=True, null=True, help_text='For internal use only')
    lead_file = models.FileField(upload_to='uploads/leads/%Y/%m/%d/', blank=True, null=True)
    lead_file_name = models.CharField(max_length=200, blank=True, null=True)

    index_first_name = models.IntegerField(blank=True, default=-1)
    index_last_name = models.IntegerField(blank=True, default=-1)
    index_address = models.IntegerField(blank=True, default=-1)
    index_city = models.IntegerField(blank=True, default=-1)
    index_state = models.IntegerField(blank=True, default=-1)
    index_zip = models.IntegerField(blank=True, default=-1)
    index_date_of_birth = models.IntegerField(blank=True, default=-1)
    index_phone_home = models.IntegerField(blank=True, default=-1)
    index_phone_work = models.IntegerField(blank=True, default=-1)
    index_phone_cell = models.IntegerField(blank=True, default=-1)
    index_email = models.IntegerField(blank=True, default=-1)
    index_ip_address = models.IntegerField(blank=True, default=-1)

    skip_header = models.BooleanField(default=False)
    was_loaded = models.BooleanField(default=False)

    date_created = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "lead_upload"
        verbose_name = _("Lead Upload")
        verbose_name_plural = _("Lead Uploads")

    def __str__(self):
        return f'{self.name}'











class Lead(models.Model):
    '''
    '''
    user = models.ForeignKey(
        User,
        models.CASCADE,
        related_name="lead_user",
        verbose_name="User Who Added",
        db_column="user",
        help_text='',
        blank=True, null=True
    )
    lead_category = models.ForeignKey(
        LeadCategory,
        models.CASCADE,
        related_name="lead_lead_category",
        verbose_name="Lead Category",
        db_column="lead_category",
        help_text='',
        blank=True, null=True
    )
    lead_upload = models.ForeignKey(
        LeadUpload,
        models.CASCADE,
        related_name="lead_lead_upload",
        verbose_name="Lead Upload",
        db_column="lead_upload",
        help_text='',
        blank=True, null=True
    )
    chat_bot_sequence = models.ForeignKey(
        'chat.ChatBotSequence',
        models.CASCADE,
        related_name="lead_chat_bot_sequence",
        verbose_name="Chat Bot Sequence",
        db_column="chat_bot_sequence",
        help_text="The last chat bot sequence this user is being contacted by.",
        blank=True, null=True
    )
    chat_bot_last_contacted_by = models.ForeignKey(
        'chat.ChatBot',
        models.CASCADE,
        related_name="lead_chat_bot_last_contacted_by",
        verbose_name="Chat Bot Last Contacted By",
        db_column="chat_bot_last_contacted_by",
        help_text="The last chat bot that contacted this lead.",
        blank=True, null=True
    )
    full_name = models.CharField(max_length=255, blank=True, null=True)
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, blank=True, null=True)
    company = models.CharField(max_length=255, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    address_full = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=50, blank=True, null=True)
    state = models.CharField(max_length=50, blank=True, null=True)
    zip = models.CharField(max_length=50, blank=True, null=True)
    date_of_birth = models.CharField(max_length=50, blank=True, null=True)
    phone_home = models.CharField(max_length=15, blank=True, null=True)
    phone_home_validated = models.BooleanField(default=False)
    phone_home_is_mobile = models.BooleanField(default=False)
    phone_work = models.CharField(max_length=15, blank=True, null=True)
    phone_work_validated = models.BooleanField(default=False)
    phone_cell = models.CharField(max_length=15, blank=True, null=True)
    phone_cell_validated = models.BooleanField(default=False)
    email = models.CharField(max_length=100, blank=True, null=True)
    email_validated = models.BooleanField(default=False)
    ip_address = models.CharField(max_length=100, blank=True, null=True)

    do_not_call = models.BooleanField(default=False)
    opted_out = models.BooleanField(default=False)
    store_hours = models.JSONField(blank=True, null=True)
    scrape_api = models.JSONField(blank=True, null=True)

    crm_key = models.CharField(max_length=100, blank=True, null=True)

    lead_notes = models.ManyToManyField(LeadNote, blank=True)

    contacts = models.ManyToManyField('chat.ChatBotContact', blank=True)

    is_active = models.BooleanField('Is Active', default=True)

    date_added = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "lead"
        verbose_name = _("Lead")
        verbose_name_plural = _("Leads")

    def __str__(self):
        return f'{self.first_name} {self.last_name}'

    def save(self, *args, **kwargs):
        # If full_name is blank but first_name and last_name exist, populate full_name
        if not self.full_name and self.first_name and self.last_name:
            self.full_name = f'{self.first_name} {self.last_name}'

        # Call the original save() method
        super(Lead, self).save(*args, **kwargs)




class ScrapeJobCategory(models.Model):
    '''
    '''
    name = models.CharField(max_length=100, blank=True, null=True)
    value = models.CharField(max_length=100, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    is_active = models.BooleanField('Is Active', default=True)

    class Meta:
        db_table = "scrape_job_category"
        verbose_name = _("Scrape Job Category")
        verbose_name_plural = _("Scrape Job Categories")

    def __str__(self):
        return f'{self.name}'




class ScrapeJob(models.Model):
    SCRAPE_STATUS_WAITING = 0
    SCRAPE_STATUS_WORKING = 1
    SCRAPE_STATUS_COMPLETE = 2
    SCRAPE_STATUS_ERROR = 3
    SCRAPE_STATUS = (
        (SCRAPE_STATUS_WAITING, _('Waiting')),
        (SCRAPE_STATUS_WORKING, _('Working')),
        (SCRAPE_STATUS_COMPLETE, _('Complete')),
        (SCRAPE_STATUS_ERROR, _('Error'))
    )
    JOB_TYPE_GOOGLE = 0
    JOB_TYPE_LINKEDIN = 1
    JOB_TYPE_YELP = 2
    JOB_TYPE_YELLOW_PAGES = 3
    JOB_TYPE_AI_PAGE = 4
    JOB_TYPE_AI_DIRECTORY = 5
    JOB_TYPE = (
        (0, _('Google Map')),
        (1, _('LinkedIn')),
        (2, _('Yelp')),
        (3, _('Yellow Pages')),
        (4, _('AI Web Page Scrape')),
        (5, _('AI Directory Scrape?'))
    )
    job_type = models.IntegerField(
        default=0,
        choices=JOB_TYPE,
        help_text='',
    )
    user = models.ForeignKey(
        User,
        models.CASCADE,
        related_name="scrape_job_user",
        verbose_name="User Who Added",
        db_column="user",
        help_text='',
        blank=True, null=True
    )
    name = models.CharField(max_length=150, blank=True, null=True)
    search_term = models.CharField(max_length=150, blank=True, null=True)
    location = models.CharField(max_length=150, blank=True, null=True)
    lead_quantity = models.IntegerField(blank=True, default=50)
    language = models.CharField(max_length=5, default='en')
    category = models.CharField(max_length=100, blank=True, null=True)
    lead_category = models.ForeignKey(
        LeadCategory,
        models.CASCADE,
        related_name="scrape_job_lead_category",
        verbose_name="Lead Category",
        db_column="lead_category",
        help_text='',
        blank=True, null=True
    )

    leads_scraped_count = models.IntegerField(blank=True, null=True)
    scrape_status = models.IntegerField(
        default=0,
        choices=SCRAPE_STATUS,
        help_text='',
    )
    scrape_status_percent = models.IntegerField(blank=True, default=0)

    date_created = models.DateTimeField(default=timezone.now)
    date_run = models.DateTimeField(blank=True, null=True)
    date_completed = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = "scrape_job"
        verbose_name = _("Scrape Job")
        verbose_name_plural = _("Scrape Jobs")

    def __str__(self):
        return f'{self.name}'

    def start_run(self):
        self.date_run = timezone.now()

    def end_run(self):
        self.date_completed = timezone.now()


