from django.urls import path, re_path
from django.urls import re_path
from django.contrib.auth.views import LogoutView
from django.conf import settings

from . import views
from . import api

urlpatterns = [

    re_path(r'^create_scrape/$', views.view_create_scrape, name='leads_create_scrape'),

    re_path(
        r'^add_note/(?P<pk>[\-\d]+)/$',
        views.add_note_view,
        name='leads_add_note'
    ),

    re_path(
        r'^view/(?P<pk>[\-\d]+)/$',
        views.view_lead,
        name='leads_view_lead'
    ),

    re_path(r'^list/$', views.view_list, name='leads_view_list'),

    re_path(r'^upload/$', views.view_upload, name='leads_view_upload'),

    # view_categories
    re_path(r'^categories/$', views.view_categories, name='leads_view_categories'),

    re_path(
        r'^upload/(?P<pk>[\-\d]+)/$',
        views.view_upload_map,
        name='leads_view_upload_map'
    ),

    #LocationAutocompleteAPI

    re_path(
        r'^api/locations/$',
        api.LocationAutocompleteAPI.as_view(),
        name='lead_api_locations'
    ),

    re_path(
        r'^api/upload_lead_file/$',
        api.UploadLeadFileAPI.as_view(),
        name='lead_api_upload_lead_file'
    ),

    re_path(
        r'^api/call/(?P<pk>[\-\d]+)/(?P<phone>[\-\d]+)/$',
        api.MakeBotCallAPI.as_view(),
        name='lead_api_call'
    ),

    re_path(
        r'^api/sms/(?P<pk>[\-\d]+)/(?P<phone>[\-\d]+)/$',
        api.MakeBotSMSAPI.as_view(),
        name='lead_api_sms'
    ),

    re_path(
        r'^api/leads/$',
        views.view_api_leads,
        name='lead_api_leads'
    ),

    # Matches any html file
    re_path(r'^.*\.html', views.pages, name='leads_pages'),
]
