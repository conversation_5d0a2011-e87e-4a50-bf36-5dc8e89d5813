import logging
import smtplib
import email.utils
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import time

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction

from apps.email_warmup.models import WarmupEmail

logger = logging.getLogger(__name__)

BUSINESS_HOURS_START = 7
BUSINESS_HOURS_END = 19
WEEKDAYS = {0, 1, 2, 3, 4}

class Command(BaseCommand):
    help = 'Send warmup emails that are scheduled and ready to be sent.'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting to send scheduled warm-up emails...'))

        now = timezone.localtime()
        if now.weekday() not in WEEKDAYS or not (time(BUSINESS_HOURS_START) <= now.time() <= time(BUSINESS_HOURS_END)):
            self.stdout.write(self.style.WARNING('Current time is outside of allowed warm-up sending window (M-F 7AM–7PM).'))
            return

        pending_emails = WarmupEmail.objects.filter(status='pending', scheduled_time__lte=timezone.now())
        sent_count = 0

        for email_obj in pending_emails.select_related('campaign__email_account', 'trusted_account'):
            try:
                with transaction.atomic():
                    from_account, to_account, smtp_info = self.determine_smtp(email_obj)
                    if not smtp_info:
                        self.stdout.write(f"Skipping {email_obj.subject} — missing SMTP configuration.")
                        continue

                    msg = self.build_message(email_obj, from_account.email_address, to_account.email_address)
                    success = self.send_email(msg, smtp_info)

                    if success:
                        email_obj.status = 'sent'
                        email_obj.sent_time = timezone.now()
                        email_obj.message_id = msg['Message-ID']
                        email_obj.save()
                        sent_count += 1
                        self.stdout.write(f"Sent: {email_obj.subject}")
                    else:
                        email_obj.status = 'failed'
                        email_obj.save()
            except Exception as e:
                logger.error(f"Error sending email {email_obj.id}: {str(e)}")

        self.stdout.write(self.style.SUCCESS(f"Total warm-up emails sent: {sent_count}"))

    def determine_smtp(self, email_obj):
        if email_obj.email_type == 'initial':
            account = email_obj.campaign.email_account
            smtp = {
                'server': account.smtp_server,
                'port': account.smtp_port,
                'username': account.email_address,
                'password': account.email_password,
                'use_tls': account.smtp_use_tls,
                'use_ssl': account.smtp_use_ssl
            }
            return account, email_obj.trusted_account, smtp
        else:
            account = email_obj.trusted_account
            if not account.smtp_server or not account.email_password:
                return account, email_obj.campaign.email_account, None
            smtp = {
                'server': account.smtp_server,
                'port': account.smtp_port,
                'username': account.email_address,
                'password': account.email_password,
                'use_tls': account.smtp_use_tls,
                'use_ssl': account.smtp_use_ssl
            }
            return account, email_obj.campaign.email_account, smtp

    def build_message(self, email_obj, from_email, to_email):
        msg = MIMEMultipart('alternative')
        msg['Subject'] = email_obj.subject
        msg['From'] = from_email
        msg['To'] = to_email
        msg['Date'] = email.utils.formatdate(localtime=True)
        msg['Message-ID'] = email.utils.make_msgid(domain=from_email.split('@')[1])

        if email_obj.conversation_id:
            msg['X-Conversation-ID'] = email_obj.conversation_id
        if email_obj.parent_email and email_obj.parent_email.message_id:
            msg['In-Reply-To'] = email_obj.parent_email.message_id
            msg['References'] = email_obj.parent_email.message_id

        msg.attach(MIMEText(email_obj.body_text, 'plain'))
        if email_obj.body_html:
            msg.attach(MIMEText(email_obj.body_html, 'html'))

        return msg

    def send_email(self, msg, smtp):
        try:
            if smtp['use_ssl']:
                server = smtplib.SMTP_SSL(smtp['server'], smtp['port'])
            else:
                server = smtplib.SMTP(smtp['server'], smtp['port'])

            server.ehlo()
            if smtp['use_tls']:
                server.starttls()
                server.ehlo()
            server.login(smtp['username'], smtp['password'])
            server.sendmail(msg['From'], [msg['To']], msg.as_string())
            server.quit()
            return True
        except Exception as e:
            logger.error(f"SMTP send failed: {str(e)}")
            return False
