# Generated by Django 5.1.3 on 2025-06-18 07:09

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("email", "0004_auto_20250414_0139"),
    ]

    operations = [
        migrations.CreateModel(
            name="TrustedEmailAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "email_address",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="Email Address"
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date_created", models.DateTime<PERSON>ield(auto_now_add=True)),
                ("date_updated", models.DateTime<PERSON>ield(auto_now=True)),
            ],
            options={
                "verbose_name": "Trusted Email Account",
                "verbose_name_plural": "Trusted Email Accounts",
                "db_table": "emailwarmup_trusted_account",
            },
        ),
        migrations.CreateModel(
            name="WarmupCampaign",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, verbose_name="Campaign Name"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "start_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Start Date"
                    ),
                ),
                (
                    "end_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="End Date"
                    ),
                ),
                (
                    "current_week",
                    models.PositiveIntegerField(default=1, verbose_name="Current Week"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("active", "Active"),
                            ("paused", "Paused"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "email_account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warmup_campaigns",
                        to="email.emailaccount",
                    ),
                ),
            ],
            options={
                "verbose_name": "Warmup Campaign",
                "verbose_name_plural": "Warmup Campaigns",
                "db_table": "emailwarmup_campaign",
            },
        ),
        migrations.CreateModel(
            name="WarmupEmail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "conversation_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Conversation ID",
                    ),
                ),
                (
                    "email_type",
                    models.CharField(
                        choices=[
                            ("initial", "Initial Email"),
                            ("reply", "Reply Email"),
                        ],
                        max_length=20,
                        verbose_name="Email Type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("replied", "Replied"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                ("subject", models.CharField(max_length=255, verbose_name="Subject")),
                ("body_text", models.TextField(verbose_name="Body Text")),
                (
                    "body_html",
                    models.TextField(blank=True, null=True, verbose_name="Body HTML"),
                ),
                ("scheduled_time", models.DateTimeField(verbose_name="Scheduled Time")),
                (
                    "sent_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Sent Time"
                    ),
                ),
                (
                    "message_id",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Message ID"
                    ),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "campaign",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="emails",
                        to="email_warmup.warmupcampaign",
                    ),
                ),
                (
                    "parent_email",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="replies",
                        to="email_warmup.warmupemail",
                    ),
                ),
                (
                    "trusted_account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warmup_emails",
                        to="email_warmup.trustedemailaccount",
                    ),
                ),
            ],
            options={
                "verbose_name": "Warmup Email",
                "verbose_name_plural": "Warmup Emails",
                "db_table": "emailwarmup_email",
            },
        ),
        migrations.CreateModel(
            name="WarmupCampaignSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "week_number",
                    models.PositiveIntegerField(verbose_name="Week Number"),
                ),
                (
                    "min_emails_per_day",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Min Emails Per Day",
                    ),
                ),
                (
                    "max_emails_per_day",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Max Emails Per Day",
                    ),
                ),
                (
                    "min_replies_per_thread",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Min Replies Per Thread",
                    ),
                ),
                (
                    "max_replies_per_thread",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Max Replies Per Thread",
                    ),
                ),
                (
                    "min_reply_delay_minutes",
                    models.PositiveIntegerField(
                        default=30,
                        validators=[django.core.validators.MinValueValidator(30)],
                        verbose_name="Min Reply Delay (minutes)",
                    ),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "campaign",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settings",
                        to="email_warmup.warmupcampaign",
                    ),
                ),
            ],
            options={
                "verbose_name": "Warmup Campaign Settings",
                "verbose_name_plural": "Warmup Campaign Settings",
                "db_table": "emailwarmup_campaign_settings",
                "unique_together": {("campaign", "week_number")},
            },
        ),
    ]
