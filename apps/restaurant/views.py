import os
import pytz
import pandas as pd

from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse
from django.contrib import messages
from django.conf import settings
from django.utils import timezone

from io import BytesIO

from .models import Member, MemberAction
from .forms import MemberForm, MemberActionForm
from .utils import generate_member_card
from utils.api.twilio import TwilioUtility

def register_member(request):
    """
    View for registering a new member with name and phone number.
    Sends a text message with the member card image.
    """
    if request.method == 'POST':
        form = MemberForm(request.POST)
        if form.is_valid():
            # Clean the phone number
            cleaned_phone = ''.join(filter(str.isdigit, form.cleaned_data['phone_number']))

            # Check for existing member with this phone number
            if Member.objects.filter(phone_number=cleaned_phone).exists():
                messages.error(request, "A member with this phone number already exists.")
                return redirect('restaurant:register_member')

            # Create member instance but don't save yet
            member = form.save(commit=False)
            member.phone_number = cleaned_phone
            member.save()

            # Generate the member card with name and QR code
            card_path = generate_member_card(member.name, member.id)

            # Get the full path to the image
            full_path = os.path.join(settings.MEDIA_ROOT, card_path)

            # Send text message with the image
            try:
                twilio = TwilioUtility()
                message = f"Welcome to CoinOp, {member.name}! Here's your membership card.\n\nPlease visit https://coinopgameroom.com/deals/ for member benefits.\n\nSearch your texts for CoinOp anytime you need to find this URL."
                twilio.send_text_with_image(member.phone_number, message, card_path)

                messages.success(request, f"Member {member.name} registered successfully! A text message has been sent.")
                return redirect('restaurant:member_action')
            except Exception as e:
                messages.error(request, f"Error sending text message: {str(e)}")
                return redirect('restaurant:register_member')
    else:
        form = MemberForm()

    return render(request, 'restaurant/register_member.html', {'form': form})

def member_action(request):
    """
    View for recording member actions by scanning QR code and entering text.
    """
    if request.method == 'POST':
        form = MemberActionForm(request.POST)
        if form.is_valid():
            # Get the member ID from the QR code
            member_id = form.cleaned_data['qr_code']

            try:
                # Find the member
                member = Member.objects.get(id=member_id)

                # Create the member action
                action = MemberAction(
                    member=member,
                    action_text=form.cleaned_data['action_text']
                )
                action.save()

                messages.success(request, f"Action recorded for {member.name}!")
                return redirect('restaurant:member_action')
            except Member.DoesNotExist:
                messages.error(request, "Invalid QR code. Member not found.")
                return redirect('restaurant:member_action')
    else:
        form = MemberActionForm()

    return render(request, 'restaurant/member_action.html', {'form': form})

def export_actions(request):
    """
    View for exporting all member actions to an Excel file.
    """
    # Get all member actions with related member data
    actions = MemberAction.objects.select_related('member').all()

    # Get PST timezone
    pst = pytz.timezone('America/Los_Angeles')


    # Create a DataFrame with the data
    data = []
    for action in actions:
        data.append({
            'Member Name': action.member.name,
            'Phone Number': action.member.phone_number,
            'Action Text': action.action_text,
            'Created At': action.created_at.astimezone(pst).replace(tzinfo=None)
        })

    df = pd.DataFrame(data)

    # Create an Excel file
    output = BytesIO()
    writer = pd.ExcelWriter(output, engine='xlsxwriter')
    df.to_excel(writer, sheet_name='Member Actions', index=False)
    writer.close()
    output.seek(0)

    # Create the HTTP response with the Excel file
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename=member_actions.xlsx'

    return response
