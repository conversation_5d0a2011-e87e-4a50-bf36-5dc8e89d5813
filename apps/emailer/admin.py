from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import (
    EmailTemplate,
    Campaign,
    EmailSequence,
    SequenceMessage,
    CampaignLead,
    CampaignEmail,
    EmailAccountDailyLimit
)


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'is_active', 'date_created', 'date_updated')
    list_filter = ('is_active', 'user', 'date_created')
    search_fields = ('name', 'subject')
    readonly_fields = ('date_created', 'date_updated')
    
    fieldsets = (
        (None, {
            'fields': ('user', 'name', 'subject', 'is_active')
        }),
        ('Content', {
            'fields': ('body_text', 'body_html'),
            'classes': ('wide',)
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        }),
    )


class SequenceMessageInline(admin.TabularInline):
    model = SequenceMessage
    extra = 1
    fields = ('name', 'subject', 'template', 'delay_days', 'order', 'is_active')
    ordering = ('order',)


@admin.register(EmailSequence)
class EmailSequenceAdmin(admin.ModelAdmin):
    list_display = ('name', 'campaign', 'message_count', 'is_active', 'date_created')
    list_filter = ('is_active', 'campaign__status', 'date_created')
    search_fields = ('name', 'campaign__name')
    readonly_fields = ('date_created', 'date_updated')
    inlines = [SequenceMessageInline]
    
    def message_count(self, obj):
        return obj.messages.count()
    message_count.short_description = 'Messages'


@admin.register(SequenceMessage)
class SequenceMessageAdmin(admin.ModelAdmin):
    list_display = ('name', 'sequence', 'campaign', 'delay_days', 'order', 'is_active')
    list_filter = ('is_active', 'sequence__campaign__status')
    search_fields = ('name', 'subject', 'sequence__name', 'sequence__campaign__name')
    readonly_fields = ('date_created', 'date_updated')
    
    fieldsets = (
        (None, {
            'fields': ('sequence', 'template', 'name', 'order', 'delay_days', 'is_active')
        }),
        ('Email Content', {
            'fields': ('subject', 'body_text', 'body_html'),
            'classes': ('wide',)
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        }),
    )
    
    def campaign(self, obj):
        return obj.sequence.campaign.name
    campaign.short_description = 'Campaign'


class CampaignLeadInline(admin.TabularInline):
    model = CampaignLead
    extra = 0
    readonly_fields = ('date_added', 'date_updated', 'current_sequence_position')
    fields = ('lead', 'status', 'current_sequence_position', 'date_added')


@admin.register(Campaign)
class CampaignAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'target_lead_category', 'status', 'lead_count', 'start_date', 'is_active')
    list_filter = ('status', 'is_active', 'target_lead_category', 'date_created')
    search_fields = ('name', 'description')
    readonly_fields = ('date_created', 'date_updated')
    inlines = [CampaignLeadInline]
    
    fieldsets = (
        (None, {
            'fields': ('user', 'name', 'description', 'target_lead_category', 'status', 'is_active')
        }),
        ('Schedule', {
            'fields': ('start_date', 'end_date'),
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        }),
    )
    
    def lead_count(self, obj):
        return obj.campaign_leads.count()
    lead_count.short_description = 'Leads'


@admin.register(CampaignLead)
class CampaignLeadAdmin(admin.ModelAdmin):
    list_display = ('campaign', 'lead', 'status', 'current_sequence_position', 'date_added')
    list_filter = ('status', 'campaign__status', 'date_added')
    search_fields = ('campaign__name', 'lead__first_name', 'lead__last_name', 'lead__email')
    readonly_fields = ('date_added', 'date_updated')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('campaign', 'lead')


@admin.register(CampaignEmail)
class CampaignEmailAdmin(admin.ModelAdmin):
    list_display = ('subject', 'campaign', 'lead', 'email_account', 'status', 'scheduled_time', 'sent_time')
    list_filter = ('status', 'campaign__status', 'scheduled_time', 'sent_time')
    search_fields = ('subject', 'campaign__name', 'campaign_lead__lead__email')
    readonly_fields = ('date_created', 'date_updated', 'message_id')
    
    fieldsets = (
        (None, {
            'fields': ('campaign', 'campaign_lead', 'sequence_message', 'email_account', 'status')
        }),
        ('Email Content', {
            'fields': ('subject', 'body_text', 'body_html'),
            'classes': ('wide',)
        }),
        ('Schedule & Delivery', {
            'fields': ('scheduled_time', 'sent_time', 'message_id'),
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        }),
    )
    
    def campaign(self, obj):
        return obj.campaign.name
    campaign.short_description = 'Campaign'
    
    def lead(self, obj):
        return str(obj.campaign_lead.lead)
    lead.short_description = 'Lead'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'campaign', 'campaign_lead__lead', 'email_account'
        )


@admin.register(EmailAccountDailyLimit)
class EmailAccountDailyLimitAdmin(admin.ModelAdmin):
    list_display = ('email_account', 'date', 'emails_sent', 'daily_limit', 'capacity_status')
    list_filter = ('date', 'email_account')
    search_fields = ('email_account__email_address', 'email_account__account_name')
    readonly_fields = ('capacity_status', 'remaining_capacity')
    
    def capacity_status(self, obj):
        if obj.has_capacity:
            return format_html(
                '<span style="color: green;">✓ Available ({}/{})</span>',
                obj.emails_sent, obj.daily_limit
            )
        else:
            return format_html(
                '<span style="color: red;">✗ At Limit ({}/{})</span>',
                obj.emails_sent, obj.daily_limit
            )
    capacity_status.short_description = 'Capacity Status'
    
    def remaining_capacity(self, obj):
        return obj.remaining_capacity
    remaining_capacity.short_description = 'Remaining'
