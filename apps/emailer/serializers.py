from rest_framework import serializers
from django.utils import timezone

from .models import (
    EmailTemplate,
    Campaign,
    EmailSequence,
    SequenceMessage,
    CampaignLead,
    CampaignEmail,
    EmailAccountDailyLimit
)
from apps.leads.models import Lead, LeadCategory
from apps.email.models import EmailAccount


class EmailTemplateSerializer(serializers.ModelSerializer):
    """Serializer for EmailTemplate model."""
    
    class Meta:
        model = EmailTemplate
        fields = [
            'id', 'name', 'subject', 'body_text', 'body_html',
            'is_active', 'date_created', 'date_updated'
        ]
        read_only_fields = ['id', 'date_created', 'date_updated']

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class SequenceMessageSerializer(serializers.ModelSerializer):
    """Serializer for SequenceMessage model."""
    template_name = serializers.CharField(source='template.name', read_only=True)
    
    class Meta:
        model = SequenceMessage
        fields = [
            'id', 'name', 'subject', 'body_text', 'body_html',
            'template', 'template_name', 'delay_days', 'order',
            'is_active', 'date_created', 'date_updated'
        ]
        read_only_fields = ['id', 'date_created', 'date_updated']


class EmailSequenceSerializer(serializers.ModelSerializer):
    """Serializer for EmailSequence model."""
    messages = SequenceMessageSerializer(many=True, read_only=True)
    message_count = serializers.IntegerField(source='messages.count', read_only=True)
    
    class Meta:
        model = EmailSequence
        fields = [
            'id', 'name', 'description', 'messages', 'message_count',
            'is_active', 'date_created', 'date_updated'
        ]
        read_only_fields = ['id', 'date_created', 'date_updated']


class CampaignLeadSerializer(serializers.ModelSerializer):
    """Serializer for CampaignLead model."""
    lead_name = serializers.CharField(source='lead.__str__', read_only=True)
    lead_email = serializers.CharField(source='lead.email', read_only=True)
    emails_sent = serializers.IntegerField(source='emails.count', read_only=True)
    
    class Meta:
        model = CampaignLead
        fields = [
            'id', 'lead', 'lead_name', 'lead_email', 'status',
            'current_sequence_position', 'emails_sent',
            'date_added', 'date_updated', 'date_completed'
        ]
        read_only_fields = ['id', 'date_added', 'date_updated']


class CampaignSerializer(serializers.ModelSerializer):
    """Serializer for Campaign model."""
    sequences = EmailSequenceSerializer(many=True, read_only=True)
    campaign_leads = CampaignLeadSerializer(many=True, read_only=True)
    target_lead_category_name = serializers.CharField(source='target_lead_category.name', read_only=True)
    
    # Statistics
    total_leads = serializers.IntegerField(source='campaign_leads.count', read_only=True)
    total_emails_sent = serializers.IntegerField(source='campaign_emails.filter(status="sent").count', read_only=True)
    
    class Meta:
        model = Campaign
        fields = [
            'id', 'name', 'description', 'target_lead_category',
            'target_lead_category_name', 'status', 'start_date', 'end_date',
            'is_active', 'sequences', 'campaign_leads',
            'total_leads', 'total_emails_sent',
            'date_created', 'date_updated'
        ]
        read_only_fields = ['id', 'date_created', 'date_updated']

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class CampaignEmailSerializer(serializers.ModelSerializer):
    """Serializer for CampaignEmail model."""
    campaign_name = serializers.CharField(source='campaign.name', read_only=True)
    lead_name = serializers.CharField(source='campaign_lead.lead.__str__', read_only=True)
    lead_email = serializers.CharField(source='campaign_lead.lead.email', read_only=True)
    email_account_name = serializers.CharField(source='email_account.account_name', read_only=True)
    sequence_message_name = serializers.CharField(source='sequence_message.name', read_only=True)
    
    class Meta:
        model = CampaignEmail
        fields = [
            'id', 'campaign', 'campaign_name', 'campaign_lead',
            'lead_name', 'lead_email', 'sequence_message',
            'sequence_message_name', 'email_account', 'email_account_name',
            'subject', 'body_text', 'body_html', 'status',
            'scheduled_time', 'sent_time', 'message_id',
            'date_created', 'date_updated'
        ]
        read_only_fields = [
            'id', 'campaign_name', 'lead_name', 'lead_email',
            'email_account_name', 'sequence_message_name',
            'sent_time', 'message_id', 'date_created', 'date_updated'
        ]


class EmailAccountDailyLimitSerializer(serializers.ModelSerializer):
    """Serializer for EmailAccountDailyLimit model."""
    email_account_name = serializers.CharField(source='email_account.account_name', read_only=True)
    email_address = serializers.CharField(source='email_account.email_address', read_only=True)
    remaining_capacity = serializers.IntegerField(read_only=True)
    has_capacity = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = EmailAccountDailyLimit
        fields = [
            'id', 'email_account', 'email_account_name', 'email_address',
            'date', 'emails_sent', 'daily_limit', 'remaining_capacity',
            'has_capacity'
        ]
        read_only_fields = ['id', 'remaining_capacity', 'has_capacity']


class CampaignStatsSerializer(serializers.Serializer):
    """Serializer for campaign statistics."""
    total_campaigns = serializers.IntegerField()
    active_campaigns = serializers.IntegerField()
    total_leads = serializers.IntegerField()
    total_emails_sent = serializers.IntegerField()
    emails_sent_today = serializers.IntegerField()
    available_accounts = serializers.IntegerField()
    accounts_at_limit = serializers.IntegerField()


class LeadCategorySerializer(serializers.ModelSerializer):
    """Serializer for LeadCategory model."""
    lead_count = serializers.IntegerField(source='lead_lead_category.count', read_only=True)
    
    class Meta:
        model = LeadCategory
        fields = ['id', 'name', 'description', 'is_active', 'lead_count']


class EmailAccountSerializer(serializers.ModelSerializer):
    """Serializer for EmailAccount model (read-only for selection)."""
    
    class Meta:
        model = EmailAccount
        fields = [
            'id', 'account_name', 'email_address',
            'is_bulk_mail_account', 'is_warming_up', 'is_active'
        ]
        read_only_fields = ['id']


class CampaignCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating campaigns with lead auto-assignment."""
    auto_add_leads = serializers.BooleanField(default=True, write_only=True)
    
    class Meta:
        model = Campaign
        fields = [
            'name', 'description', 'target_lead_category',
            'auto_add_leads'
        ]

    def create(self, validated_data):
        auto_add_leads = validated_data.pop('auto_add_leads', True)
        validated_data['user'] = self.context['request'].user
        
        campaign = super().create(validated_data)
        
        if auto_add_leads:
            # Auto-add leads from the target category
            from .services import CampaignManager
            added_count = CampaignManager.add_leads_to_campaign(campaign)
            
            # Add the count to the response (if needed)
            campaign._added_leads_count = added_count
        
        return campaign
