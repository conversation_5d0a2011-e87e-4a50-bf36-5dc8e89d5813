import logging
import smtplib
import email.utils
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import time

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction

from apps.emailer.models import CampaignEmail, EmailAccountDailyLimit
from apps.emailer.services import EmailSender, CampaignManager

logger = logging.getLogger(__name__)

BUSINESS_HOURS_START = 7
BUSINESS_HOURS_END = 19
WEEKDAYS = {0, 1, 2, 3, 4}  # Monday to Friday


class Command(BaseCommand):
    help = 'Send scheduled campaign emails and schedule follow-up emails'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force sending emails outside business hours',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting to send scheduled campaign emails...'))

        # Check if we're in business hours (unless forced)
        if not options['force']:
            now = timezone.localtime()
            if now.weekday() not in WEEKDAYS or not (time(BUSINESS_HOURS_START) <= now.time() <= time(BUSINESS_HOURS_END)):
                self.stdout.write(self.style.WARNING(
                    'Current time is outside of allowed sending window (M-F 7AM–7PM). '
                    'Use --force to override.'
                ))
                return

        # Send scheduled emails
        sent_count = self.send_scheduled_emails()
        self.stdout.write(self.style.SUCCESS(f'Sent {sent_count} emails'))

        # Schedule follow-up emails
        scheduled_count = CampaignManager.schedule_follow_up_emails()
        self.stdout.write(self.style.SUCCESS(f'Scheduled {scheduled_count} follow-up emails'))

        self.stdout.write(self.style.SUCCESS('Finished processing campaign emails'))

    def send_scheduled_emails(self):
        """Send all scheduled emails that are due."""
        now = timezone.now()
        
        # Get all scheduled emails that are due
        pending_emails = CampaignEmail.objects.filter(
            status='scheduled',
            scheduled_time__lte=now
        ).select_related('campaign_lead__lead', 'email_account')

        sent_count = 0

        for email_obj in pending_emails:
            try:
                with transaction.atomic():
                    # Check if the account still has capacity
                    today = now.date()
                    daily_limit, created = EmailAccountDailyLimit.objects.get_or_create(
                        email_account=email_obj.email_account,
                        date=today,
                        defaults={'emails_sent': 0}
                    )

                    if not daily_limit.has_capacity:
                        self.stdout.write(
                            self.style.WARNING(
                                f"Email account {email_obj.email_account} has reached daily limit"
                            )
                        )
                        continue

                    # Get SMTP connection info
                    smtp_info = {
                        'server': email_obj.email_account.smtp_server,
                        'port': email_obj.email_account.smtp_port,
                        'use_ssl': email_obj.email_account.smtp_use_ssl,
                        'username': email_obj.email_account.email_address,
                        'password': email_obj.email_account.smtp_password,
                    }

                    # Build and send the email
                    msg = self.build_message(
                        email_obj,
                        email_obj.email_account.email_address,
                        email_obj.campaign_lead.lead.email
                    )
                    success = self.send_email(msg, smtp_info)

                    if success:
                        # Update the email status
                        email_obj.status = 'sent'
                        email_obj.sent_time = now
                        email_obj.message_id = msg['Message-ID']
                        email_obj.save()

                        # Increment the daily limit counter
                        daily_limit.emails_sent += 1
                        daily_limit.save()

                        # Update the campaign lead's sequence position
                        campaign_lead = email_obj.campaign_lead
                        campaign_lead.current_sequence_position += 1
                        campaign_lead.save()

                        sent_count += 1
                        self.stdout.write(f"Sent: {email_obj.subject} to {email_obj.campaign_lead.lead.email}")
                    else:
                        email_obj.status = 'failed'
                        email_obj.save()
                        self.stdout.write(
                            self.style.ERROR(f"Failed to send: {email_obj.subject}")
                        )

            except Exception as e:
                logger.error(f"Error sending email {email_obj.id}: {str(e)}")
                self.stdout.write(
                    self.style.ERROR(f"Error sending email {email_obj.id}: {str(e)}")
                )

        return sent_count

    def build_message(self, email_obj, from_email, to_email):
        """Build the email message."""
        # Create multipart message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = email_obj.subject
        msg['From'] = from_email
        msg['To'] = to_email
        msg['Date'] = email.utils.formatdate(localtime=True)
        msg['Message-ID'] = email.utils.make_msgid()

        # Add text part
        text_part = MIMEText(email_obj.body_text, 'plain', 'utf-8')
        msg.attach(text_part)

        # Add HTML part if available
        if email_obj.body_html:
            html_part = MIMEText(email_obj.body_html, 'html', 'utf-8')
            msg.attach(html_part)

        return msg

    def send_email(self, msg, smtp_info):
        """Send the email using SMTP."""
        try:
            # Create SMTP connection
            if smtp_info['use_ssl']:
                server = smtplib.SMTP_SSL(smtp_info['server'], smtp_info['port'])
            else:
                server = smtplib.SMTP(smtp_info['server'], smtp_info['port'])
                server.starttls()

            # Login and send
            server.login(smtp_info['username'], smtp_info['password'])
            server.send_message(msg)
            server.quit()

            return True

        except Exception as e:
            logger.error(f"SMTP error: {str(e)}")
            return False
