from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator

from apps.authentication.models import User
from apps.email.models import EmailAccount
from apps.leads.models import Lead


class EmailTemplate(models.Model):
    """
    Model to store reusable email templates.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='email_templates')
    name = models.CharField(_('Template Name'), max_length=255)
    subject = models.CharField(_('Subject'), max_length=255)
    body_text = models.TextField(_('Body Text'))
    body_html = models.TextField(_('Body HTML'))
    
    is_active = models.BooleanField(_('Is Active'), default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "emailer_template"
        verbose_name = _("Email Template")
        verbose_name_plural = _("Email Templates")

    def __str__(self):
        return self.name


class Campaign(models.Model):
    """
    Model to store email campaign details.
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('active', _('Active')),
        ('paused', _('Paused')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='emailer_campaigns')
    name = models.CharField(_('Campaign Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True, null=True)
    
    # Target lead category
    target_lead_category = models.ForeignKey(
        'leads.LeadCategory', 
        on_delete=models.CASCADE, 
        related_name='emailer_campaigns',
        help_text=_('Leads from this category will be targeted by this campaign.')
    )
    
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='draft')
    
    start_date = models.DateTimeField(_('Start Date'), blank=True, null=True)
    end_date = models.DateTimeField(_('End Date'), blank=True, null=True)
    
    is_active = models.BooleanField(_('Is Active'), default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "emailer_campaign"
        verbose_name = _("Campaign")
        verbose_name_plural = _("Campaigns")

    def __str__(self):
        return self.name


class EmailSequence(models.Model):
    """
    Model to store email sequences within campaigns.
    """
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='sequences')
    name = models.CharField(_('Sequence Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True, null=True)
    
    is_active = models.BooleanField(_('Is Active'), default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "emailer_sequence"
        verbose_name = _("Email Sequence")
        verbose_name_plural = _("Email Sequences")

    def __str__(self):
        return f"{self.campaign.name} - {self.name}"


class SequenceMessage(models.Model):
    """
    Model to store individual messages within email sequences.
    """
    sequence = models.ForeignKey(EmailSequence, on_delete=models.CASCADE, related_name='messages')
    template = models.ForeignKey(
        EmailTemplate, 
        on_delete=models.SET_NULL, 
        related_name='sequence_messages',
        blank=True, 
        null=True,
        help_text=_('Template to use as starting point for this message.')
    )
    
    name = models.CharField(_('Message Name'), max_length=255)
    subject = models.CharField(_('Subject'), max_length=255)
    body_text = models.TextField(_('Body Text'))
    body_html = models.TextField(_('Body HTML'))
    
    delay_days = models.PositiveIntegerField(
        _('Delay (days)'), 
        default=0,
        help_text=_('Number of days to wait before sending this message after the previous one.')
    )
    
    order = models.PositiveIntegerField(_('Order'), default=1)
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "emailer_sequence_message"
        verbose_name = _("Sequence Message")
        verbose_name_plural = _("Sequence Messages")
        ordering = ['order']

    def __str__(self):
        return f"{self.sequence.name} - {self.name}"

    def save(self, *args, **kwargs):
        # If using a template, copy its content
        if self.template:
            self.subject = self.template.subject
            self.body_text = self.template.body_text
            self.body_html = self.template.body_html
        super().save(*args, **kwargs)


class CampaignLead(models.Model):
    """
    Model to track leads in campaigns and prevent duplicate sends.
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
        ('opted_out', _('Opted Out')),
    )

    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='campaign_leads')
    lead = models.ForeignKey(Lead, on_delete=models.CASCADE, related_name='emailer_campaigns')
    
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')
    current_sequence_position = models.PositiveIntegerField(_('Current Sequence Position'), default=0)
    
    date_added = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    date_completed = models.DateTimeField(_('Date Completed'), blank=True, null=True)

    class Meta:
        db_table = "emailer_campaign_lead"
        verbose_name = _("Campaign Lead")
        verbose_name_plural = _("Campaign Leads")
        unique_together = ('campaign', 'lead')

    def __str__(self):
        return f"{self.campaign.name} - {self.lead}"


class CampaignEmail(models.Model):
    """
    Model to track emails sent in campaigns.
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('scheduled', _('Scheduled')),
        ('sent', _('Sent')),
        ('delivered', _('Delivered')),
        ('failed', _('Failed')),
        ('bounced', _('Bounced')),
    )

    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='campaign_emails')
    campaign_lead = models.ForeignKey(CampaignLead, on_delete=models.CASCADE, related_name='emails')
    sequence_message = models.ForeignKey(SequenceMessage, on_delete=models.CASCADE, related_name='sent_emails')
    email_account = models.ForeignKey(EmailAccount, on_delete=models.CASCADE, related_name='emailer_sent')
    
    subject = models.CharField(_('Subject'), max_length=255)
    body_text = models.TextField(_('Body Text'))
    body_html = models.TextField(_('Body HTML'))
    
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')
    
    scheduled_time = models.DateTimeField(_('Scheduled Time'))
    sent_time = models.DateTimeField(_('Sent Time'), blank=True, null=True)
    
    message_id = models.CharField(_('Message ID'), max_length=255, blank=True, null=True)
    
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "emailer_campaign_email"
        verbose_name = _("Campaign Email")
        verbose_name_plural = _("Campaign Emails")

    def __str__(self):
        return f"{self.subject} - {self.campaign_lead.lead} ({self.get_status_display()})"


class EmailAccountDailyLimit(models.Model):
    """
    Model to track daily email sending limits for email accounts.
    """
    email_account = models.ForeignKey(EmailAccount, on_delete=models.CASCADE, related_name='emailer_daily_limits')
    date = models.DateField(_('Date'))
    emails_sent = models.PositiveIntegerField(_('Emails Sent'), default=0)
    
    # Daily limit per account (40 emails per day)
    daily_limit = models.PositiveIntegerField(_('Daily Limit'), default=40)

    class Meta:
        db_table = "emailer_email_account_daily_limit"
        verbose_name = _("Email Account Daily Limit")
        verbose_name_plural = _("Email Account Daily Limits")
        unique_together = ('email_account', 'date')

    def __str__(self):
        return f"{self.email_account} - {self.date} ({self.emails_sent}/{self.daily_limit})"

    @property
    def has_capacity(self):
        """Check if the account still has capacity to send emails today."""
        return self.emails_sent < self.daily_limit

    @property
    def remaining_capacity(self):
        """Get the remaining email capacity for today."""
        return max(0, self.daily_limit - self.emails_sent)
