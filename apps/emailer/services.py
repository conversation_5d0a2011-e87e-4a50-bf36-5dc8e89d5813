import logging
import random
from datetime import timedelta
from django.utils import timezone
from django.db import transaction
from django.db.models import Q

from apps.email.models import EmailAccount
from .models import (
    Campaign,
    CampaignLead,
    CampaignEmail,
    EmailAccountDailyLimit,
    SequenceMessage
)

logger = logging.getLogger(__name__)


class EmailAccountSelector:
    """
    Service for selecting available email accounts for sending.
    """
    
    @staticmethod
    def get_available_accounts():
        """
        Get email accounts that are available for bulk email sending.
        
        Returns:
            QuerySet of EmailAccount objects that meet the criteria:
            - is_bulk_mail_account=True
            - is_warming_up=False
            - is_active=True
        """
        return EmailAccount.objects.filter(
            is_bulk_mail_account=True,
            is_warming_up=False,
            is_active=True
        )
    
    @staticmethod
    def get_accounts_with_capacity(date=None):
        """
        Get email accounts that still have capacity to send emails today.
        
        Args:
            date: Date to check capacity for (defaults to today)
            
        Returns:
            List of tuples: (EmailAccount, remaining_capacity)
        """
        if date is None:
            date = timezone.now().date()
        
        available_accounts = EmailAccountSelector.get_available_accounts()
        accounts_with_capacity = []
        
        for account in available_accounts:
            daily_limit, created = EmailAccountDailyLimit.objects.get_or_create(
                email_account=account,
                date=date,
                defaults={'emails_sent': 0}
            )
            
            if daily_limit.has_capacity:
                accounts_with_capacity.append((account, daily_limit.remaining_capacity))
        
        return accounts_with_capacity
    
    @staticmethod
    def select_random_account_with_capacity(date=None):
        """
        Randomly select an email account that has capacity to send emails.
        
        Args:
            date: Date to check capacity for (defaults to today)
            
        Returns:
            EmailAccount object or None if no accounts have capacity
        """
        accounts_with_capacity = EmailAccountSelector.get_accounts_with_capacity(date)
        
        if not accounts_with_capacity:
            return None
        
        # Randomly select from available accounts
        account, capacity = random.choice(accounts_with_capacity)
        return account


class EmailSender:
    """
    Service for sending emails and managing email scheduling.
    """
    
    @staticmethod
    def schedule_email(campaign_lead, sequence_message, scheduled_time=None):
        """
        Schedule an email to be sent to a campaign lead.
        
        Args:
            campaign_lead: CampaignLead object
            sequence_message: SequenceMessage object
            scheduled_time: When to send the email (defaults to now)
            
        Returns:
            CampaignEmail object or None if no account available
        """
        if scheduled_time is None:
            scheduled_time = timezone.now()
        
        # Select an email account with capacity
        email_account = EmailAccountSelector.select_random_account_with_capacity(
            scheduled_time.date()
        )
        
        if not email_account:
            logger.warning(f"No email accounts with capacity available for {scheduled_time.date()}")
            return None
        
        # Create the campaign email
        campaign_email = CampaignEmail.objects.create(
            campaign=campaign_lead.campaign,
            campaign_lead=campaign_lead,
            sequence_message=sequence_message,
            email_account=email_account,
            subject=sequence_message.subject,
            body_text=sequence_message.body_text,
            body_html=sequence_message.body_html,
            status='scheduled',
            scheduled_time=scheduled_time
        )
        
        logger.info(f"Scheduled email {campaign_email.id} for {campaign_lead.lead.email}")
        return campaign_email
    
    @staticmethod
    def send_scheduled_emails():
        """
        Send all scheduled emails that are due.
        
        Returns:
            Number of emails sent
        """
        now = timezone.now()
        
        # Get all scheduled emails that are due
        due_emails = CampaignEmail.objects.filter(
            status='scheduled',
            scheduled_time__lte=now
        ).select_related('campaign_lead__lead', 'email_account')
        
        sent_count = 0
        
        for email in due_emails:
            # Check if the account still has capacity
            today = now.date()
            daily_limit, created = EmailAccountDailyLimit.objects.get_or_create(
                email_account=email.email_account,
                date=today,
                defaults={'emails_sent': 0}
            )
            
            if not daily_limit.has_capacity:
                logger.warning(f"Email account {email.email_account} has reached daily limit")
                # Reschedule for tomorrow
                email.scheduled_time = timezone.now().replace(
                    hour=9, minute=0, second=0, microsecond=0
                ) + timedelta(days=1)
                email.save()
                continue
            
            # Send the email (placeholder for actual email sending logic)
            try:
                # TODO: Implement actual email sending using SMTP
                # This would use Django's send_mail or a similar function
                # For now, we'll just mark it as sent
                logger.info(f"Sending email {email.id} to {email.campaign_lead.lead.email}")
                
                # Update the email status
                email.status = 'sent'
                email.sent_time = now
                email.save()
                
                # Increment the daily limit counter
                daily_limit.emails_sent += 1
                daily_limit.save()
                
                # Update the campaign lead's sequence position
                campaign_lead = email.campaign_lead
                campaign_lead.current_sequence_position += 1
                campaign_lead.save()
                
                sent_count += 1
                
            except Exception as e:
                logger.error(f"Failed to send email {email.id}: {str(e)}")
                email.status = 'failed'
                email.save()
        
        return sent_count


class CampaignManager:
    """
    Service for managing campaigns and scheduling emails.
    """
    
    @staticmethod
    def start_campaign(campaign):
        """
        Start a campaign by scheduling the first email for each lead.
        
        Args:
            campaign: Campaign object
            
        Returns:
            Number of emails scheduled
        """
        if campaign.status != 'draft':
            logger.warning(f"Campaign {campaign.id} is not in draft status")
            return 0
        
        # Update campaign status
        campaign.status = 'active'
        campaign.start_date = timezone.now()
        campaign.save()
        
        # Get all leads in the campaign
        campaign_leads = CampaignLead.objects.filter(campaign=campaign, status='pending')
        
        if not campaign_leads.exists():
            logger.warning(f"No leads found for campaign {campaign.id}")
            return 0
        
        # Get the first email sequence for the campaign
        sequences = campaign.sequences.filter(is_active=True)
        
        if not sequences.exists():
            logger.warning(f"No active sequences found for campaign {campaign.id}")
            return 0
        
        scheduled_count = 0
        
        for sequence in sequences:
            first_message = sequence.messages.filter(is_active=True).order_by('order').first()
            
            if not first_message:
                logger.warning(f"No active messages found for sequence {sequence.id}")
                continue
            
            # Schedule the first email for each lead
            for campaign_lead in campaign_leads:
                email = EmailSender.schedule_email(campaign_lead, first_message)
                
                if email:
                    # Update lead status
                    campaign_lead.status = 'in_progress'
                    campaign_lead.save()
                    
                    scheduled_count += 1
        
        return scheduled_count
    
    @staticmethod
    def schedule_follow_up_emails():
        """
        Schedule follow-up emails for leads that have completed previous emails.
        
        Returns:
            Number of follow-up emails scheduled
        """
        # Get campaign leads that are in progress
        campaign_leads = CampaignLead.objects.filter(
            status='in_progress'
        ).select_related('campaign')
        
        scheduled_count = 0
        
        for campaign_lead in campaign_leads:
            # Get the sequences for this campaign
            sequences = campaign_lead.campaign.sequences.filter(is_active=True)
            
            for sequence in sequences:
                # Get the next message in the sequence
                next_message = sequence.messages.filter(
                    is_active=True,
                    order__gt=campaign_lead.current_sequence_position
                ).order_by('order').first()
                
                if not next_message:
                    # No more messages in this sequence
                    continue
                
                # Check if we should schedule the next email based on delay
                last_email = CampaignEmail.objects.filter(
                    campaign_lead=campaign_lead,
                    status='sent'
                ).order_by('-sent_time').first()
                
                if last_email:
                    # Calculate when to send the next email
                    next_send_time = last_email.sent_time + timedelta(days=next_message.delay_days)
                    
                    # Only schedule if it's time to send
                    if next_send_time <= timezone.now():
                        email = EmailSender.schedule_email(campaign_lead, next_message, next_send_time)
                        
                        if email:
                            scheduled_count += 1
        
        return scheduled_count
    
    @staticmethod
    def add_leads_to_campaign(campaign, lead_category=None):
        """
        Add leads to a campaign based on the target lead category.
        
        Args:
            campaign: Campaign object
            lead_category: LeadCategory to filter leads (defaults to campaign's target category)
            
        Returns:
            Number of leads added
        """
        if lead_category is None:
            lead_category = campaign.target_lead_category
        
        # Get leads from the specified category that aren't already in this campaign
        from apps.leads.models import Lead
        
        leads = Lead.objects.filter(
            lead_category=lead_category,
            is_active=True,
            email__isnull=False
        ).exclude(
            emailer_campaigns__campaign=campaign
        )
        
        added_count = 0
        
        for lead in leads:
            campaign_lead, created = CampaignLead.objects.get_or_create(
                campaign=campaign,
                lead=lead,
                defaults={'status': 'pending'}
            )
            
            if created:
                added_count += 1
        
        return added_count
