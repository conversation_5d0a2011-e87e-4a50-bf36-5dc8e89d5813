from django import forms
from django.forms import inlineformset_factory
from .models import (
    EmailTemplate,
    Campaign,
    EmailSequence,
    SequenceMessage,
    CampaignLead
)
from apps.leads.models import LeadCategory


class EmailTemplateForm(forms.ModelForm):
    """Form for creating and editing email templates."""
    
    class Meta:
        model = EmailTemplate
        fields = ['name', 'subject', 'body_text', 'body_html', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter template name'
            }),
            'subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter email subject'
            }),
            'body_text': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 8,
                'placeholder': 'Enter plain text version of the email'
            }),
            'body_html': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 12,
                'placeholder': 'Enter HTML version of the email'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }


class CampaignForm(forms.ModelForm):
    """Form for creating and editing campaigns."""
    
    auto_add_leads = forms.BooleanField(
        required=False,
        initial=True,
        help_text='Automatically add all leads from the target category to this campaign',
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    class Meta:
        model = Campaign
        fields = ['name', 'description', 'target_lead_category']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter campaign name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter campaign description (optional)'
            }),
            'target_lead_category': forms.Select(attrs={
                'class': 'form-control'
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['target_lead_category'].queryset = LeadCategory.objects.filter(is_active=True)
        self.fields['target_lead_category'].empty_label = "Select a lead category"


class EmailSequenceForm(forms.ModelForm):
    """Form for creating and editing email sequences."""
    
    class Meta:
        model = EmailSequence
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter sequence name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter sequence description (optional)'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }


class SequenceMessageForm(forms.ModelForm):
    """Form for creating and editing sequence messages."""
    
    class Meta:
        model = SequenceMessage
        fields = [
            'name', 'template', 'subject', 'body_text', 'body_html',
            'delay_days', 'order', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter message name'
            }),
            'template': forms.Select(attrs={
                'class': 'form-control'
            }),
            'subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter email subject'
            }),
            'body_text': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': 'Enter plain text version'
            }),
            'body_html': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 8,
                'placeholder': 'Enter HTML version'
            }),
            'delay_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'placeholder': 'Days to wait before sending'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'placeholder': 'Message order in sequence'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            self.fields['template'].queryset = EmailTemplate.objects.filter(
                user=user, is_active=True
            )
        self.fields['template'].empty_label = "Select a template (optional)"


class CampaignLeadForm(forms.ModelForm):
    """Form for managing campaign leads."""
    
    class Meta:
        model = CampaignLead
        fields = ['lead', 'status']
        widgets = {
            'lead': forms.Select(attrs={
                'class': 'form-control'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            })
        }


# Inline formsets for managing related objects
SequenceMessageFormSet = inlineformset_factory(
    EmailSequence,
    SequenceMessage,
    form=SequenceMessageForm,
    extra=1,
    can_delete=True,
    fields=['name', 'template', 'subject', 'body_text', 'body_html', 'delay_days', 'order', 'is_active']
)


class BulkLeadAddForm(forms.Form):
    """Form for bulk adding leads to a campaign."""
    
    lead_category = forms.ModelChoiceField(
        queryset=LeadCategory.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        help_text='Select the lead category to add leads from'
    )
    
    exclude_existing = forms.BooleanField(
        required=False,
        initial=True,
        help_text='Exclude leads that are already in this campaign',
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    exclude_opted_out = forms.BooleanField(
        required=False,
        initial=True,
        help_text='Exclude leads that have opted out',
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )


class CampaignStatusForm(forms.Form):
    """Form for changing campaign status."""
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
    ]
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def __init__(self, current_status=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Filter available status choices based on current status
        if current_status == 'draft':
            self.fields['status'].choices = [('active', 'Start Campaign')]
        elif current_status == 'active':
            self.fields['status'].choices = [
                ('paused', 'Pause Campaign'),
                ('completed', 'Complete Campaign')
            ]
        elif current_status == 'paused':
            self.fields['status'].choices = [
                ('active', 'Resume Campaign'),
                ('completed', 'Complete Campaign')
            ]
