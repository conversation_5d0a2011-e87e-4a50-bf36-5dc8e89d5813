from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Count, Q
from django.urls import reverse
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from .models import (
    EmailTemplate,
    Campaign,
    EmailSequence,
    SequenceMessage,
    CampaignLead,
    CampaignEmail,
    EmailAccountDailyLimit
)
from .serializers import (
    EmailTemplateSerializer,
    CampaignSerializer,
    CampaignCreateSerializer,
    EmailSequenceSerializer,
    SequenceMessageSerializer,
    CampaignLeadSerializer,
    CampaignEmailSerializer,
    EmailAccountDailyLimitSerializer,
    CampaignStatsSerializer,
    LeadCategorySerializer,
    EmailAccountSerializer
)
from .forms import (
    EmailTemplateForm,
    CampaignForm,
    EmailSequenceForm,
    SequenceMessageForm,
    SequenceMessageFormSet,
    BulkLeadAddForm,
    CampaignStatusForm
)
from .services import CampaignManager, EmailAccountSelector
from apps.leads.models import LeadCategory
from apps.email.models import EmailAccount


@login_required
def dashboard(request):
    """Dashboard view for email campaigns."""
    # Get campaign statistics
    user_campaigns = Campaign.objects.filter(user=request.user)
    
    stats = {
        'total_campaigns': user_campaigns.count(),
        'active_campaigns': user_campaigns.filter(status='active').count(),
        'total_leads': CampaignLead.objects.filter(campaign__user=request.user).count(),
        'total_emails_sent': CampaignEmail.objects.filter(
            campaign__user=request.user, status='sent'
        ).count(),
        'emails_sent_today': CampaignEmail.objects.filter(
            campaign__user=request.user,
            status='sent',
            sent_time__date=timezone.now().date()
        ).count(),
    }
    
    # Get recent campaigns
    recent_campaigns = user_campaigns.order_by('-date_created')[:5]
    
    # Get available email accounts
    available_accounts = EmailAccountSelector.get_accounts_with_capacity()
    
    context = {
        'stats': stats,
        'recent_campaigns': recent_campaigns,
        'available_accounts': len(available_accounts),
        'accounts_at_limit': EmailAccount.objects.filter(
            is_bulk_mail_account=True,
            is_warming_up=False,
            is_active=True
        ).count() - len(available_accounts),
    }
    
    return render(request, 'emailer/dashboard.html', context)


@login_required
def campaign_detail(request, campaign_id):
    """Detail view for a specific campaign."""
    campaign = get_object_or_404(Campaign, id=campaign_id, user=request.user)
    
    # Get campaign statistics
    campaign_stats = {
        'total_leads': campaign.campaign_leads.count(),
        'pending_leads': campaign.campaign_leads.filter(status='pending').count(),
        'in_progress_leads': campaign.campaign_leads.filter(status='in_progress').count(),
        'completed_leads': campaign.campaign_leads.filter(status='completed').count(),
        'total_emails': campaign.campaign_emails.count(),
        'sent_emails': campaign.campaign_emails.filter(status='sent').count(),
        'scheduled_emails': campaign.campaign_emails.filter(status='scheduled').count(),
        'failed_emails': campaign.campaign_emails.filter(status='failed').count(),
    }
    
    context = {
        'campaign': campaign,
        'stats': campaign_stats,
    }
    
    return render(request, 'emailer/campaign_detail.html', context)


@login_required
def create_campaign(request):
    """Create a new campaign."""
    if request.method == 'POST':
        form = CampaignForm(request.POST)
        if form.is_valid():
            campaign = form.save(commit=False)
            campaign.user = request.user
            campaign.save()

            # Auto-add leads if requested
            if form.cleaned_data.get('auto_add_leads'):
                added_count = CampaignManager.add_leads_to_campaign(campaign)
                messages.success(
                    request,
                    f'Campaign "{campaign.name}" created successfully! {added_count} leads added.'
                )
            else:
                messages.success(request, f'Campaign "{campaign.name}" created successfully!')

            return redirect('emailer:campaign_detail', campaign_id=campaign.id)
    else:
        form = CampaignForm()

    return render(request, 'emailer/create_campaign.html', {'form': form})


@login_required
def edit_campaign(request, campaign_id):
    """Edit an existing campaign."""
    campaign = get_object_or_404(Campaign, id=campaign_id, user=request.user)

    if request.method == 'POST':
        form = CampaignForm(request.POST, instance=campaign)
        if form.is_valid():
            form.save()
            messages.success(request, f'Campaign "{campaign.name}" updated successfully!')
            return redirect('emailer:campaign_detail', campaign_id=campaign.id)
    else:
        form = CampaignForm(instance=campaign)

    return render(request, 'emailer/edit_campaign.html', {'form': form, 'campaign': campaign})


@login_required
def templates_list(request):
    """List all email templates."""
    templates = EmailTemplate.objects.filter(user=request.user).order_by('-date_created')
    return render(request, 'emailer/templates_list.html', {'templates': templates})


@login_required
def create_template(request):
    """Create a new email template."""
    if request.method == 'POST':
        form = EmailTemplateForm(request.POST)
        if form.is_valid():
            template = form.save(commit=False)
            template.user = request.user
            template.save()
            messages.success(request, f'Template "{template.name}" created successfully!')
            return redirect('emailer:templates_list')
    else:
        form = EmailTemplateForm()

    return render(request, 'emailer/create_template.html', {'form': form})


@login_required
def edit_template(request, template_id):
    """Edit an existing email template."""
    template = get_object_or_404(EmailTemplate, id=template_id, user=request.user)

    if request.method == 'POST':
        form = EmailTemplateForm(request.POST, instance=template)
        if form.is_valid():
            form.save()
            messages.success(request, f'Template "{template.name}" updated successfully!')
            return redirect('emailer:templates_list')
    else:
        form = EmailTemplateForm(instance=template)

    return render(request, 'emailer/edit_template.html', {'form': form, 'template': template})


@login_required
def delete_template(request, template_id):
    """Delete an email template."""
    template = get_object_or_404(EmailTemplate, id=template_id, user=request.user)

    if request.method == 'POST':
        template_name = template.name
        template.delete()
        messages.success(request, f'Template "{template_name}" deleted successfully!')
        return redirect('emailer:templates_list')

    return render(request, 'emailer/delete_template.html', {'template': template})


@login_required
def create_sequence(request, campaign_id):
    """Create a new email sequence for a campaign."""
    campaign = get_object_or_404(Campaign, id=campaign_id, user=request.user)

    if request.method == 'POST':
        form = EmailSequenceForm(request.POST)
        if form.is_valid():
            sequence = form.save(commit=False)
            sequence.campaign = campaign
            sequence.save()
            messages.success(request, f'Sequence "{sequence.name}" created successfully!')
            return redirect('emailer:sequence_detail', sequence_id=sequence.id)
    else:
        form = EmailSequenceForm()

    return render(request, 'emailer/create_sequence.html', {
        'form': form,
        'campaign': campaign
    })


@login_required
def sequence_detail(request, sequence_id):
    """Detail view for an email sequence."""
    sequence = get_object_or_404(
        EmailSequence,
        id=sequence_id,
        campaign__user=request.user
    )

    messages = sequence.messages.filter(is_active=True).order_by('order')

    return render(request, 'emailer/sequence_detail.html', {
        'sequence': sequence,
        'messages': messages
    })


@login_required
def create_message(request, sequence_id):
    """Create a new message in a sequence."""
    sequence = get_object_or_404(
        EmailSequence,
        id=sequence_id,
        campaign__user=request.user
    )

    if request.method == 'POST':
        form = SequenceMessageForm(request.POST, user=request.user)
        if form.is_valid():
            message = form.save(commit=False)
            message.sequence = sequence
            message.save()
            messages.success(request, f'Message "{message.name}" created successfully!')
            return redirect('emailer:sequence_detail', sequence_id=sequence.id)
    else:
        # Set default order to next available
        next_order = sequence.messages.count() + 1
        form = SequenceMessageForm(user=request.user, initial={'order': next_order})

    return render(request, 'emailer/create_message.html', {
        'form': form,
        'sequence': sequence
    })


@login_required
def edit_message(request, message_id):
    """Edit a sequence message."""
    message = get_object_or_404(
        SequenceMessage,
        id=message_id,
        sequence__campaign__user=request.user
    )

    if request.method == 'POST':
        form = SequenceMessageForm(request.POST, instance=message, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, f'Message "{message.name}" updated successfully!')
            return redirect('emailer:sequence_detail', sequence_id=message.sequence.id)
    else:
        form = SequenceMessageForm(instance=message, user=request.user)

    return render(request, 'emailer/edit_message.html', {
        'form': form,
        'message': message
    })


@login_required
def campaign_change_status(request, campaign_id):
    """Change campaign status."""
    campaign = get_object_or_404(Campaign, id=campaign_id, user=request.user)

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'start' and campaign.status == 'draft':
            scheduled_count = CampaignManager.start_campaign(campaign)
            messages.success(
                request,
                f'Campaign started successfully! {scheduled_count} emails scheduled.'
            )
        elif action == 'pause' and campaign.status == 'active':
            campaign.status = 'paused'
            campaign.save()
            messages.success(request, 'Campaign paused successfully!')
        elif action == 'resume' and campaign.status == 'paused':
            campaign.status = 'active'
            campaign.save()
            messages.success(request, 'Campaign resumed successfully!')
        elif action == 'complete':
            campaign.status = 'completed'
            campaign.end_date = timezone.now()
            campaign.save()
            messages.success(request, 'Campaign completed successfully!')
        else:
            messages.error(request, 'Invalid action for current campaign status.')

    return redirect('emailer:campaign_detail', campaign_id=campaign.id)


@login_required
def add_leads_to_campaign(request, campaign_id):
    """Add leads to a campaign."""
    campaign = get_object_or_404(Campaign, id=campaign_id, user=request.user)

    if request.method == 'POST':
        form = BulkLeadAddForm(request.POST)
        if form.is_valid():
            lead_category = form.cleaned_data.get('lead_category')
            added_count = CampaignManager.add_leads_to_campaign(campaign, lead_category)
            messages.success(
                request,
                f'{added_count} leads added to campaign from {lead_category.name}.'
            )
            return redirect('emailer:campaign_detail', campaign_id=campaign.id)
    else:
        form = BulkLeadAddForm(initial={'lead_category': campaign.target_lead_category})

    return render(request, 'emailer/add_leads.html', {
        'form': form,
        'campaign': campaign
    })


# API ViewSets

class EmailTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for managing email templates."""
    serializer_class = EmailTemplateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return EmailTemplate.objects.filter(user=self.request.user)


class CampaignViewSet(viewsets.ModelViewSet):
    """ViewSet for managing campaigns."""
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return Campaign.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        if self.action == 'create':
            return CampaignCreateSerializer
        return CampaignSerializer
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """Start a campaign."""
        campaign = self.get_object()
        
        if campaign.status != 'draft':
            return Response(
                {'error': 'Campaign must be in draft status to start'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        scheduled_count = CampaignManager.start_campaign(campaign)
        
        return Response({
            'message': f'Campaign started successfully. {scheduled_count} emails scheduled.',
            'scheduled_emails': scheduled_count
        })
    
    @action(detail=True, methods=['post'])
    def pause(self, request, pk=None):
        """Pause a campaign."""
        campaign = self.get_object()
        campaign.status = 'paused'
        campaign.save()
        
        return Response({'message': 'Campaign paused successfully'})
    
    @action(detail=True, methods=['post'])
    def resume(self, request, pk=None):
        """Resume a paused campaign."""
        campaign = self.get_object()
        
        if campaign.status != 'paused':
            return Response(
                {'error': 'Campaign must be paused to resume'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        campaign.status = 'active'
        campaign.save()
        
        return Response({'message': 'Campaign resumed successfully'})
    
    @action(detail=True, methods=['post'])
    def add_leads(self, request, pk=None):
        """Add leads to a campaign."""
        campaign = self.get_object()
        
        added_count = CampaignManager.add_leads_to_campaign(campaign)
        
        return Response({
            'message': f'{added_count} leads added to campaign',
            'added_count': added_count
        })
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get campaign statistics."""
        user_campaigns = self.get_queryset()
        
        stats = {
            'total_campaigns': user_campaigns.count(),
            'active_campaigns': user_campaigns.filter(status='active').count(),
            'total_leads': CampaignLead.objects.filter(campaign__user=request.user).count(),
            'total_emails_sent': CampaignEmail.objects.filter(
                campaign__user=request.user, status='sent'
            ).count(),
            'emails_sent_today': CampaignEmail.objects.filter(
                campaign__user=request.user,
                status='sent',
                sent_time__date=timezone.now().date()
            ).count(),
            'available_accounts': len(EmailAccountSelector.get_accounts_with_capacity()),
            'accounts_at_limit': EmailAccount.objects.filter(
                is_bulk_mail_account=True,
                is_warming_up=False,
                is_active=True
            ).count() - len(EmailAccountSelector.get_accounts_with_capacity()),
        }
        
        serializer = CampaignStatsSerializer(stats)
        return Response(serializer.data)


class EmailSequenceViewSet(viewsets.ModelViewSet):
    """ViewSet for managing email sequences."""
    serializer_class = EmailSequenceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return EmailSequence.objects.filter(campaign__user=self.request.user)


class SequenceMessageViewSet(viewsets.ModelViewSet):
    """ViewSet for managing sequence messages."""
    serializer_class = SequenceMessageSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return SequenceMessage.objects.filter(sequence__campaign__user=self.request.user)


class CampaignLeadViewSet(viewsets.ModelViewSet):
    """ViewSet for managing campaign leads."""
    serializer_class = CampaignLeadSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return CampaignLead.objects.filter(campaign__user=self.request.user)


class CampaignEmailViewSet(viewsets.ModelViewSet):
    """ViewSet for managing campaign emails."""
    serializer_class = CampaignEmailSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return CampaignEmail.objects.filter(campaign__user=self.request.user)


class EmailAccountDailyLimitViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing email account daily limits."""
    serializer_class = EmailAccountDailyLimitSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return EmailAccountDailyLimit.objects.filter(
            email_account__user=self.request.user
        ).select_related('email_account')


# Utility API views

@login_required
def api_lead_categories(request):
    """API endpoint to get available lead categories."""
    categories = LeadCategory.objects.filter(is_active=True).annotate(
        lead_count=Count('lead_lead_category')
    )
    
    serializer = LeadCategorySerializer(categories, many=True)
    return JsonResponse({'categories': serializer.data})


@login_required
def api_email_accounts(request):
    """API endpoint to get available email accounts."""
    accounts = EmailAccount.objects.filter(
        user=request.user,
        is_bulk_mail_account=True,
        is_warming_up=False,
        is_active=True
    )
    
    serializer = EmailAccountSerializer(accounts, many=True)
    return JsonResponse({'accounts': serializer.data})
