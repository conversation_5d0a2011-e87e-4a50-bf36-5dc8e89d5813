from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Count, Q
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from .models import (
    EmailTemplate,
    Campaign,
    EmailSequence,
    SequenceMessage,
    CampaignLead,
    CampaignEmail,
    EmailAccountDailyLimit
)
from .serializers import (
    EmailTemplateSerializer,
    CampaignSerializer,
    CampaignCreateSerializer,
    EmailSequenceSerializer,
    SequenceMessageSerializer,
    CampaignLeadSerializer,
    CampaignEmailSerializer,
    EmailAccountDailyLimitSerializer,
    CampaignStatsSerializer,
    LeadCategorySerializer,
    EmailAccountSerializer
)
from .services import CampaignManager, EmailAccountSelector
from apps.leads.models import LeadCategory
from apps.email.models import EmailAccount


@login_required
def dashboard(request):
    """Dashboard view for email campaigns."""
    # Get campaign statistics
    user_campaigns = Campaign.objects.filter(user=request.user)
    
    stats = {
        'total_campaigns': user_campaigns.count(),
        'active_campaigns': user_campaigns.filter(status='active').count(),
        'total_leads': CampaignLead.objects.filter(campaign__user=request.user).count(),
        'total_emails_sent': CampaignEmail.objects.filter(
            campaign__user=request.user, status='sent'
        ).count(),
        'emails_sent_today': CampaignEmail.objects.filter(
            campaign__user=request.user,
            status='sent',
            sent_time__date=timezone.now().date()
        ).count(),
    }
    
    # Get recent campaigns
    recent_campaigns = user_campaigns.order_by('-date_created')[:5]
    
    # Get available email accounts
    available_accounts = EmailAccountSelector.get_accounts_with_capacity()
    
    context = {
        'stats': stats,
        'recent_campaigns': recent_campaigns,
        'available_accounts': len(available_accounts),
        'accounts_at_limit': EmailAccount.objects.filter(
            is_bulk_mail_account=True,
            is_warming_up=False,
            is_active=True
        ).count() - len(available_accounts),
    }
    
    return render(request, 'emailer/dashboard.html', context)


@login_required
def campaign_detail(request, campaign_id):
    """Detail view for a specific campaign."""
    campaign = get_object_or_404(Campaign, id=campaign_id, user=request.user)
    
    # Get campaign statistics
    campaign_stats = {
        'total_leads': campaign.campaign_leads.count(),
        'pending_leads': campaign.campaign_leads.filter(status='pending').count(),
        'in_progress_leads': campaign.campaign_leads.filter(status='in_progress').count(),
        'completed_leads': campaign.campaign_leads.filter(status='completed').count(),
        'total_emails': campaign.campaign_emails.count(),
        'sent_emails': campaign.campaign_emails.filter(status='sent').count(),
        'scheduled_emails': campaign.campaign_emails.filter(status='scheduled').count(),
        'failed_emails': campaign.campaign_emails.filter(status='failed').count(),
    }
    
    context = {
        'campaign': campaign,
        'stats': campaign_stats,
    }
    
    return render(request, 'emailer/campaign_detail.html', context)


# API ViewSets

class EmailTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for managing email templates."""
    serializer_class = EmailTemplateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return EmailTemplate.objects.filter(user=self.request.user)


class CampaignViewSet(viewsets.ModelViewSet):
    """ViewSet for managing campaigns."""
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return Campaign.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        if self.action == 'create':
            return CampaignCreateSerializer
        return CampaignSerializer
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """Start a campaign."""
        campaign = self.get_object()
        
        if campaign.status != 'draft':
            return Response(
                {'error': 'Campaign must be in draft status to start'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        scheduled_count = CampaignManager.start_campaign(campaign)
        
        return Response({
            'message': f'Campaign started successfully. {scheduled_count} emails scheduled.',
            'scheduled_emails': scheduled_count
        })
    
    @action(detail=True, methods=['post'])
    def pause(self, request, pk=None):
        """Pause a campaign."""
        campaign = self.get_object()
        campaign.status = 'paused'
        campaign.save()
        
        return Response({'message': 'Campaign paused successfully'})
    
    @action(detail=True, methods=['post'])
    def resume(self, request, pk=None):
        """Resume a paused campaign."""
        campaign = self.get_object()
        
        if campaign.status != 'paused':
            return Response(
                {'error': 'Campaign must be paused to resume'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        campaign.status = 'active'
        campaign.save()
        
        return Response({'message': 'Campaign resumed successfully'})
    
    @action(detail=True, methods=['post'])
    def add_leads(self, request, pk=None):
        """Add leads to a campaign."""
        campaign = self.get_object()
        
        added_count = CampaignManager.add_leads_to_campaign(campaign)
        
        return Response({
            'message': f'{added_count} leads added to campaign',
            'added_count': added_count
        })
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get campaign statistics."""
        user_campaigns = self.get_queryset()
        
        stats = {
            'total_campaigns': user_campaigns.count(),
            'active_campaigns': user_campaigns.filter(status='active').count(),
            'total_leads': CampaignLead.objects.filter(campaign__user=request.user).count(),
            'total_emails_sent': CampaignEmail.objects.filter(
                campaign__user=request.user, status='sent'
            ).count(),
            'emails_sent_today': CampaignEmail.objects.filter(
                campaign__user=request.user,
                status='sent',
                sent_time__date=timezone.now().date()
            ).count(),
            'available_accounts': len(EmailAccountSelector.get_accounts_with_capacity()),
            'accounts_at_limit': EmailAccount.objects.filter(
                is_bulk_mail_account=True,
                is_warming_up=False,
                is_active=True
            ).count() - len(EmailAccountSelector.get_accounts_with_capacity()),
        }
        
        serializer = CampaignStatsSerializer(stats)
        return Response(serializer.data)


class EmailSequenceViewSet(viewsets.ModelViewSet):
    """ViewSet for managing email sequences."""
    serializer_class = EmailSequenceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return EmailSequence.objects.filter(campaign__user=self.request.user)


class SequenceMessageViewSet(viewsets.ModelViewSet):
    """ViewSet for managing sequence messages."""
    serializer_class = SequenceMessageSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return SequenceMessage.objects.filter(sequence__campaign__user=self.request.user)


class CampaignLeadViewSet(viewsets.ModelViewSet):
    """ViewSet for managing campaign leads."""
    serializer_class = CampaignLeadSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return CampaignLead.objects.filter(campaign__user=self.request.user)


class CampaignEmailViewSet(viewsets.ModelViewSet):
    """ViewSet for managing campaign emails."""
    serializer_class = CampaignEmailSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return CampaignEmail.objects.filter(campaign__user=self.request.user)


class EmailAccountDailyLimitViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing email account daily limits."""
    serializer_class = EmailAccountDailyLimitSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return EmailAccountDailyLimit.objects.filter(
            email_account__user=self.request.user
        ).select_related('email_account')


# Utility API views

@login_required
def api_lead_categories(request):
    """API endpoint to get available lead categories."""
    categories = LeadCategory.objects.filter(is_active=True).annotate(
        lead_count=Count('lead_lead_category')
    )
    
    serializer = LeadCategorySerializer(categories, many=True)
    return JsonResponse({'categories': serializer.data})


@login_required
def api_email_accounts(request):
    """API endpoint to get available email accounts."""
    accounts = EmailAccount.objects.filter(
        user=request.user,
        is_bulk_mail_account=True,
        is_warming_up=False,
        is_active=True
    )
    
    serializer = EmailAccountSerializer(accounts, many=True)
    return JsonResponse({'accounts': serializer.data})
