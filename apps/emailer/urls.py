from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'emailer'

# API Router
router = DefaultRouter()
router.register(r'templates', views.EmailTemplateViewSet, basename='template')
router.register(r'campaigns', views.CampaignViewSet, basename='campaign')
router.register(r'sequences', views.EmailSequenceViewSet, basename='sequence')
router.register(r'messages', views.SequenceMessageViewSet, basename='message')
router.register(r'campaign-leads', views.CampaignLeadViewSet, basename='campaign-lead')
router.register(r'campaign-emails', views.CampaignEmailViewSet, basename='campaign-email')
router.register(r'daily-limits', views.EmailAccountDailyLimitViewSet, basename='daily-limit')

urlpatterns = [
    # Web views
    path('', views.dashboard, name='dashboard'),

    # Campaign management
    path('campaign/create/', views.create_campaign, name='create_campaign'),
    path('campaign/<int:campaign_id>/', views.campaign_detail, name='campaign_detail'),
    path('campaign/<int:campaign_id>/edit/', views.edit_campaign, name='edit_campaign'),
    path('campaign/<int:campaign_id>/status/', views.campaign_change_status, name='campaign_change_status'),
    path('campaign/<int:campaign_id>/add-leads/', views.add_leads_to_campaign, name='add_leads_to_campaign'),

    # Template management
    path('templates/', views.templates_list, name='templates_list'),
    path('templates/create/', views.create_template, name='create_template'),
    path('templates/<int:template_id>/edit/', views.edit_template, name='edit_template'),
    path('templates/<int:template_id>/delete/', views.delete_template, name='delete_template'),

    # Sequence management
    path('campaign/<int:campaign_id>/sequence/create/', views.create_sequence, name='create_sequence'),
    path('sequence/<int:sequence_id>/', views.sequence_detail, name='sequence_detail'),
    path('sequence/<int:sequence_id>/message/create/', views.create_message, name='create_message'),
    path('message/<int:message_id>/edit/', views.edit_message, name='edit_message'),

    # API endpoints
    path('api/', include(router.urls)),
    path('api/lead-categories/', views.api_lead_categories, name='api_lead_categories'),
    path('api/email-accounts/', views.api_email_accounts, name='api_email_accounts'),
]
