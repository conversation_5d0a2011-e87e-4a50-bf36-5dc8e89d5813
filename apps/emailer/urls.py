from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>
from . import views

app_name = 'emailer'

# API Router
router = DefaultRouter()
router.register(r'templates', views.EmailTemplateViewSet, basename='template')
router.register(r'campaigns', views.CampaignViewSet, basename='campaign')
router.register(r'sequences', views.EmailSequenceViewSet, basename='sequence')
router.register(r'messages', views.SequenceMessageViewSet, basename='message')
router.register(r'campaign-leads', views.CampaignLeadViewSet, basename='campaign-lead')
router.register(r'campaign-emails', views.CampaignEmailViewSet, basename='campaign-email')
router.register(r'daily-limits', views.EmailAccountDailyLimitViewSet, basename='daily-limit')

urlpatterns = [
    # Web views
    path('', views.dashboard, name='dashboard'),
    path('campaign/<int:campaign_id>/', views.campaign_detail, name='campaign_detail'),
    
    # API endpoints
    path('api/', include(router.urls)),
    path('api/lead-categories/', views.api_lead_categories, name='api_lead_categories'),
    path('api/email-accounts/', views.api_email_accounts, name='api_email_accounts'),
]
