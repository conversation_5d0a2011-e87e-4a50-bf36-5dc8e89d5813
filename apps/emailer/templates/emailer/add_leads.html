{% extends "layouts/base.html" %}
{% load static %}

{% block title %}Add Leads to Campaign{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">Add Leads to Campaign</h1>
                    <p class="text-muted mb-0">Campaign: {{ campaign.name }}</p>
                </div>
                <a href="{% url 'emailer:campaign_detail' campaign.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Campaign
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lead Selection</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.lead_category.id_for_label }}" class="form-label">
                                Lead Category <span class="text-danger">*</span>
                            </label>
                            {{ form.lead_category }}
                            {% if form.lead_category.errors %}
                                <div class="text-danger small">{{ form.lead_category.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                {{ form.lead_category.help_text }}
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <div class="form-check">
                                {{ form.exclude_existing }}
                                <label class="form-check-label" for="{{ form.exclude_existing.id_for_label }}">
                                    {{ form.exclude_existing.label }}
                                </label>
                            </div>
                            <div class="form-text">
                                {{ form.exclude_existing.help_text }}
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <div class="form-check">
                                {{ form.exclude_opted_out }}
                                <label class="form-check-label" for="{{ form.exclude_opted_out.id_for_label }}">
                                    {{ form.exclude_opted_out.label }}
                                </label>
                            </div>
                            <div class="form-text">
                                {{ form.exclude_opted_out.help_text }}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'emailer:campaign_detail' campaign.id %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i> Add Leads
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Campaign Stats -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-chart-bar"></i> Current Campaign Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-primary">{{ campaign.campaign_leads.count }}</h5>
                                <small class="text-muted">Current Leads</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-success">{{ campaign.campaign_leads.filter.status='completed'.count }}</h5>
                                <small class="text-muted">Completed</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-warning">{{ campaign.campaign_leads.filter.status='in_progress'.count }}</h5>
                                <small class="text-muted">In Progress</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> About Adding Leads
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">When adding leads to your campaign:</p>
                    <ul class="mb-0">
                        <li><strong>Lead Category:</strong> Only leads from the selected category will be added</li>
                        <li><strong>Duplicate Prevention:</strong> Leads already in this campaign will be skipped</li>
                        <li><strong>Email Validation:</strong> Only leads with valid email addresses will be added</li>
                        <li><strong>Opt-out Respect:</strong> Leads who have opted out will be excluded by default</li>
                        <li><strong>Active Status:</strong> Only active leads will be considered</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on lead category field
    $('#{{ form.lead_category.id_for_label }}').focus();
    
    // Show lead count when category is selected
    $('#{{ form.lead_category.id_for_label }}').change(function() {
        const categoryId = $(this).val();
        if (categoryId) {
            // You could add an AJAX call here to show how many leads would be added
            // from the selected category
        }
    });
});
</script>
{% endblock %}
