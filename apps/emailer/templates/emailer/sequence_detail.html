{% extends "layouts/base.html" %}
{% load static %}

{% block title %}{{ sequence.name }} - Sequence Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">{{ sequence.name }}</h1>
                    <p class="text-muted mb-0">
                        Campaign: <a href="{% url 'emailer:campaign_detail' sequence.campaign.id %}">{{ sequence.campaign.name }}</a>
                    </p>
                </div>
                <div>
                    <a href="{% url 'emailer:create_message' sequence.id %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Message
                    </a>
                    <a href="{% url 'emailer:campaign_detail' sequence.campaign.id %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Campaign
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Sequence Messages</h6>
                    <span class="badge badge-info">{{ messages.count }} message(s)</span>
                </div>
                <div class="card-body">
                    {% if messages %}
                        <div class="timeline">
                            {% for message in messages %}
                            <div class="timeline-item mb-4">
                                <div class="row">
                                    <div class="col-md-1 text-center">
                                        <div class="timeline-marker">
                                            <span class="badge badge-primary badge-lg">{{ message.order }}</span>
                                        </div>
                                        {% if not forloop.last %}
                                        <div class="timeline-line">
                                            <i class="fas fa-arrow-down text-muted"></i>
                                            {% if message.delay_days > 0 %}
                                            <small class="text-muted d-block">
                                                Wait {{ message.delay_days }} day{{ message.delay_days|pluralize }}
                                            </small>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-11">
                                        <div class="card border-left-primary">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="font-weight-bold mb-1">{{ message.name }}</h6>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{% url 'emailer:edit_message' message.id %}" 
                                                           class="btn btn-outline-primary btn-sm">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </a>
                                                        <button type="button" 
                                                                class="btn btn-outline-info btn-sm"
                                                                onclick="previewMessage({{ message.id }})">
                                                            <i class="fas fa-eye"></i> Preview
                                                        </button>
                                                    </div>
                                                </div>
                                                <p class="text-muted mb-2">
                                                    <strong>Subject:</strong> {{ message.subject }}
                                                </p>
                                                <p class="mb-2">
                                                    {{ message.body_text|truncatechars:150 }}
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        {% if message.template %}
                                                        <small class="text-info">
                                                            <i class="fas fa-file-alt"></i> Based on template: {{ message.template.name }}
                                                        </small>
                                                        {% endif %}
                                                    </div>
                                                    <div>
                                                        {% if message.delay_days > 0 %}
                                                        <span class="badge badge-secondary">
                                                            Delay: {{ message.delay_days }} day{{ message.delay_days|pluralize }}
                                                        </span>
                                                        {% else %}
                                                        <span class="badge badge-success">Immediate</span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Messages in Sequence</h5>
                            <p class="text-muted mb-4">
                                Add messages to this sequence to create your email campaign flow.
                            </p>
                            <a href="{% url 'emailer:create_message' sequence.id %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First Message
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Message Preview Modal -->
<div class="modal fade" id="messagePreviewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="messagePreviewContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline-marker {
    margin-bottom: 10px;
}

.timeline-line {
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.badge-lg {
    padding: 8px 12px;
    font-size: 14px;
}

.border-left-primary {
    border-left: 4px solid #4e73df !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function previewMessage(messageId) {
    $('#messagePreviewModal').modal('show');
    
    // Load message content via AJAX
    fetch(`/emailer/api/messages/${messageId}/`)
        .then(response => response.json())
        .then(data => {
            const content = `
                <div class="mb-3">
                    <h6><strong>Message Name:</strong></h6>
                    <p class="border p-2 bg-light">${data.name}</p>
                </div>
                <div class="mb-3">
                    <h6><strong>Subject:</strong></h6>
                    <p class="border p-2 bg-light">${data.subject}</p>
                </div>
                <div class="mb-3">
                    <h6><strong>Delay:</strong></h6>
                    <p class="border p-2 bg-light">${data.delay_days} day(s)</p>
                </div>
                <div class="mb-3">
                    <h6><strong>Plain Text Version:</strong></h6>
                    <pre class="border p-2 bg-light" style="white-space: pre-wrap;">${data.body_text}</pre>
                </div>
                <div class="mb-3">
                    <h6><strong>HTML Version:</strong></h6>
                    <div class="border p-2">
                        ${data.body_html}
                    </div>
                </div>
            `;
            document.getElementById('messagePreviewContent').innerHTML = content;
        })
        .catch(error => {
            document.getElementById('messagePreviewContent').innerHTML = 
                '<div class="alert alert-danger">Error loading message preview.</div>';
        });
}
</script>
{% endblock %}
