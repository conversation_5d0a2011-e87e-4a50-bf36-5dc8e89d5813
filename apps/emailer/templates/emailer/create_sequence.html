{% extends "layouts/base.html" %}
{% load static %}

{% block title %}Create Email Sequence{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">Create Email Sequence</h1>
                    <p class="text-muted mb-0">Campaign: {{ campaign.name }}</p>
                </div>
                <a href="{% url 'emailer:campaign_detail' campaign.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Campaign
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Sequence Details</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                Sequence Name <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                Description
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-4">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active Sequence
                                </label>
                            </div>
                            <div class="form-text">
                                Only active sequences will be used when starting campaigns.
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'emailer:campaign_detail' campaign.id %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Sequence
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> About Email Sequences
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">Email sequences allow you to send multiple emails to leads over time:</p>
                    <ul class="mb-0">
                        <li><strong>Multiple Messages:</strong> Create a series of emails that will be sent in order</li>
                        <li><strong>Delays:</strong> Set delays between emails (e.g., send second email 3 days after first)</li>
                        <li><strong>Templates:</strong> Use existing templates as starting points for messages</li>
                        <li><strong>Tracking:</strong> Track which emails each lead has received</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on sequence name field
    $('#{{ form.name.id_for_label }}').focus();
});
</script>
{% endblock %}
