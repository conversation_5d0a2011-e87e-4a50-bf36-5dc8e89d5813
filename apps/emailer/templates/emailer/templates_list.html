{% extends "layouts/base.html" %}
{% load static %}

{% block title %}Email Templates{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Email Templates</h1>
                <div>
                    <a href="{% url 'emailer:create_template' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Template
                    </a>
                    <a href="{% url 'emailer:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Your Email Templates</h6>
                </div>
                <div class="card-body">
                    {% if templates %}
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for template in templates %}
                                    <tr>
                                        <td>
                                            <strong>{{ template.name }}</strong>
                                        </td>
                                        <td>{{ template.subject|truncatechars:50 }}</td>
                                        <td>
                                            {% if template.is_active %}
                                                <span class="badge badge-success">Active</span>
                                            {% else %}
                                                <span class="badge badge-secondary">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ template.date_created|date:"M d, Y" }}</td>
                                        <td>{{ template.date_updated|date:"M d, Y" }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'emailer:edit_template' template.id %}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="Edit Template">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-info" 
                                                        onclick="previewTemplate({{ template.id }})"
                                                        title="Preview Template">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <a href="{% url 'emailer:delete_template' template.id %}" 
                                                   class="btn btn-sm btn-outline-danger" 
                                                   title="Delete Template"
                                                   onclick="return confirm('Are you sure you want to delete this template?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Email Templates Yet</h5>
                            <p class="text-muted mb-4">
                                Create reusable email templates to speed up your campaign creation process.
                            </p>
                            <a href="{% url 'emailer:create_template' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Your First Template
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="templatePreviewContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function previewTemplate(templateId) {
    $('#templatePreviewModal').modal('show');
    
    // Load template content via AJAX
    fetch(`/emailer/api/templates/${templateId}/`)
        .then(response => response.json())
        .then(data => {
            const content = `
                <div class="mb-3">
                    <h6><strong>Subject:</strong></h6>
                    <p class="border p-2 bg-light">${data.subject}</p>
                </div>
                <div class="mb-3">
                    <h6><strong>Plain Text Version:</strong></h6>
                    <pre class="border p-2 bg-light" style="white-space: pre-wrap;">${data.body_text}</pre>
                </div>
                <div class="mb-3">
                    <h6><strong>HTML Version:</strong></h6>
                    <div class="border p-2">
                        ${data.body_html}
                    </div>
                </div>
            `;
            document.getElementById('templatePreviewContent').innerHTML = content;
        })
        .catch(error => {
            document.getElementById('templatePreviewContent').innerHTML = 
                '<div class="alert alert-danger">Error loading template preview.</div>';
        });
}
</script>
{% endblock %}
