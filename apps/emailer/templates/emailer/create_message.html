{% extends "layouts/base.html" %}
{% load static %}

{% block title %}Create Message{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">Create Message</h1>
                    <p class="text-muted mb-0">
                        Sequence: <a href="{% url 'emailer:sequence_detail' sequence.id %}">{{ sequence.name }}</a>
                    </p>
                </div>
                <a href="{% url 'emailer:sequence_detail' sequence.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Sequence
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Message Details</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        Message Name <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="{{ form.order.id_for_label }}" class="form-label">
                                        Order <span class="text-danger">*</span>
                                    </label>
                                    {{ form.order }}
                                    {% if form.order.errors %}
                                        <div class="text-danger small">{{ form.order.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="{{ form.delay_days.id_for_label }}" class="form-label">
                                        Delay (Days) <span class="text-danger">*</span>
                                    </label>
                                    {{ form.delay_days }}
                                    {% if form.delay_days.errors %}
                                        <div class="text-danger small">{{ form.delay_days.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.template.id_for_label }}" class="form-label">
                                Use Template (Optional)
                            </label>
                            {{ form.template }}
                            {% if form.template.errors %}
                                <div class="text-danger small">{{ form.template.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                Select a template to use as a starting point. This will populate the subject and body fields.
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.subject.id_for_label }}" class="form-label">
                                Email Subject <span class="text-danger">*</span>
                            </label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <div class="text-danger small">{{ form.subject.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.body_text.id_for_label }}" class="form-label">
                                Plain Text Version <span class="text-danger">*</span>
                            </label>
                            {{ form.body_text }}
                            {% if form.body_text.errors %}
                                <div class="text-danger small">{{ form.body_text.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                This version will be used for email clients that don't support HTML.
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.body_html.id_for_label }}" class="form-label">
                                HTML Version <span class="text-danger">*</span>
                            </label>
                            {{ form.body_html }}
                            {% if form.body_html.errors %}
                                <div class="text-danger small">{{ form.body_html.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                Use HTML tags to format your email. You can use a WYSIWYG editor for easier editing.
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active Message
                                </label>
                            </div>
                            <div class="form-text">
                                Only active messages will be sent in campaigns.
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'emailer:sequence_detail' sequence.id %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-info me-2" onclick="previewMessage()">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Message
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on message name field
    $('#{{ form.name.id_for_label }}').focus();
    
    // Load template content when template is selected
    $('#{{ form.template.id_for_label }}').change(function() {
        const templateId = $(this).val();
        if (templateId) {
            fetch(`/emailer/api/templates/${templateId}/`)
                .then(response => response.json())
                .then(data => {
                    $('#{{ form.subject.id_for_label }}').val(data.subject);
                    $('#{{ form.body_text.id_for_label }}').val(data.body_text);
                    $('#{{ form.body_html.id_for_label }}').val(data.body_html);
                })
                .catch(error => {
                    console.error('Error loading template:', error);
                });
        }
    });
    
    // Auto-generate HTML from plain text if HTML is empty
    $('#{{ form.body_text.id_for_label }}').on('blur', function() {
        const htmlField = $('#{{ form.body_html.id_for_label }}');
        if (!htmlField.val().trim()) {
            const plainText = $(this).val();
            const htmlContent = plainText.replace(/\n/g, '<br>');
            htmlField.val(`<p>${htmlContent}</p>`);
        }
    });
});

function previewMessage() {
    const name = $('#{{ form.name.id_for_label }}').val();
    const subject = $('#{{ form.subject.id_for_label }}').val();
    const bodyText = $('#{{ form.body_text.id_for_label }}').val();
    const bodyHtml = $('#{{ form.body_html.id_for_label }}').val();
    const delayDays = $('#{{ form.delay_days.id_for_label }}').val();
    
    if (!name || !subject || !bodyText || !bodyHtml) {
        alert('Please fill in all required fields before previewing.');
        return;
    }
    
    const content = `
        <div class="mb-3">
            <h6><strong>Message Name:</strong></h6>
            <p class="border p-2 bg-light">${name}</p>
        </div>
        <div class="mb-3">
            <h6><strong>Subject:</strong></h6>
            <p class="border p-2 bg-light">${subject}</p>
        </div>
        <div class="mb-3">
            <h6><strong>Delay:</strong></h6>
            <p class="border p-2 bg-light">${delayDays} day(s)</p>
        </div>
        <div class="mb-3">
            <h6><strong>Plain Text Version:</strong></h6>
            <pre class="border p-2 bg-light" style="white-space: pre-wrap;">${bodyText}</pre>
        </div>
        <div class="mb-3">
            <h6><strong>HTML Version:</strong></h6>
            <div class="border p-2">
                ${bodyHtml}
            </div>
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = content;
    $('#previewModal').modal('show');
}
</script>
{% endblock %}
