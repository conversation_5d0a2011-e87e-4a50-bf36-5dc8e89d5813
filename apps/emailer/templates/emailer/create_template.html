{% extends "layouts/base.html" %}
{% load static %}

{% block title %}Create Email Template{% endblock %}

{% block extra_css %}
<!-- Add TinyMCE or other WYSIWYG editor CSS here -->
<style>
.form-label {
    font-weight: 600;
}
.required {
    color: #dc3545;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Create Email Template</h1>
                <a href="{% url 'emailer:templates_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Templates
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Template Details</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        Template Name <span class="required">*</span>
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="form-check">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            Active Template
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.subject.id_for_label }}" class="form-label">
                                Email Subject <span class="required">*</span>
                            </label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <div class="text-danger small">{{ form.subject.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.body_text.id_for_label }}" class="form-label">
                                Plain Text Version <span class="required">*</span>
                            </label>
                            {{ form.body_text }}
                            {% if form.body_text.errors %}
                                <div class="text-danger small">{{ form.body_text.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                This version will be used for email clients that don't support HTML.
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <label for="{{ form.body_html.id_for_label }}" class="form-label">
                                HTML Version <span class="required">*</span>
                            </label>
                            {{ form.body_html }}
                            {% if form.body_html.errors %}
                                <div class="text-danger small">{{ form.body_html.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                Use HTML tags to format your email. You can use a WYSIWYG editor for easier editing.
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'emailer:templates_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-info me-2" onclick="previewTemplate()">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Template
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add TinyMCE or other WYSIWYG editor JS here -->
<script>
$(document).ready(function() {
    // Auto-focus on template name field
    $('#{{ form.name.id_for_label }}').focus();
    
    // Initialize WYSIWYG editor for HTML field (optional)
    // You can add TinyMCE, CKEditor, or similar here
    
    // Auto-generate HTML from plain text if HTML is empty
    $('#{{ form.body_text.id_for_label }}').on('blur', function() {
        const htmlField = $('#{{ form.body_html.id_for_label }}');
        if (!htmlField.val().trim()) {
            const plainText = $(this).val();
            const htmlContent = plainText.replace(/\n/g, '<br>');
            htmlField.val(`<p>${htmlContent}</p>`);
        }
    });
});

function previewTemplate() {
    const subject = $('#{{ form.subject.id_for_label }}').val();
    const bodyText = $('#{{ form.body_text.id_for_label }}').val();
    const bodyHtml = $('#{{ form.body_html.id_for_label }}').val();
    
    if (!subject || !bodyText || !bodyHtml) {
        alert('Please fill in all required fields before previewing.');
        return;
    }
    
    const content = `
        <div class="mb-3">
            <h6><strong>Subject:</strong></h6>
            <p class="border p-2 bg-light">${subject}</p>
        </div>
        <div class="mb-3">
            <h6><strong>Plain Text Version:</strong></h6>
            <pre class="border p-2 bg-light" style="white-space: pre-wrap;">${bodyText}</pre>
        </div>
        <div class="mb-3">
            <h6><strong>HTML Version:</strong></h6>
            <div class="border p-2">
                ${bodyHtml}
            </div>
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = content;
    $('#previewModal').modal('show');
}
</script>
{% endblock %}
