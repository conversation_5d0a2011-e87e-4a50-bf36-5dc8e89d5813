{% extends "layouts/base.html" %}
{% load static %}

{% block title %}Create Campaign{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Create New Campaign</h1>
                <a href="{% url 'emailer:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campaign Details</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                Campaign Name <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                Description
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-3">
                            <label for="{{ form.target_lead_category.id_for_label }}" class="form-label">
                                Target Lead Category <span class="text-danger">*</span>
                            </label>
                            {{ form.target_lead_category }}
                            {% if form.target_lead_category.errors %}
                                <div class="text-danger small">{{ form.target_lead_category.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                Leads from this category will be targeted by this campaign.
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <div class="form-check">
                                {{ form.auto_add_leads }}
                                <label class="form-check-label" for="{{ form.auto_add_leads.id_for_label }}">
                                    {{ form.auto_add_leads.label }}
                                </label>
                            </div>
                            <div class="form-text">
                                {{ form.auto_add_leads.help_text }}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'emailer:dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Campaign
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Next Steps
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">After creating your campaign, you'll be able to:</p>
                    <ul class="mb-0">
                        <li>Create email sequences with multiple messages</li>
                        <li>Set delays between emails in the sequence</li>
                        <li>Use templates to speed up email creation</li>
                        <li>Add more leads to the campaign</li>
                        <li>Start the campaign to begin sending emails</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on campaign name field
    $('#{{ form.name.id_for_label }}').focus();
    
    // Show/hide auto-add leads help text based on selection
    $('#{{ form.auto_add_leads.id_for_label }}').change(function() {
        if ($(this).is(':checked')) {
            $(this).next('label').addClass('text-success');
        } else {
            $(this).next('label').removeClass('text-success');
        }
    });
});
</script>
{% endblock %}
