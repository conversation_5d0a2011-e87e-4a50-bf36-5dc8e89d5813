from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta

from apps.email.models import EmailAccount
from apps.leads.models import Lead, LeadCategory
from .models import (
    EmailTemplate,
    Campaign,
    EmailSequence,
    SequenceMessage,
    CampaignLead,
    CampaignEmail,
    EmailAccountDailyLimit
)
from .services import (
    EmailAccountSelector,
    EmailSender,
    CampaignManager
)

User = get_user_model()


class EmailerModelsTestCase(TestCase):
    """Test cases for emailer models."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.lead_category = LeadCategory.objects.create(
            name='Test Category',
            description='Test category for leads'
        )
        
        self.email_account = EmailAccount.objects.create(
            user=self.user,
            account_name='Test Account',
            email_address='<EMAIL>',
            smtp_server='smtp.example.com',
            smtp_port=587,
            smtp_password='password',
            is_bulk_mail_account=True,
            is_warming_up=False,
            is_active=True
        )
        
        self.lead = Lead.objects.create(
            user=self.user,
            lead_category=self.lead_category,
            first_name='John',
            last_name='Doe',
            email='<EMAIL>'
        )
    
    def test_email_template_creation(self):
        """Test creating an email template."""
        template = EmailTemplate.objects.create(
            user=self.user,
            name='Test Template',
            subject='Test Subject',
            body_text='Test body text',
            body_html='<p>Test body HTML</p>'
        )
        
        self.assertEqual(str(template), 'Test Template')
        self.assertTrue(template.is_active)
    
    def test_campaign_creation(self):
        """Test creating a campaign."""
        campaign = Campaign.objects.create(
            user=self.user,
            name='Test Campaign',
            description='Test campaign description',
            target_lead_category=self.lead_category
        )
        
        self.assertEqual(str(campaign), 'Test Campaign')
        self.assertEqual(campaign.status, 'draft')
        self.assertTrue(campaign.is_active)
    
    def test_email_sequence_creation(self):
        """Test creating an email sequence."""
        campaign = Campaign.objects.create(
            user=self.user,
            name='Test Campaign',
            target_lead_category=self.lead_category
        )
        
        sequence = EmailSequence.objects.create(
            campaign=campaign,
            name='Test Sequence',
            description='Test sequence description'
        )
        
        self.assertEqual(str(sequence), 'Test Campaign - Test Sequence')
        self.assertTrue(sequence.is_active)
    
    def test_sequence_message_creation(self):
        """Test creating a sequence message."""
        campaign = Campaign.objects.create(
            user=self.user,
            name='Test Campaign',
            target_lead_category=self.lead_category
        )
        
        sequence = EmailSequence.objects.create(
            campaign=campaign,
            name='Test Sequence'
        )
        
        message = SequenceMessage.objects.create(
            sequence=sequence,
            name='Test Message',
            subject='Test Subject',
            body_text='Test body',
            body_html='<p>Test body</p>',
            delay_days=0,
            order=1
        )
        
        self.assertEqual(str(message), 'Test Sequence - Test Message')
        self.assertEqual(message.delay_days, 0)
        self.assertEqual(message.order, 1)
    
    def test_campaign_lead_creation(self):
        """Test creating a campaign lead."""
        campaign = Campaign.objects.create(
            user=self.user,
            name='Test Campaign',
            target_lead_category=self.lead_category
        )
        
        campaign_lead = CampaignLead.objects.create(
            campaign=campaign,
            lead=self.lead
        )
        
        self.assertEqual(str(campaign_lead), 'Test Campaign - John Doe')
        self.assertEqual(campaign_lead.status, 'pending')
        self.assertEqual(campaign_lead.current_sequence_position, 0)
    
    def test_email_account_daily_limit(self):
        """Test email account daily limit functionality."""
        today = timezone.now().date()
        
        daily_limit = EmailAccountDailyLimit.objects.create(
            email_account=self.email_account,
            date=today,
            emails_sent=10
        )
        
        self.assertTrue(daily_limit.has_capacity)
        self.assertEqual(daily_limit.remaining_capacity, 30)
        
        # Test at limit
        daily_limit.emails_sent = 40
        daily_limit.save()
        
        self.assertFalse(daily_limit.has_capacity)
        self.assertEqual(daily_limit.remaining_capacity, 0)


class EmailAccountSelectorTestCase(TestCase):
    """Test cases for EmailAccountSelector service."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create available account
        self.available_account = EmailAccount.objects.create(
            user=self.user,
            account_name='Available Account',
            email_address='<EMAIL>',
            smtp_server='smtp.example.com',
            smtp_port=587,
            smtp_password='password',
            is_bulk_mail_account=True,
            is_warming_up=False,
            is_active=True
        )
        
        # Create unavailable account (warming up)
        self.warming_account = EmailAccount.objects.create(
            user=self.user,
            account_name='Warming Account',
            email_address='<EMAIL>',
            smtp_server='smtp.example.com',
            smtp_port=587,
            smtp_password='password',
            is_bulk_mail_account=True,
            is_warming_up=True,
            is_active=True
        )
    
    def test_get_available_accounts(self):
        """Test getting available email accounts."""
        accounts = EmailAccountSelector.get_available_accounts()
        
        self.assertEqual(accounts.count(), 1)
        self.assertEqual(accounts.first(), self.available_account)
    
    def test_get_accounts_with_capacity(self):
        """Test getting accounts with capacity."""
        accounts_with_capacity = EmailAccountSelector.get_accounts_with_capacity()
        
        self.assertEqual(len(accounts_with_capacity), 1)
        account, capacity = accounts_with_capacity[0]
        self.assertEqual(account, self.available_account)
        self.assertEqual(capacity, 40)
    
    def test_select_random_account_with_capacity(self):
        """Test selecting a random account with capacity."""
        account = EmailAccountSelector.select_random_account_with_capacity()
        
        self.assertEqual(account, self.available_account)
    
    def test_no_accounts_with_capacity(self):
        """Test when no accounts have capacity."""
        # Set account to limit
        today = timezone.now().date()
        EmailAccountDailyLimit.objects.create(
            email_account=self.available_account,
            date=today,
            emails_sent=40
        )
        
        account = EmailAccountSelector.select_random_account_with_capacity()
        self.assertIsNone(account)


class CampaignManagerTestCase(TestCase):
    """Test cases for CampaignManager service."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.lead_category = LeadCategory.objects.create(
            name='Test Category',
            description='Test category for leads'
        )
        
        self.email_account = EmailAccount.objects.create(
            user=self.user,
            account_name='Test Account',
            email_address='<EMAIL>',
            smtp_server='smtp.example.com',
            smtp_port=587,
            smtp_password='password',
            is_bulk_mail_account=True,
            is_warming_up=False,
            is_active=True
        )
        
        self.lead = Lead.objects.create(
            user=self.user,
            lead_category=self.lead_category,
            first_name='John',
            last_name='Doe',
            email='<EMAIL>'
        )
        
        self.campaign = Campaign.objects.create(
            user=self.user,
            name='Test Campaign',
            target_lead_category=self.lead_category
        )
        
        self.sequence = EmailSequence.objects.create(
            campaign=self.campaign,
            name='Test Sequence'
        )
        
        self.message = SequenceMessage.objects.create(
            sequence=self.sequence,
            name='Test Message',
            subject='Test Subject',
            body_text='Test body',
            body_html='<p>Test body</p>',
            order=1
        )
        
        self.campaign_lead = CampaignLead.objects.create(
            campaign=self.campaign,
            lead=self.lead
        )
    
    def test_add_leads_to_campaign(self):
        """Test adding leads to a campaign."""
        # Create another lead
        Lead.objects.create(
            user=self.user,
            lead_category=self.lead_category,
            first_name='Jane',
            last_name='Smith',
            email='<EMAIL>'
        )
        
        # Remove existing campaign lead
        self.campaign_lead.delete()
        
        added_count = CampaignManager.add_leads_to_campaign(self.campaign)
        
        self.assertEqual(added_count, 2)
        self.assertEqual(self.campaign.campaign_leads.count(), 2)
    
    def test_start_campaign(self):
        """Test starting a campaign."""
        scheduled_count = CampaignManager.start_campaign(self.campaign)
        
        self.assertEqual(scheduled_count, 1)
        self.campaign.refresh_from_db()
        self.assertEqual(self.campaign.status, 'active')
        self.assertIsNotNone(self.campaign.start_date)
        
        # Check that email was scheduled
        self.assertEqual(CampaignEmail.objects.count(), 1)
        
        # Check that lead status was updated
        self.campaign_lead.refresh_from_db()
        self.assertEqual(self.campaign_lead.status, 'in_progress')
