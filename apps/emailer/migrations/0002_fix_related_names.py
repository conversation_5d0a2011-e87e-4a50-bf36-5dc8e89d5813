# Migration to fix related names

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('emailer', '0001_initial'),
    ]

    operations = [
        # Fix EmailSequence related name
        migrations.AlterField(
            model_name='emailsequence',
            name='campaign',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='sequences',
                to='emailer.campaign'
            ),
        ),
        
        # Fix CampaignLead related name
        migrations.AlterField(
            model_name='campaignlead',
            name='campaign',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='campaign_leads',
                to='emailer.campaign'
            ),
        ),
        
        # Fix CampaignEmail related name for campaign
        migrations.AlterField(
            model_name='campaignemail',
            name='campaign',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='campaign_emails',
                to='emailer.campaign'
            ),
        ),
        
        # Fix CampaignEmail related name for campaign_lead
        migrations.AlterField(
            model_name='campaignemail',
            name='campaign_lead',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='emails',
                to='emailer.campaignlead'
            ),
        ),
        
        # Fix CampaignEmail related name for email_account
        migrations.AlterField(
            model_name='campaignemail',
            name='email_account',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='emailer_sent',
                to='email.emailaccount'
            ),
        ),
        
        # Fix SequenceMessage related name for sequence
        migrations.AlterField(
            model_name='sequencemessage',
            name='sequence',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='messages',
                to='emailer.emailsequence'
            ),
        ),
        
        # Fix SequenceMessage related name for template
        migrations.AlterField(
            model_name='sequencemessage',
            name='template',
            field=models.ForeignKey(
                blank=True,
                help_text='Template to use as starting point for this message.',
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='sequence_messages',
                to='emailer.emailtemplate'
            ),
        ),
        
        # Fix EmailTemplate related name for user
        migrations.AlterField(
            model_name='emailtemplate',
            name='user',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='email_templates',
                to='authentication.user'
            ),
        ),
        
        # Fix Campaign related name for user
        migrations.AlterField(
            model_name='campaign',
            name='user',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='emailer_campaigns',
                to='authentication.user'
            ),
        ),
        
        # Fix Campaign related name for target_lead_category
        migrations.AlterField(
            model_name='campaign',
            name='target_lead_category',
            field=models.ForeignKey(
                help_text='Leads from this category will be targeted by this campaign.',
                on_delete=django.db.models.deletion.CASCADE,
                related_name='emailer_campaigns',
                to='leads.leadcategory'
            ),
        ),
        
        # Fix EmailAccountDailyLimit related name
        migrations.AlterField(
            model_name='emailaccountdailylimit',
            name='email_account',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='emailer_daily_limits',
                to='email.emailaccount'
            ),
        ),
    ]
