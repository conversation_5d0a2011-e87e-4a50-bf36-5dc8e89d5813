# Generated by Django 5.1.3 on 2025-06-21 05:12

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("email", "0005_emailaccount_is_bulk_mail_account_and_more"),
        ("leads", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Campaign",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, verbose_name="Campaign Name"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("active", "Active"),
                            ("paused", "Paused"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "start_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Start Date"
                    ),
                ),
                (
                    "end_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="End Date"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "target_lead_category",
                    models.ForeignKey(
                        help_text="Leads from this category will be targeted by this campaign.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="campaign_target_lead_category",
                        to="leads.leadcategory",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="campaign_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Campaign",
                "verbose_name_plural": "Campaigns",
                "db_table": "emailer_campaign",
            },
        ),
        migrations.CreateModel(
            name="CampaignLead",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("opted_out", "Opted Out"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "current_sequence_position",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Current Sequence Position"
                    ),
                ),
                ("date_added", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "date_completed",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Date Completed"
                    ),
                ),
                (
                    "campaign",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="campaign_lead_campaign",
                        to="emailer.campaign",
                    ),
                ),
                (
                    "lead",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="emailer_campaign_lead",
                        to="leads.lead",
                    ),
                ),
            ],
            options={
                "verbose_name": "Campaign Lead",
                "verbose_name_plural": "Campaign Leads",
                "db_table": "emailer_campaign_lead",
                "unique_together": {("campaign", "lead")},
            },
        ),
        migrations.CreateModel(
            name="EmailSequence",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, verbose_name="Sequence Name"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "campaign",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_sequence_campaign",
                        to="emailer.campaign",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Sequence",
                "verbose_name_plural": "Email Sequences",
                "db_table": "emailer_sequence",
            },
        ),
        migrations.CreateModel(
            name="EmailTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, verbose_name="Template Name"),
                ),
                ("subject", models.CharField(max_length=255, verbose_name="Subject")),
                ("body_text", models.TextField(verbose_name="Body Text")),
                ("body_html", models.TextField(verbose_name="Body HTML")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_template_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Template",
                "verbose_name_plural": "Email Templates",
                "db_table": "emailer_template",
            },
        ),
        migrations.CreateModel(
            name="SequenceMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Message Name")),
                ("subject", models.CharField(max_length=255, verbose_name="Subject")),
                ("body_text", models.TextField(verbose_name="Body Text")),
                ("body_html", models.TextField(verbose_name="Body HTML")),
                (
                    "delay_days",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of days to wait before sending this message after the previous one.",
                        verbose_name="Delay (days)",
                    ),
                ),
                ("order", models.PositiveIntegerField(default=1, verbose_name="Order")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "sequence",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sequence_message_sequence",
                        to="emailer.emailsequence",
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        blank=True,
                        help_text="Template to use as starting point for this message.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="sequence_message_template",
                        to="emailer.emailtemplate",
                    ),
                ),
            ],
            options={
                "verbose_name": "Sequence Message",
                "verbose_name_plural": "Sequence Messages",
                "db_table": "emailer_sequence_message",
                "ordering": ["order"],
            },
        ),
        migrations.CreateModel(
            name="CampaignEmail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("subject", models.CharField(max_length=255, verbose_name="Subject")),
                ("body_text", models.TextField(verbose_name="Body Text")),
                ("body_html", models.TextField(verbose_name="Body HTML")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("scheduled", "Scheduled"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("bounced", "Bounced"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                ("scheduled_time", models.DateTimeField(verbose_name="Scheduled Time")),
                (
                    "sent_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Sent Time"
                    ),
                ),
                (
                    "message_id",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Message ID"
                    ),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_updated", models.DateTimeField(auto_now=True)),
                (
                    "campaign",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="campaign_email_campaign",
                        to="emailer.campaign",
                    ),
                ),
                (
                    "email_account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="campaign_email_email_account",
                        to="email.emailaccount",
                    ),
                ),
                (
                    "campaign_lead",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="campaign_email_campaign_lead",
                        to="emailer.campaignlead",
                    ),
                ),
                (
                    "sequence_message",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_emails",
                        to="emailer.sequencemessage",
                    ),
                ),
            ],
            options={
                "verbose_name": "Campaign Email",
                "verbose_name_plural": "Campaign Emails",
                "db_table": "emailer_campaign_email",
            },
        ),
        migrations.CreateModel(
            name="EmailAccountDailyLimit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(verbose_name="Date")),
                (
                    "emails_sent",
                    models.PositiveIntegerField(default=0, verbose_name="Emails Sent"),
                ),
                (
                    "daily_limit",
                    models.PositiveIntegerField(default=40, verbose_name="Daily Limit"),
                ),
                (
                    "email_account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="emailer_daily_limit_email_account",
                        to="email.emailaccount",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Account Daily Limit",
                "verbose_name_plural": "Email Account Daily Limits",
                "db_table": "emailer_email_account_daily_limit",
                "unique_together": {("email_account", "date")},
            },
        ),
    ]
