# Emailer App

This Django app allows users to create and manage email campaigns that send bulk emails to leads.

## Features

- **Lead Model Integration**: Uses `apps.leads.models.Lead` as recipients and targets specific `Lead.lead_category`
- **Email Sending Accounts**: Uses `apps.email.models.EmailAccount` with filtering for bulk mail accounts
- **Daily Limits**: Each EmailAccount limited to 40 emails per day with automatic tracking
- **Email Sequences**: Support for multi-email sequences with configurable delays
- **Email Templates**: Reusable email templates with both plain text and HTML versions
- **Campaign Management**: Full campaign lifecycle management (draft, active, paused, completed)
- **Duplicate Prevention**: Tracks which leads have received which campaign messages
- **WYSIWYG Support**: HTML email composition support

## Requirements

### Lead Model Integration
- Uses `apps.leads.models.Lead` as the model representing recipients
- Campaigns target a specific `Lead.lead_category`
- Leads assigned to campaigns are tracked to prevent duplicate sends

### Email Sending Accounts
- Uses `apps.email.models.EmailAccount` for sending
- Only uses accounts where:
  - `is_bulk_mail_account == True`
  - `is_warming_up == False`
  - `is_active == True`
- Each EmailAccount limited to 40 emails per day
- Tracks which account sends which emails
- Campaign pauses if all accounts hit daily limit

### Email Sequences
- Each campaign consists of a sequence of emails
- Every email contains both plain text and HTML versions
- Support for delays between emails (e.g., second email 5 days after first)
- Tracks which email(s) each lead has received in the sequence

### Email Templates
- Users can create and manage reusable email templates
- Each template includes both plain text and HTML versions
- Campaign emails can be created using templates as starting point

## Models

### EmailTemplate
- Stores reusable email templates
- Fields: name, subject, body_text, body_html, user, is_active

### Campaign
- Stores campaign details
- Fields: name, description, target_lead_category, status, start_date, end_date

### EmailSequence
- Stores email sequences within campaigns
- Fields: campaign, name, description, is_active

### SequenceMessage
- Stores individual messages within sequences
- Fields: sequence, template, name, subject, body_text, body_html, delay_days, order

### CampaignLead
- Tracks leads in campaigns and prevents duplicates
- Fields: campaign, lead, status, current_sequence_position

### CampaignEmail
- Tracks emails sent in campaigns
- Fields: campaign, campaign_lead, sequence_message, email_account, subject, body_text, body_html, status, scheduled_time, sent_time

### EmailAccountDailyLimit
- Tracks daily email sending limits
- Fields: email_account, date, emails_sent, daily_limit (default: 40)

## Services

### EmailAccountSelector
- `get_available_accounts()`: Get accounts available for bulk sending
- `get_accounts_with_capacity()`: Get accounts with remaining daily capacity
- `select_random_account_with_capacity()`: Randomly select available account

### EmailSender
- `schedule_email()`: Schedule an email to be sent
- `send_scheduled_emails()`: Send all due scheduled emails

### CampaignManager
- `start_campaign()`: Start a campaign and schedule first emails
- `schedule_follow_up_emails()`: Schedule follow-up emails based on delays
- `add_leads_to_campaign()`: Add leads to campaign based on target category

## Management Commands

### send_scheduled_campaign_emails
Sends scheduled campaign emails and schedules follow-up emails.

```bash
python manage.py send_scheduled_campaign_emails
```

Options:
- `--force`: Force sending emails outside business hours

The command:
1. Checks business hours (M-F 7AM-7PM) unless forced
2. Sends all scheduled emails that are due
3. Respects daily limits for email accounts
4. Schedules follow-up emails based on sequence delays
5. Uses SMTP settings from EmailAccount model

## API Endpoints

### REST API
- `/api/templates/` - Email templates CRUD
- `/api/campaigns/` - Campaigns CRUD with additional actions:
  - `POST /api/campaigns/{id}/start/` - Start campaign
  - `POST /api/campaigns/{id}/pause/` - Pause campaign
  - `POST /api/campaigns/{id}/resume/` - Resume campaign
  - `POST /api/campaigns/{id}/add_leads/` - Add leads to campaign
  - `GET /api/campaigns/stats/` - Get campaign statistics
- `/api/sequences/` - Email sequences CRUD
- `/api/messages/` - Sequence messages CRUD
- `/api/campaign-leads/` - Campaign leads management
- `/api/campaign-emails/` - Campaign emails tracking
- `/api/daily-limits/` - Email account daily limits (read-only)

### Utility APIs
- `/api/lead-categories/` - Get available lead categories
- `/api/email-accounts/` - Get available email accounts

## Web Interface

### Dashboard (`/`)
- Campaign statistics overview
- Recent campaigns list
- Email account capacity status

### Campaign Detail (`/campaign/{id}/`)
- Detailed campaign statistics
- Lead and email tracking
- Campaign management actions

## Usage

### Creating a Campaign
1. Create email templates (optional)
2. Create a new campaign with target lead category
3. Create email sequences with messages
4. Add leads to campaign (automatic or manual)
5. Start the campaign

### Managing Campaigns
- View campaign statistics on dashboard
- Pause, resume, or stop campaigns
- Monitor email sending and delivery
- Track lead progress through sequences

### Email Sending
- Run the management command periodically (e.g., via cron)
- Emails are sent during business hours only
- Automatic account selection and load balancing
- Daily limits are enforced automatically

## Installation

1. Add `'apps.emailer'` to `INSTALLED_APPS` in settings
2. Run migrations: `python manage.py migrate`
3. Include URLs in main `urls.py`:
   ```python
   path('emailer/', include('apps.emailer.urls')),
   ```
4. Set up periodic task for sending emails:
   ```bash
   # Add to crontab for every 15 minutes during business hours
   */15 7-19 * * 1-5 cd /path/to/project && python manage.py send_scheduled_campaign_emails
   ```

## Testing

Run tests with:
```bash
python manage.py test apps.emailer
```

The test suite covers:
- Model creation and relationships
- Service functionality
- Email account selection logic
- Campaign management operations
- Daily limit enforcement
