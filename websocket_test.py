from channels.generic.websocket import AsyncWebsocketConsumer
import json

class TestConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        print("Client attempting to connect")
        await self.accept()
        await self.send(text_data=json.dumps({
            'message': 'Connected successfully!'
        }))

    async def disconnect(self, close_code):
        print(f"Client disconnected with code: {close_code}")

    async def receive(self, text_data):
        await self.send(text_data=json.dumps({
            'message': f'Received: {text_data}'
        }))